
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trading Bot Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="refresh" content="60">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #2c3e50;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    margin-bottom: 20px;
                    border-radius: 5px;
                }
                .metrics-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                .metric-card {
                    background-color: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 15px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    width: calc(25% - 20px);
                }
                .metric-title {
                    font-size: 14px;
                    color: #7f8c8d;
                    margin-bottom: 5px;
                }
                .metric-value {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                }
                .positive {
                    color: #27ae60;
                }
                .negative {
                    color: #e74c3c;
                }
                .chart-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .chart-card {
                    background-color: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    width: calc(50% - 20px);
                }
                .chart-title {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: #2c3e50;
                }
                .chart-img {
                    width: 100%;
                    height: auto;
                }
                .trades-container {
                    background-color: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                }
                .trades-title {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: #2c3e50;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                }
                th, td {
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f2f2f2;
                }
                .footer {
                    text-align: center;
                    margin-top: 20px;
                    color: #7f8c8d;
                    font-size: 12px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Multi-Terminal Trading Bot Dashboard</h1>
                    <p>Last updated: 2025-06-06 15:23:19</p>
                    <p>Terminal 1: ARIMA | Terminal 2: LSTM | Terminal 3: TFT | Terminal 4: LSTM+ARIMA | Terminal 5: TFT+ARIMA</p>
                </div>

                <div class="metrics-container">
        
                </div>

                <div class="chart-container">
                    <div class="chart-card">
                        <div class="chart-title">Equity Curve</div>
                        <img class="chart-img" src="equity_curve.png" alt="Equity Curve">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Drawdown</div>
                        <img class="chart-img" src="drawdown.png" alt="Drawdown">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Monthly Returns</div>
                        <img class="chart-img" src="monthly_returns.png" alt="Monthly Returns">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Trade Distribution</div>
                        <img class="chart-img" src="trade_distribution.png" alt="Trade Distribution">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Performance Metrics</div>
                        <img class="chart-img" src="performance_metrics.png" alt="Performance Metrics">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Daily Profit</div>
                        <img class="chart-img" src="daily_profit.png" alt="Daily Profit">
                    </div>
                </div>

                <div class="trades-container">
                    <div class="trades-title">Recent Trades</div>
        
                    <p>No trades available</p>
            
                </div>

                <div class="footer">
                    <p>Multi-Terminal Trading Bot Dashboard - Powered by ARIMA, LSTM, TFT, and Ensemble Models</p>
                </div>
            </div>
        </body>
        </html>
        