"""
Helpers Module.
This module provides helper functions for the trading bot.
"""
import logging
import time
import os
import json
import pandas as pd

from typing import List, Any
from datetime import datetime

# Configure logger
logger = logging.getLogger('utils.helpers')

def format_number(number: float, decimals: int = 2) -> str:
    """
    Format a number with the specified number of decimal places.
    
    Args:
        number: Number to format
        decimals: Number of decimal places
    
    Returns:
        str: Formatted number
    """
    return f"{number:.{decimals}f}"

def format_price(price: float, symbol: str = 'BTCUSD') -> str:
    """
    Format a price with the appropriate number of decimal places for the symbol.
    
    Args:
        price: Price to format
        symbol: Symbol
    
    Returns:
        str: Formatted price
    """
    # Define decimal places for different symbols
    decimals = {
        'BTCUSD': 2,
        'ETHUSD': 2,
        'XRPUSD': 4,
        'LTCUSD': 2,
        'BCHUSD': 2,
        'EOSUSD': 4,
        'BNBUSD': 2,
        'ADAUSD': 6,
        'DOTUSD': 4,
        'LINKUSD': 4
    }
    
    # Get decimal places for the symbol
    decimal_places = decimals.get(symbol, 2)
    
    return format_number(price, decimal_places)

def format_volume(volume: float) -> str:
    """
    Format a volume.
    
    Args:
        volume: Volume to format
    
    Returns:
        str: Formatted volume
    """
    return format_number(volume, 2)

def format_timestamp(timestamp: float) -> str:
    """
    Format a timestamp.
    
    Args:
        timestamp: Timestamp to format
    
    Returns:
        str: Formatted timestamp
    """
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

def get_timeframe_seconds(timeframe: str) -> int:
    """
    Get the number of seconds in a timeframe.
    
    Args:
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4', 'D1')
    
    Returns:
        int: Number of seconds
    """
    # Define seconds for different timeframes
    seconds = {
        'M1': 60,
        'M5': 300,
        'M15': 900,
        'M30': 1800,
        'H1': 3600,
        'H4': 14400,
        'D1': 86400,
        'W1': 604800,
        'MN1': 2592000
    }
    
    return seconds.get(timeframe, 0)

def get_timeframe_name(timeframe: str) -> str:
    """
    Get the name of a timeframe.
    
    Args:
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4', 'D1')
    
    Returns:
        str: Timeframe name
    """
    # Define names for different timeframes
    names = {
        'M1': '1 Minute',
        'M5': '5 Minutes',
        'M15': '15 Minutes',
        'M30': '30 Minutes',
        'H1': '1 Hour',
        'H4': '4 Hours',
        'D1': '1 Day',
        'W1': '1 Week',
        'MN1': '1 Month'
    }
    
    return names.get(timeframe, timeframe)

def calculate_pip_value(symbol: str, volume: float) -> float:
    """
    Calculate the pip value for a symbol.
    
    Args:
        symbol: Symbol
        volume: Volume in lots
    
    Returns:
        float: Pip value
    """
    # Define pip values for different symbols (per standard lot)
    pip_values = {
        'BTCUSD': 1.0,  # $1 per pip for 1 lot
        'ETHUSD': 1.0,
        'XRPUSD': 1.0,
        'LTCUSD': 1.0,
        'BCHUSD': 1.0,
        'EOSUSD': 1.0,
        'BNBUSD': 1.0,
        'ADAUSD': 1.0,
        'DOTUSD': 1.0,
        'LINKUSD': 1.0
    }
    
    # Get pip value for the symbol
    pip_value = pip_values.get(symbol, 1.0)
    
    # Calculate pip value for the volume
    return pip_value * volume

def calculate_position_size(
    account_balance: float, 
    risk_percentage: float, 
    stop_loss_pips: float, 
    symbol: str
) -> float:
    """
    Calculate position size based on risk management.
    
    Args:
        account_balance: Account balance
        risk_percentage: Risk percentage (0-100)
        stop_loss_pips: Stop loss in pips
        symbol: Symbol
    
    Returns:
        float: Position size in lots
    """
    # Calculate risk amount
    risk_amount = account_balance * (risk_percentage / 100)
    
    # Calculate pip value for 1 lot
    pip_value = calculate_pip_value(symbol, 1.0)
    
    # Calculate position size
    position_size = risk_amount / (stop_loss_pips * pip_value)
    
    # Round to 2 decimal places
    position_size = round(position_size, 2)
    
    return position_size

def save_json(data: Any, filepath: str):
    """
    Save data to a JSON file.
    
    Args:
        data: Data to save
        filepath: Path to save the data
    """
    try:
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=4)
        logger.info(f"Data saved to {filepath}")
    except Exception as e:
        logger.error(f"Error saving data to {filepath}: {str(e)}")

def load_json(filepath: str) -> Any:
    """
    Load data from a JSON file.
    
    Args:
        filepath: Path to load the data from
    
    Returns:
        Any: Loaded data
    """
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        logger.info(f"Data loaded from {filepath}")
        return data
    except Exception as e:
        logger.error(f"Error loading data from {filepath}: {str(e)}")
        return None

def create_directory(directory: str):
    """
    Create a directory if it doesn't exist.
    
    Args:
        directory: Directory to create
    """
    try:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Directory created: {directory}")
    except Exception as e:
        logger.error(f"Error creating directory {directory}: {str(e)}")

def get_current_time() -> datetime:
    """
    Get the current time.
    
    Returns:
        datetime: Current time
    """
    return datetime.now()

def get_current_timestamp() -> float:
    """
    Get the current timestamp.
    
    Returns:
        float: Current timestamp
    """
    return time.time()

def get_date_range(start_date: str, end_date: str = None) -> List[str]:
    """
    Get a list of dates in the specified range.
    
    Args:
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format (optional)
    
    Returns:
        List[str]: List of dates
    """
    # Convert dates to datetime
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date) if end_date else pd.Timestamp.now()
    
    # Generate date range
    dates = pd.date_range(start=start, end=end, freq='D')
    
    # Convert to string
    date_strings = [date.strftime('%Y-%m-%d') for date in dates]
    
    return date_strings

def get_timeframe_range(
    start_date: str, 
    end_date: str = None, 
    timeframe: str = 'D1'
) -> List[str]:
    """
    Get a list of timestamps in the specified range for a timeframe.
    
    Args:
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format (optional)
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4', 'D1')
    
    Returns:
        List[str]: List of timestamps
    """
    # Convert dates to datetime
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date) if end_date else pd.Timestamp.now()
    
    # Get frequency
    freq_map = {
        'M1': 'min',
        'M5': '5min',
        'M15': '15min',
        'M30': '30min',
        'H1': 'H',
        'H4': '4H',
        'D1': 'D',
        'W1': 'W',
        'MN1': 'M'
    }
    
    freq = freq_map.get(timeframe, 'D')
    
    # Generate timestamp range
    timestamps = pd.date_range(start=start, end=end, freq=freq)
    
    # Convert to string
    timestamp_strings = [ts.strftime('%Y-%m-%d %H:%M:%S') for ts in timestamps]
    
    return timestamp_strings
