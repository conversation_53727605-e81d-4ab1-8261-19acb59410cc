"""
Run MT5 Initializer Script.
This script properly imports and runs the MT5 initializer following the MT5 connection guide.
It uses the approach from current_mt5_connection.py and multi_mt5_connection.py to maintain
Algo Trading enabled across multiple terminals.
"""
import os
import sys
import logging
import time
import argparse

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the multi-terminal connector
from multi_mt5_connection import MT5MultiConnector

def main():
    """Run the MT5 initializer using the MT5 connection guide approach."""
    parser = argparse.ArgumentParser(description="MT5 Initializer Script")
    parser.add_argument("--terminal", type=int, default=1, help="Terminal ID to initialize (1-5)")
    parser.add_argument("--monitor", action="store_true", help="Keep monitoring Algo Trading status")
    parser.add_argument("--interval", type=int, default=30, help="Monitoring interval in seconds")

    args = parser.parse_args()

    logger.info("Running MT5 initializer following the MT5 connection guide...")

    # Create MT5 multi-connector
    connector = MT5MultiConnector()

    # Check if the specified terminal is running
    terminal_running, algo_trading_enabled = connector.check_terminal(args.terminal)

    if not terminal_running:
        logger.error(f"Terminal {args.terminal} is not running")
        return 1

    # Display terminal information
    logger.info(f"Terminal {args.terminal} is running")

    # Display account information
    connector.display_account_info()

    # Display symbol information for BTCUSD.a
    connector.display_symbol_info("BTCUSD.a")

    # If Algo Trading is not enabled, provide instructions
    if not algo_trading_enabled:
        logger.warning("Algo Trading is DISABLED")
        logger.info("\nTo enable Algo Trading:")
        logger.info("1. In the MT5 terminal, click the 'Algo Trading' button in the toolbar")
        logger.info("2. Make sure the button is highlighted (enabled)")
        logger.info("3. If prompted about allowing automated trading, click 'Yes'")
        logger.info("4. Wait a few seconds for the change to take effect")

        # Wait for user to enable Algo Trading
        input("\nPress Enter after enabling Algo Trading in the terminal...")

        # Check again
        algo_trading_enabled = connector.display_terminal_info(args.terminal)

        if not algo_trading_enabled:
            logger.error("Failed to enable Algo Trading")
            logger.info("\nAdvanced troubleshooting:")
            logger.info("1. Close all MT5 terminals")
            logger.info("2. Open the terminal manually")
            logger.info("3. Enable Algo Trading by clicking the button")
            logger.info("4. Run this script again")
            return 1

    # If monitoring is requested, keep checking Algo Trading status
    if args.monitor:
        try:
            logger.info("\n" + "=" * 80)
            logger.info("MT5 CONNECTION MONITOR ACTIVE")
            logger.info("=" * 80)
            logger.info("Keeping MT5 connection alive. Press Ctrl+C to exit.")
            logger.info("IMPORTANT: Exiting may disable Algo Trading!")
            logger.info("The script will periodically check Algo Trading status.")

            while True:
                # Check if Algo Trading is still enabled
                algo_trading_enabled = connector.display_terminal_info(args.terminal)

                if algo_trading_enabled:
                    logger.info(f"MT5 connection active with Algo Trading ENABLED... {time.strftime('%H:%M:%S')}")
                else:
                    logger.warning("Algo Trading is DISABLED!")
                    logger.info("Please manually enable Algo Trading in the MT5 terminal")

                time.sleep(args.interval)
        except KeyboardInterrupt:
            logger.warning("\nScript interrupted. Algo Trading may be disabled.")
            logger.info("If you need to maintain Algo Trading, please restart this script.")

    # IMPORTANT: Do NOT call mt5.shutdown() as it will disable Algo Trading
    logger.info("Script completed without calling mt5.shutdown()")

    return 0

if __name__ == "__main__":
    sys.exit(main())
