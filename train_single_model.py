"""
Train Single Model Script.
This script trains an LSTM model for a specific timeframe.
It follows the MT5 connection guide best practices to maintain Algo Trading enabled.
"""
import logging
import os
import sys
import argparse
import pandas as pd
import numpy as np
from datetime import datetime
import torch
import time

from data.data_manager import DataManager
from analysis.feature_engineering import FeatureEngineer
from model.model_trainer import ModelTrainer
from utils.torch_security import register_safe_classes

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join('logs', f'train_single_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'))
    ]
)
logger = logging.getLogger('train_single_model')

def load_timeframe_data(timeframe, start_date, end_date=None):
    """
    Load data for a specific timeframe from storage.

    Args:
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1')
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format (optional)

    Returns:
        pd.DataFrame: Timeframe data
    """
    data_manager = DataManager()

    try:
        # Load the latest data file for the timeframe
        df = data_manager.load_historical_data("BTCUSD.a", timeframe)

        if df is None or len(df) == 0:
            logger.error(f"No {timeframe} data found")
            return None

        # Filter to the requested date range
        if start_date:
            df = df.loc[start_date:]
        if end_date:
            df = df.loc[:end_date]

        logger.info(f"Loaded {len(df)} {timeframe} bars for BTCUSD.a")
        return df

    except Exception as e:
        logger.error(f"Failed to load {timeframe} data: {str(e)}")
        return None

def train_model(timeframe, df, start_date, end_date=None, epochs=100, batch_size=64,
               sequence_length=10, hidden_size=64, num_layers=2, patience=10, max_samples=50000):
    """
    Train model for a specific timeframe using the provided data.

    Args:
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1')
        df: DataFrame with timeframe data
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format (optional)
        epochs: Number of epochs for training
        batch_size: Batch size for training
        sequence_length: Sequence length for LSTM model
        hidden_size: Hidden size for LSTM model
        num_layers: Number of LSTM layers
        patience: Patience for early stopping
        max_samples: Maximum number of samples to use for training

    Returns:
        bool: True if training was successful, False otherwise
    """
    try:
        # Create feature engineering instance
        feature_engineering = FeatureEngineer()

        # Generate features
        logger.info(f"Generating features for {timeframe} data...")
        df_features = feature_engineering.create_features(df)

        # Save features
        data_manager = DataManager()
        data_manager.save_features(
            df=df_features,
            symbol="BTCUSD.a",
            timeframe=timeframe
        )
        logger.info(f"Saved feature data to storage for {timeframe}")

        # If dataset is too large, sample it
        if len(df_features) > max_samples:
            logger.info(f"Dataset is large ({len(df_features)} samples), sampling to {max_samples} samples")
            # Keep the most recent data
            df_features = df_features.iloc[-max_samples:]
            logger.info(f"Sampled dataset size: {len(df_features)}")

        # Create sequences for training with improved target variable
        logger.info(f"Creating sequences for training with sequence_length={sequence_length}...")
        X_train, y_train, X_val, y_val, X_test, y_test = feature_engineering.prepare_data_for_training(
            df=df_features,
            sequence_length=sequence_length,
            target_column='future_price_change',  # Use improved target variable
            train_test_split=0.8,
            validation_split=0.1
        )

        # Check if we have valid data
        if len(X_train) == 0 or len(X_val) == 0 or len(X_test) == 0:
            logger.error(f"Failed to create valid sequences for {timeframe}")
            return False

        # Log data shapes
        logger.info(f"Training data shapes: X_train: {X_train.shape}, y_train: {y_train.shape}")
        logger.info(f"Validation data shapes: X_val: {X_val.shape}, y_val: {y_val.shape}")
        logger.info(f"Test data shapes: X_test: {X_test.shape}, y_test: {y_test.shape}")

        # Train model
        logger.info(f"Training {timeframe} model with hidden_size={hidden_size}, num_layers={num_layers}...")

        # Set model parameters
        model_name = f"BTCUSD.a_{timeframe}"
        input_size = X_train.shape[2]  # Number of features
        output_size = 1

        # Create model trainer
        model_trainer = ModelTrainer(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size
        )

        # Train model
        logger.info(f"Training with epochs={epochs}, batch_size={batch_size}, patience={patience}...")
        model_trainer.train(
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val,
            epochs=epochs,
            batch_size=batch_size,
            patience=patience
        )

        # Get feature names
        feature_columns = df_features.columns.tolist()
        feature_columns = [col for col in feature_columns if col != 'returns']

        # Evaluate model with feature names
        metrics = model_trainer.evaluate(X_test, y_test, feature_names=feature_columns)

        # Log metrics
        logger.info(f"Model {model_name} trained and evaluated:")
        for metric_name, metric_value in metrics.items():
            logger.info(f"  {metric_name}: {metric_value:.6f}")

        # Save model
        os.makedirs("model/saved_models", exist_ok=True)
        model_path = f"model/saved_models/{model_name}.pth"
        model_trainer.save_model(model_path)
        logger.info(f"Model saved to {model_path}")

        return True

    except Exception as e:
        logger.error(f"Failed to train {timeframe} model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def print_banner(timeframe):
    """Print a banner for the Single Model Training."""
    banner = """
    ===============================================================
                         TRAIN SINGLE MODEL

      LSTM Model Training for BTCUSD {timeframe}
      Version: 1.0.0
      Date: {date}
    ===============================================================
    """.format(timeframe=timeframe, date=datetime.now().strftime("%Y-%m-%d"))

    print(banner)
    logger.info(f"Single Model Training for {timeframe} starting...")

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train Single Model for BTCUSD Trading Bot')
    parser.add_argument('--timeframe', type=str, required=True, help='Timeframe to train model for (e.g., M5, M15, M30, H1, H4)')
    parser.add_argument('--start-date', type=str, default='2020-04-27', help='Start date for training data')
    parser.add_argument('--end-date', type=str, default=None, help='End date for training data')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs for training')
    parser.add_argument('--batch-size', type=int, default=64, help='Batch size for training')
    parser.add_argument('--sequence-length', type=int, default=10, help='Sequence length for LSTM model')
    parser.add_argument('--hidden-size', type=int, default=64, help='Hidden size for LSTM model')
    parser.add_argument('--num-layers', type=int, default=2, help='Number of LSTM layers')
    parser.add_argument('--patience', type=int, default=10, help='Patience for early stopping')
    parser.add_argument('--max-samples', type=int, default=50000, help='Maximum number of samples to use for training')

    args = parser.parse_args()

    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Display banner
    print_banner(args.timeframe)

    # Register safe classes for PyTorch 2.6+ security model
    register_safe_classes()

    try:
        logger.info(f"=== Training {args.timeframe} Model ===")

        # Load data
        logger.info(f"Loading {args.timeframe} data from {args.start_date} to {args.end_date or 'now'}...")
        df = load_timeframe_data(args.timeframe, args.start_date, args.end_date)

        if df is None or len(df) == 0:
            logger.error(f"Failed to load {args.timeframe} data")
            return 1

        # Train model
        logger.info(f"Training {args.timeframe} model...")
        success = train_model(
            timeframe=args.timeframe,
            df=df,
            start_date=args.start_date,
            end_date=args.end_date,
            epochs=args.epochs,
            batch_size=args.batch_size,
            sequence_length=args.sequence_length,
            hidden_size=args.hidden_size,
            num_layers=args.num_layers,
            patience=args.patience,
            max_samples=args.max_samples
        )

        if success:
            logger.info(f"{args.timeframe} model training completed successfully")
            return 0
        else:
            logger.error(f"{args.timeframe} model training failed")
            return 1

    except Exception as e:
        logger.error(f"Error training {args.timeframe} model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
