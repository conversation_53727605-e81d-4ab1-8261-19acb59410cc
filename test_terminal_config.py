"""
Test Terminal Configuration Script.
This script tests the standardized terminal configuration.
"""
import logging
import sys
import os
import subprocess
from config.credentials import MT5_TERMINALS
from utils.path_utils import normalize_path, get_executable_name

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_terminal_config():
    """Test the standardized terminal configuration."""
    logger.info("Testing standardized terminal configuration...")

    # Test path normalization
    for terminal_id, config in MT5_TERMINALS.items():
        path = config["path"]
        normalized_path = normalize_path(path)
        executable = get_executable_name(normalized_path)

        logger.info(f"\nTerminal {terminal_id} ({config['name']}):")
        logger.info(f"  Original path: {path}")
        logger.info(f"  Normalized path: {normalized_path}")
        logger.info(f"  Executable: {executable}")

        # Check if the executable exists
        if os.path.exists(normalized_path):
            logger.info(f"  ✅ Executable exists")
        else:
            logger.error(f"  ❌ Executable does not exist")

        # Check if the terminal is running
        try:
            result = subprocess.run(['tasklist', '/FI', f'IMAGENAME eq {executable}'],
                                   capture_output=True, text=True, check=False)

            if executable in result.stdout:
                logger.info(f"  [SUCCESS] Terminal is running")
            else:
                logger.warning(f"  [WARNING] Terminal is not running")
        except Exception as e:
            logger.error(f"  [FAILED] Error checking if terminal is running: {str(e)}")

    # Skip testing crypto_trading_bot.config.terminal_config for now
    logger.info("\nSkipping crypto_trading_bot.config.terminal_config test")

    logger.info("\nTerminal configuration test completed")
    return 0

def main():
    """Main function."""
    return test_terminal_config()

if __name__ == "__main__":
    sys.exit(main())
