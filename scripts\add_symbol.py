"""
Add Symbol Script.
This script provides a simple way to add a new trading symbol to the system.
"""
import argparse
import logging
import sys
import os
from typing import List, Optional

# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from trading.mt5_connector import MT5Connector
from trading.symbol_manager import SymbolManager
from data.data_collector import DataCollector
from data.symbol_data_service import SymbolDataService

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/add_symbol.log')
    ]
)
logger = logging.getLogger('scripts.add_symbol')

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Add a new trading symbol')
    parser.add_argument('symbol', help='Symbol to add (e.g., BTCUSD.a)')
    parser.add_argument('--years', type=int, default=5, help='Number of years of historical data to collect')
    parser.add_argument('--no-validate', action='store_false', dest='validate', help='Skip data validation')
    parser.add_argument('--terminals', type=int, nargs='+', help='Terminal IDs to collect from')
    
    args = parser.parse_args()
    
    # Initialize MT5 connector
    mt5_connector = MT5Connector()
    if not mt5_connector.check_connection():
        logger.error("Failed to initialize MT5 connector")
        return
    
    # Initialize symbol manager
    symbol_manager = SymbolManager(mt5_connector)
    
    # Validate symbol format
    if not symbol_manager.validate_symbol_format(args.symbol):
        logger.error(f"Invalid symbol format: {args.symbol}")
        return
    
    # Check symbol availability
    availability = symbol_manager.check_symbol_availability(args.symbol)
    if not any(availability.values()):
        logger.error(f"Symbol {args.symbol} is not available on any terminal")
        return
    
    # Add symbol
    if not symbol_manager.add_symbol(args.symbol):
        logger.error(f"Failed to add symbol {args.symbol}")
        return
    
    # Initialize data collector
    data_collector = DataCollector(mt5_connector)
    
    # Initialize symbol data service
    symbol_data_service = SymbolDataService(mt5_connector, data_collector)
    
    # Collect historical data
    logger.info(f"Collecting historical data for {args.symbol}...")
    data = symbol_data_service.collect_historical_data(
        symbol=args.symbol,
        years=args.years,
        terminal_ids=args.terminals
    )
    
    if not data:
        logger.error(f"Failed to collect historical data for {args.symbol}")
        return
    
    # Validate data if requested
    if args.validate:
        logger.info(f"Validating data for {args.symbol}...")
        validation_results = symbol_data_service.validate_data(args.symbol)
        
        if not validation_results:
            logger.warning(f"No validation results for {args.symbol}")
        else:
            # Log validation results
            for tf, metrics in validation_results.items():
                logger.info(f"Validation results for {args.symbol} {tf}:")
                for metric, value in metrics.items():
                    logger.info(f"  {metric}: {value}")
    
    # Merge data from all terminals
    for tf in data.keys():
        logger.info(f"Merging data for {args.symbol} {tf}...")
        merged_df = symbol_data_service.merge_terminal_data(args.symbol, tf)
        if merged_df is not None:
            logger.info(f"Successfully merged data for {args.symbol} {tf} ({len(merged_df)} bars)")
        else:
            logger.warning(f"Failed to merge data for {args.symbol} {tf}")
    
    logger.info(f"Symbol {args.symbol} added successfully")

if __name__ == '__main__':
    main()
