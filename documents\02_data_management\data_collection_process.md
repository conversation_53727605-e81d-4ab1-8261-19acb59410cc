# Data Collection Process

This document explains the data collection process used in the trading system.

## Overview

The trading system collects historical OHLCV (Open, High, Low, Close, Volume) data from 5 MT5 terminals, each specializing in a specific model type:
- Terminal 1: ARIMA
- Terminal 2: LSTM
- Terminal 3: TFT
- Terminal 4: LSTM+ARIMA ensemble
- Terminal 5: TFT+ARIMA ensemble

Data is collected for multiple timeframes:
- M5: 5-minute
- M15: 15-minute
- M30: 30-minute
- H1: 1-hour
- H4: 4-hour

## Data Collection Process

The data collection process is handled by the `DataCollector` class in `data/data_collector.py`. This class provides methods for:
- Collecting historical data
- Processing data with technical indicators
- Saving data to disk
- Loading data from disk

### Collecting Historical Data

To collect historical data, use the `collect_historical_data` method:

```python
from data.data_collector import DataCollector

# Initialize the data collector
data_collector = DataCollector()

# Collect historical data
data = data_collector.collect_historical_data(
    symbol="BTCUSD.a",
    timeframe="M5",
    start_date="2023-01-01",
    end_date="2023-12-31",
    terminal_id=1
)
```

The `collect_historical_data` method:
1. Connects to the specified MT5 terminal
2. Retrieves historical OHLCV data for the specified symbol and timeframe
3. Processes the data to ensure it's clean and consistent
4. Saves the data using the standardized path structure
5. Returns the collected data as a pandas DataFrame

### Processing Data with Technical Indicators

To process data with technical indicators, use the `process_data_with_indicators` method:

```python
# Process data with technical indicators
processed_data = data_collector.process_data_with_indicators(
    data=data,
    symbol="BTCUSD.a",
    timeframe="M5",
    terminal_id=1
)
```

The `process_data_with_indicators` method:
1. Calculates technical indicators using the TA-Lib library
2. Adds the indicators to the DataFrame
3. Handles missing values and ensures data quality
4. Saves the processed data using the standardized path structure
5. Returns the processed data as a pandas DataFrame

### Technical Indicators

The following technical indicators are calculated:
- Moving Averages (SMA, EMA, WMA)
- Relative Strength Index (RSI)
- Moving Average Convergence Divergence (MACD)
- Bollinger Bands
- Average True Range (ATR)
- Commodity Channel Index (CCI)
- Stochastic Oscillator
- On-Balance Volume (OBV)
- Ichimoku Cloud
- Parabolic SAR
- Momentum
- Rate of Change (ROC)

### Saving and Loading Data

Data is saved using the standardized path structure:

```python
# Save data
data_collector.save_data(
    data=processed_data,
    symbol="BTCUSD.a",
    timeframe="M5",
    terminal_id=1
)

# Load data
loaded_data = data_collector.load_data(
    symbol="BTCUSD.a",
    timeframe="M5",
    terminal_id=1
)
```

The `save_data` and `load_data` methods use the `get_terminal_data_path` function from `utils/path_utils.py` to determine the correct path for each terminal.

## Data Quality Checks

The data collection process includes several data quality checks:
- Checking for missing values
- Ensuring data is sorted by time
- Removing duplicate entries
- Validating OHLCV values (e.g., high >= low)
- Checking for gaps in the time series

## Data Storage

Data is stored in Parquet format, which provides:
- Efficient storage (compressed)
- Fast read/write operations
- Schema enforcement
- Support for complex nested data structures

The standardized path structure for historical data is:

```
data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
```

Example:
```
data/storage/historical/terminal_1/BTCUSD.a_M5/20230101_20250101_terminal_1_20250425123456.parquet
```

The standardized path structure for processed data with technical indicators is:

```
data/storage/features/terminal_{terminal_id}/{symbol}_{timeframe}/features_terminal_{terminal_id}_{timestamp}.parquet
```

Example:
```
data/storage/features/terminal_1/BTCUSD.a_M5/features_terminal_1_20250425123456.parquet
```

## Data Collection Schedule

Data collection is typically scheduled to run:
- At the end of each trading day
- Before model training
- On demand when new data is needed

Last updated: 2025-06-06 15:35:00
