"""
Test script for the Data Validator implementation.
This script tests the functionality of the data validator.
"""
import sys
import logging
import numpy as np
import pandas as pd
import unittest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append('.')

# Import the data validator
from data.validation.data_validator import DataValidator

class TestDataValidator(unittest.TestCase):
    """Test cases for the Data Validator."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a data validator
        self.validator = DataValidator()

        # Create a sample DataFrame for testing
        self.df = pd.DataFrame({
            'time': pd.date_range(start='2023-01-01', periods=100, freq='h'),
            'open': np.random.rand(100) * 100,
            'high': np.random.rand(100) * 100 + 10,
            'low': np.random.rand(100) * 100 - 10,
            'close': np.random.rand(100) * 100,
            'tick_volume': np.random.randint(1, 1000, 100)
        })

        # Ensure high > low, high > open, high > close, low < open, low < close for all rows
        for i in range(len(self.df)):
            max_val = max(self.df.loc[i, 'open'], self.df.loc[i, 'close'], self.df.loc[i, 'low']) + 1
            min_val = min(self.df.loc[i, 'open'], self.df.loc[i, 'close']) - 1
            self.df.loc[i, 'high'] = max_val
            self.df.loc[i, 'low'] = min_val

        # Set time as index
        self.df.set_index('time', inplace=True)

    def test_missing_values_check(self):
        """Test missing values check."""
        # Add some missing values
        df_with_missing = self.df.copy()
        df_with_missing.loc[df_with_missing.index[0:5], 'open'] = np.nan

        # Add rule
        self.validator.add_rule(
            rule_name="missing_values_check",
            rule_type="missing_values",
            parameters={
                "columns": ["open", "high", "low", "close", "tick_volume"],
                "threshold": 0.1  # Allow up to 10% missing values
            }
        )

        # Validate data
        results = self.validator.validate_data(df_with_missing)

        # Check results
        self.assertIn("missing_values_check", results)
        self.assertEqual(results["missing_values_check"]["status"], "pass")

        # Now make it fail by lowering the threshold
        self.validator.add_rule(
            rule_name="missing_values_check_strict",
            rule_type="missing_values",
            parameters={
                "columns": ["open"],
                "threshold": 0.01  # Allow only 1% missing values
            }
        )

        # Validate data
        results = self.validator.validate_data(df_with_missing)

        # Check results
        self.assertIn("missing_values_check_strict", results)
        self.assertEqual(results["missing_values_check_strict"]["status"], "fail")

    def test_range_check_single_column(self):
        """Test range check for a single column."""
        # Add rule for a single column
        self.validator.add_rule(
            rule_name="price_range_check",
            rule_type="range_check",
            parameters={
                "columns": "close",
                "min_value": 0,
                "max_value": 200
            }
        )

        # Validate data
        results = self.validator.validate_data(self.df)

        # Check results
        self.assertIn("price_range_check", results)
        self.assertEqual(results["price_range_check"]["status"], "pass")

        # Now make it fail by adding a value outside the range
        df_with_outlier = self.df.copy()
        df_with_outlier.loc[df_with_outlier.index[0], 'close'] = 300

        # Validate data
        results = self.validator.validate_data(df_with_outlier)

        # Check results
        self.assertIn("price_range_check", results)
        self.assertEqual(results["price_range_check"]["status"], "fail")

    def test_range_check_multiple_columns(self):
        """Test range check for multiple columns."""
        # Add rule for multiple columns
        self.validator.add_rule(
            rule_name="price_integrity_check",
            rule_type="range_check",
            parameters={
                "columns": {
                    "high": {"min_value": None, "max_value": None, "relation": "high >= open and high >= close and high >= low"},
                    "low": {"min_value": None, "max_value": None, "relation": "low <= open and low <= close and low <= high"},
                    "open": {"min_value": 0, "max_value": None},
                    "close": {"min_value": 0, "max_value": None}
                }
            }
        )

        # Validate data
        results = self.validator.validate_data(self.df)

        # Check results
        self.assertIn("price_integrity_check", results)
        self.assertEqual(results["price_integrity_check"]["status"], "pass")

        # Now make it fail by violating the relation
        df_with_violation = self.df.copy()
        df_with_violation.loc[df_with_violation.index[0], 'high'] = 50
        df_with_violation.loc[df_with_violation.index[0], 'low'] = 60  # high < low

        # Validate data
        results = self.validator.validate_data(df_with_violation)

        # Check results
        self.assertIn("price_integrity_check", results)
        self.assertEqual(results["price_integrity_check"]["status"], "fail")

    def test_outlier_check(self):
        """Test outlier check."""
        # Add rule
        self.validator.add_rule(
            rule_name="price_outliers_check",
            rule_type="outlier_check",
            parameters={
                "column": "close",
                "method": "zscore",
                "threshold": 3.0  # Z-score threshold for outliers
            }
        )

        # Validate data
        results = self.validator.validate_data(self.df)

        # Check results
        self.assertIn("price_outliers_check", results)
        self.assertEqual(results["price_outliers_check"]["status"], "pass")

        # Now make it fail by adding multiple outliers (more than 5% of the data)
        df_with_outlier = self.df.copy()
        # Add extreme outliers to 6 rows (6% of the data)
        mean_close = df_with_outlier['close'].mean()
        std_close = df_with_outlier['close'].std()
        # Set to mean + 10 standard deviations to ensure they're outliers
        for i in range(6):  # 6 out of 100 = 6%
            df_with_outlier.loc[df_with_outlier.index[i], 'close'] = mean_close + 10 * std_close

        # Validate data
        results = self.validator.validate_data(df_with_outlier)

        # Check results
        self.assertIn("price_outliers_check", results)
        self.assertEqual(results["price_outliers_check"]["status"], "fail")

    def test_timestamp_check(self):
        """Test timestamp check."""
        # Add rule
        self.validator.add_rule(
            rule_name="timestamp_check",
            rule_type="timestamp_check",
            parameters={
                "timestamp_column": "time",
                "ascending": True
            }
        )

        # Create a DataFrame with time as a column (not index)
        df_with_time_column = self.df.reset_index()

        # Validate data
        results = self.validator.validate_data(df_with_time_column)

        # Check results
        self.assertIn("timestamp_check", results)
        self.assertEqual(results["timestamp_check"]["status"], "pass")

        # Now make it fail by adding out-of-order timestamps
        df_with_bad_timestamps = df_with_time_column.copy()
        # Swap two timestamps
        temp = df_with_bad_timestamps.loc[0, 'time']
        df_with_bad_timestamps.loc[0, 'time'] = df_with_bad_timestamps.loc[10, 'time']
        df_with_bad_timestamps.loc[10, 'time'] = temp

        # Validate data
        results = self.validator.validate_data(df_with_bad_timestamps)

        # Check results
        self.assertIn("timestamp_check", results)
        self.assertEqual(results["timestamp_check"]["status"], "fail")

    def test_data_type_check(self):
        """Test data type check."""
        # Add rule
        self.validator.add_rule(
            rule_name="data_type_check",
            rule_type="data_type_check",
            parameters={
                "column_types": {
                    "open": "numeric",
                    "high": "numeric",
                    "low": "numeric",
                    "close": "numeric",
                    "tick_volume": "numeric"
                }
            }
        )

        # Validate data
        results = self.validator.validate_data(self.df)

        # Check results
        self.assertIn("data_type_check", results)
        self.assertEqual(results["data_type_check"]["status"], "pass")

        # Now make it fail by changing a data type
        df_with_bad_type = self.df.copy()
        df_with_bad_type['close'] = df_with_bad_type['close'].astype(str)

        # Validate data
        results = self.validator.validate_data(df_with_bad_type)

        # Check results
        self.assertIn("data_type_check", results)
        self.assertEqual(results["data_type_check"]["status"], "fail")

    def test_custom_check(self):
        """Test custom check."""
        # Add rule
        self.validator.add_rule(
            rule_name="price_integrity_custom",
            rule_type="custom_check",
            parameters={
                "check_name": "price_integrity"
            }
        )

        # Validate data
        results = self.validator.validate_data(self.df)

        # Check results
        self.assertIn("price_integrity_custom", results)
        self.assertEqual(results["price_integrity_custom"]["status"], "pass")

        # Now make it fail by violating price integrity
        df_with_violation = self.df.copy()
        df_with_violation['high'] = df_with_violation['low'] - 1  # high < low

        # Validate data
        results = self.validator.validate_data(df_with_violation)

        # Check results
        self.assertIn("price_integrity_custom", results)
        self.assertEqual(results["price_integrity_custom"]["status"], "fail")

    def test_generate_validation_report(self):
        """Test generating a validation report."""
        # Add some rules
        self.validator.add_rule(
            rule_name="missing_values_check",
            rule_type="missing_values",
            parameters={
                "columns": ["open", "high", "low", "close", "tick_volume"],
                "threshold": 0.1
            }
        )

        self.validator.add_rule(
            rule_name="price_range_check",
            rule_type="range_check",
            parameters={
                "columns": "close",
                "min_value": 0,
                "max_value": 200
            }
        )

        # Validate data
        self.validator.validate_data(self.df)

        # Generate report
        report = self.validator.generate_validation_report()

        # Check report
        self.assertIn("status", report)
        self.assertIn("timestamp", report)
        self.assertIn("summary", report)
        self.assertIn("details", report)

        # Check summary
        self.assertEqual(report["summary"]["total_rules"], 2)
        self.assertEqual(report["summary"]["passed"], 2)
        self.assertEqual(report["summary"]["failed"], 0)
        self.assertEqual(report["summary"]["errors"], 0)

        # Check overall status
        self.assertEqual(report["status"], "pass")

def main():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    main()
