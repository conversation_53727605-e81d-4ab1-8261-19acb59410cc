# Model Training Guide

This document explains the model training process used in the trading system.

## Overview

The trading system uses multiple model types for price prediction:
- ARIMA: Statistical time series forecasting
- LSTM: Deep learning sequence modeling
- TFT: Transformer-based forecasting
- Ensemble models: Combinations of the above

Each model type is assigned to a specific MT5 terminal:
- Terminal 1: ARIMA
- Terminal 2: LSTM
- Terminal 3: TFT
- Terminal 4: LSTM+ARIMA ensemble
- Terminal 5: TFT+ARIMA ensemble

## Model Training Process

The model training process is handled by the `ModelManager` class in `model/model_manager.py`. This class provides methods for:
- Creating models
- Training models
- Updating models
- Evaluating models
- Making predictions
- Saving and loading models

### Training a Model

To train a model, use the `train_model` method:

```python
from model.model_manager import ModelManager

# Initialize the model manager
model_manager = ModelManager()

# Train a model
history = model_manager.train_model(
    model_name="btcusd_m5_lstm",
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    feature_names=feature_names,
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

The `train_model` method:
1. Creates a model if it doesn't exist
2. Trains the model on the provided data
3. Saves the model using the standardized path structure
4. Returns the training history

### Model Types

#### LSTM Model

The LSTM model is a deep learning model for sequence modeling. It is trained using:
- A sequence of historical price data
- Technical indicators as features
- Future price movement as the target

Training parameters:
- Sequence length: 60 (default)
- Hidden size: 128 (default)
- Number of layers: 2 (default)
- Learning rate: 0.001 (default)
- Batch size: 64 (default)
- Number of epochs: 100 (default)

#### TFT Model

The Temporal Fusion Transformer (TFT) model is a state-of-the-art model for time series forecasting. It is trained using:
- Historical price data
- Technical indicators as features
- Future price movement as the target

Training parameters:
- Hidden size: 128 (default)
- Attention heads: 4 (default)
- Dropout rate: 0.1 (default)
- Learning rate: 0.001 (default)
- Batch size: 64 (default)
- Number of epochs: 100 (default)

#### ARIMA Model

The ARIMA model is a statistical time series forecasting model. It is trained using:
- Historical price data
- Auto-regressive and moving average components
- Differencing to make the time series stationary

Training parameters:
- p: Auto-regressive order (default: auto)
- d: Differencing order (default: auto)
- q: Moving average order (default: auto)

#### Ensemble Models

Ensemble models combine the predictions of multiple models. The trading system uses two types of ensembles:
- LSTM+ARIMA: Combines LSTM and ARIMA predictions
- TFT+ARIMA: Combines TFT and ARIMA predictions

Ensemble weights are determined during training based on validation performance.

### Saving and Loading Models

Models are saved using the standardized path structure:

```python
# Save a model
model_manager.save_model(
    model_name="btcusd_m5_lstm",
    symbol="BTCUSD.a",
    timeframe="M5"
)

# Load a model
model_manager.load_model(
    model_name="btcusd_m5_lstm",
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

The `save_model` and `load_model` methods use the `get_model_path` function from `utils/path_utils.py` to determine the correct path for each model type.

## Performance Optimization

The model training process includes several performance optimizations:
- Automatic Mixed Precision (AMP) for faster training on GPUs
- Gradient accumulation for larger effective batch sizes
- Learning rate scheduling for better convergence
- Early stopping to prevent overfitting
- Model checkpointing to save the best model

## Model Evaluation

Models are evaluated using the `evaluate_model` method:

```python
# Evaluate a model
metrics = model_manager.evaluate_model(
    model_name="btcusd_m5_lstm",
    X=X_test,
    y=y_test,
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

Evaluation metrics include:
- Mean Absolute Error (MAE)
- Mean Squared Error (MSE)
- Root Mean Squared Error (RMSE)
- R-squared (R²)
- Direction Accuracy

## Making Predictions

To make predictions with a trained model, use the `predict` method:

```python
# Make predictions
predictions = model_manager.predict(
    model_name="btcusd_m5_lstm",
    X=X_new,
    feature_names=feature_names,
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

## Related Documentation

- [LSTM Model](lstm_model.md): Detailed information about the LSTM model
- [TFT Model](tft_model.md): Detailed information about the TFT model
- [ARIMA Model](arima_model.md): Detailed information about the ARIMA model
- [Ensemble Model](ensemble_model.md): Detailed information about ensemble models
- [Model Evaluation](model_evaluation.md): Information about model evaluation metrics
- [Standardized Paths](../technical_reference/standardized_paths.md): Information about the standardized path structure
