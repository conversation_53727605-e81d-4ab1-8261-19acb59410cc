# Configuration Guide

This document provides detailed information about configuring the trading system.

## Overview

The trading system uses several configuration files to customize its behavior. The main configuration files are:
- `config/central_config.py`: Central configuration management
- `config/credentials.py`: MT5 terminal credentials
- `config/trading_config.py`: Trading strategy configuration
- `config/logging_config.py`: Logging configuration

## Central Configuration

The central configuration is managed by `config/central_config.py`. This file imports and combines configurations from other modules to ensure consistency. It contains settings for:
- Data collection
- Model training
- Trading
- Terminal-model mapping
- Path management

Example:
```python
from config.central_config import CentralConfig

# Access terminal configuration
terminal_config = CentralConfig.get_terminal_config(terminal_id=1)

# Get model type for terminal
model_type = CentralConfig.get_terminal_model_type(terminal_id=1)

# Get model parameters
model_params = CentralConfig.get_model_params(terminal_id=1)

# Terminal-model mapping
terminal_model_mapping = {
    1: "arima",
    2: "lstm",
    3: "tft",
    4: "lstm_arima",
    5: "tft_arima"
}
```

## MT5 Terminal Credentials

The MT5 terminal credentials are stored in `config/credentials.py`. This file contains:
- Terminal paths
- Login credentials
- Server settings

Example:
```python
# MT5 terminal credentials
MT5_TERMINALS = {
    1: {
        'name': 'Terminal 1 (ARIMA)',
        'path': 'C:/Program Files/MetaTrader 5/terminal64.exe',
        'login': 12345,
        'password': 'password',
        'server': 'ServerName'
    },
    2: {
        'name': 'Terminal 2 (LSTM)',
        'path': 'C:/Program Files/MetaTrader 5 - 2/terminal64.exe',
        'login': 12346,
        'password': 'password',
        'server': 'ServerName'
    },
    3: {
        'name': 'Terminal 3 (TFT)',
        'path': 'C:/Program Files/MetaTrader 5 - 3/terminal64.exe',
        'login': 12347,
        'password': 'password',
        'server': 'ServerName'
    },
    4: {
        'name': 'Terminal 4 (LSTM+ARIMA)',
        'path': 'C:/Program Files/MetaTrader 5 - 4/terminal64.exe',
        'login': 12348,
        'password': 'password',
        'server': 'ServerName'
    },
    5: {
        'name': 'Terminal 5 (TFT+ARIMA)',
        'path': 'C:/Program Files/MetaTrader 5 - 5/terminal64.exe',
        'login': 12349,
        'password': 'password',
        'server': 'ServerName'
    }
}
```

## Trading Strategy Configuration

The trading strategy configuration is stored in `config/trading_config.py`. This file contains settings for:
- Trading symbols
- Timeframes
- Risk management
- Signal thresholds
- Position sizing
- Stop loss and take profit

Example:
```python
# Trading strategy configuration
TRADING_CONFIG = {
    # Symbol configuration
    "symbols": ["BTCUSD.a", "ETHUSD.a", "XAUUSD"],
    
    # Timeframes for analysis
    "timeframes": {
        "M5": "5m",   # 5-minute timeframe
        "M15": "15m", # 15-minute timeframe
        "M30": "30m", # 30-minute timeframe
        "H1": "1h",   # 1-hour timeframe
        "H4": "4h"    # 4-hour timeframe
    },
    
    # Risk management
    "risk_management": {
        "max_risk_per_trade": 0.02,  # Maximum risk per trade (2% of account balance)
        "max_open_positions": 3,     # Maximum number of open positions
        "max_daily_loss": 0.05,      # Maximum daily loss (5% of account balance)
        "max_drawdown": 0.20,        # Maximum drawdown (20% of account balance)
    },
    
    # Signal thresholds
    "signal_thresholds": {
        "strong_buy": 0.7,   # Strong buy signal threshold
        "buy": 0.3,          # Buy signal threshold
        "neutral_low": -0.3, # Neutral signal lower threshold
        "neutral_high": 0.3, # Neutral signal upper threshold
        "sell": -0.3,        # Sell signal threshold
        "strong_sell": -0.7  # Strong sell signal threshold
    },
    
    # Position sizing
    "position_sizing": {
        "use_dynamic_sizing": True,  # Use dynamic position sizing
        "fixed_lot_size": 0.01,      # Fixed lot size (if dynamic sizing is disabled)
        "max_position_size": 0.05,   # Maximum position size (5% of account balance)
    },
    
    # Stop loss and take profit
    "sl_tp": {
        "use_dynamic_sl_tp": True,   # Use dynamic stop loss and take profit
        "default_sl_pips": 100,      # Default stop loss in pips
        "default_tp_pips": 200,      # Default take profit in pips
        "atr_multiplier_sl": 1.5,    # ATR multiplier for stop loss
        "atr_multiplier_tp": 3.0,    # ATR multiplier for take profit
        "atr_period": 14             # ATR period
    },
    
    # Terminal-specific settings
    "terminal_settings": {
        1: {  # Terminal 1 (ARIMA)
            "risk_per_trade": 0.01,  # 1% risk per trade
            "sl_multiplier": 1.0,    # Stop loss multiplier
            "tp_multiplier": 1.5     # Take profit multiplier
        },
        2: {  # Terminal 2 (LSTM)
            "risk_per_trade": 0.015, # 1.5% risk per trade
            "sl_multiplier": 1.2,    # Stop loss multiplier
            "tp_multiplier": 2.0     # Take profit multiplier
        },
        3: {  # Terminal 3 (TFT)
            "risk_per_trade": 0.02,  # 2% risk per trade
            "sl_multiplier": 1.5,    # Stop loss multiplier
            "tp_multiplier": 2.5     # Take profit multiplier
        },
        4: {  # Terminal 4 (LSTM+ARIMA)
            "risk_per_trade": 0.025, # 2.5% risk per trade
            "sl_multiplier": 1.8,    # Stop loss multiplier
            "tp_multiplier": 3.0     # Take profit multiplier
        },
        5: {  # Terminal 5 (TFT+ARIMA)
            "risk_per_trade": 0.03,  # 3% risk per trade
            "sl_multiplier": 2.0,    # Stop loss multiplier
            "tp_multiplier": 3.5     # Take profit multiplier
        }
    }
}
```

## Model Configuration

The model configuration is stored in `config/model_config.py`. This file contains settings for:
- Model hyperparameters
- Training parameters
- Evaluation parameters
- Feature engineering

Example:
```python
# Model configuration
MODEL_CONFIG = {
    # LSTM model configuration
    "lstm": {
        "input_size": 32,        # Number of features
        "hidden_size": 128,      # Hidden layer size
        "num_layers": 2,         # Number of LSTM layers
        "dropout": 0.2,          # Dropout rate
        "learning_rate": 0.001,  # Learning rate
        "batch_size": 64,        # Batch size
        "epochs": 100,           # Number of epochs
        "patience": 10,          # Early stopping patience
        "sequence_length": 60    # Sequence length
    },
    
    # TFT model configuration
    "tft": {
        "hidden_size": 128,      # Hidden layer size
        "attention_heads": 4,    # Number of attention heads
        "dropout": 0.1,          # Dropout rate
        "learning_rate": 0.001,  # Learning rate
        "batch_size": 64,        # Batch size
        "epochs": 100,           # Number of epochs
        "patience": 10,          # Early stopping patience
        "context_length": 60,    # Context length
        "prediction_length": 10  # Prediction length
    },
    
    # ARIMA model configuration
    "arima": {
        "p": [0, 1, 2, 4, 6],    # Possible p values
        "d": [0, 1, 2],          # Possible d values
        "q": [0, 1, 2, 4],       # Possible q values
        "seasonal_p": [0, 1, 2], # Possible seasonal p values
        "seasonal_d": [0, 1],    # Possible seasonal d values
        "seasonal_q": [0, 1, 2], # Possible seasonal q values
        "max_order": 10,         # Maximum order
        "information_criterion": "aic"  # Information criterion
    },
    
    # Ensemble model configuration
    "ensemble": {
        "weights": {
            "lstm_arima": {
                "lstm": 0.6,     # LSTM weight
                "arima": 0.4     # ARIMA weight
            },
            "tft_arima": {
                "tft": 0.7,      # TFT weight
                "arima": 0.3     # ARIMA weight
            }
        }
    },
    
    # Feature engineering configuration
    "feature_engineering": {
        "use_technical_indicators": True,  # Use technical indicators
        "use_price_patterns": True,        # Use price patterns
        "use_volume_indicators": True,     # Use volume indicators
        "use_sentiment_indicators": False, # Use sentiment indicators
        "normalize_features": True,        # Normalize features
        "feature_selection": True,         # Use feature selection
        "max_features": 32                 # Maximum number of features
    },
    
    # Training configuration
    "training": {
        "train_test_split": 0.8,           # Train-test split ratio
        "validation_split": 0.2,           # Validation split ratio
        "use_cross_validation": False,     # Use cross-validation
        "n_splits": 5,                     # Number of cross-validation splits
        "shuffle": False,                  # Shuffle data
        "use_early_stopping": True,        # Use early stopping
        "use_learning_rate_scheduler": True,  # Use learning rate scheduler
        "use_amp": True                    # Use Automatic Mixed Precision
    }
}
```

## Logging Configuration

The logging configuration is stored in `config/logging_config.py`. This file contains settings for:
- Log levels
- Log formats
- Log file paths
- Log rotation

Example:
```python
# Logging configuration
import logging
import os
from datetime import datetime

# Create logs directory if it doesn't exist
os.makedirs("logs", exist_ok=True)

# Log file path
log_file = f"logs/trading_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
        "detailed": {
            "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "standard",
            "stream": "ext://sys.stdout"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "detailed",
            "filename": log_file,
            "maxBytes": 10485760,  # 10 MB
            "backupCount": 5,
            "encoding": "utf8"
        }
    },
    "loggers": {
        "": {  # Root logger
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": True
        }
    }
}
```

## Central Configuration Class

The trading system uses a `CentralConfig` class to access configuration settings. This class is defined in `config/central_config.py` and provides methods for:
- Getting terminal paths
- Getting model types for terminals
- Getting trading settings
- Getting model settings
- Getting logging settings

Example:
```python
from config.config import GENERAL_CONFIG
from config.credentials import MT5_TERMINALS
from config.trading_config import TRADING_CONFIG
from config.model_config import MODEL_CONFIG
from config.logging_config import LOGGING_CONFIG

class CentralConfig:
    """
    Central configuration class for accessing all configuration settings.
    """
    
    @staticmethod
    def get_terminal_path(terminal_id):
        """
        Get the path to the specified terminal.
        
        Args:
            terminal_id: Terminal ID
            
        Returns:
            str: Terminal path
        """
        if terminal_id in MT5_TERMINALS:
            return MT5_TERMINALS[terminal_id]['path']
        return None
    
    @staticmethod
    def get_model_type_for_terminal(terminal_id):
        """
        Get the model type for the specified terminal.
        
        Args:
            terminal_id: Terminal ID
            
        Returns:
            str: Model type
        """
        return GENERAL_CONFIG['terminal_model_mapping'].get(terminal_id, 'lstm')
    
    # More methods...
```

## Related Documentation

- [Installation Guide](installation_guide.md): Detailed installation instructions
- [Quick Start Guide](quick_start_guide.md): Quick start guide for the trading system
- [System Architecture](system_architecture.md): Information about the system architecture
- [MT5 Connection Guide](../technical_reference/mt5_connection_guide.md): Guide for connecting to MT5 terminals
