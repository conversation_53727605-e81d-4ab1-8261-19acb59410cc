# Risk Management

This document describes the comprehensive risk management framework implemented in the MT5 Trading System.

## Overview

The MT5 Trading System implements a multi-layered risk management framework designed to protect capital and ensure sustainable trading operations. The risk management system operates at multiple levels: individual trade risk, daily risk limits, portfolio risk, and terminal-specific risk controls.

## Risk Management Architecture

### Risk Management Components

```
Risk Management Framework
├── OrderManager (trading/order_manager.py)
│   ├── Position sizing calculations
│   ├── Stop loss/take profit management
│   ├── Daily loss limit monitoring
│   └── Maximum position controls
├── PositionManager (trading/position_manager.py)
│   ├── Portfolio risk calculations
│   ├── Position tracking
│   └── Risk-based position sizing
├── TradingStrategy (trading/trading_strategy.py)
│   ├── Signal-based risk controls
│   ├── Multi-timeframe risk validation
│   └── Trailing stop management
└── TerminalManager (trading/terminal_manager.py)
    ├── Terminal-specific risk profiles
    ├── Multi-terminal risk distribution
    └── Risk aggregation across terminals
```

### Risk Control Layers

1. **Trade-Level Risk**: Individual trade risk controls
2. **Daily Risk**: Daily loss and exposure limits
3. **Portfolio Risk**: Overall portfolio risk management
4. **Terminal Risk**: Terminal-specific risk profiles
5. **System Risk**: System-wide risk monitoring

## Trade-Level Risk Management

### Position Sizing

#### Risk-Based Position Sizing

```python
def calculate_position_size(account_balance, risk_per_trade, stop_loss_pips):
    """
    Calculate position size based on risk management rules.

    Args:
        account_balance: Current account balance
        risk_per_trade: Risk percentage per trade (default: 2%)
        stop_loss_pips: Stop loss distance in pips

    Returns:
        float: Position size in lots
    """
    # Calculate risk amount
    risk_amount = account_balance * risk_per_trade

    # Calculate pip value for the symbol
    pip_value = 10  # For BTCUSD.a (varies by symbol)

    # Calculate position size
    position_size = risk_amount / (stop_loss_pips * pip_value)

    # Apply maximum position limits
    max_position = account_balance * 0.1  # Max 10% of account
    position_size = min(position_size, max_position)

    return position_size
```

#### Fixed Position Sizing

```python
class OrderManager:
    def __init__(self, mt5_connector):
        # Load risk parameters from configuration
        self.max_risk_per_trade = TRADING_CONFIG['risk_management']['max_risk_per_trade']  # 2%
        self.lot_size = TRADING_CONFIG['lot_size']  # Fixed lot size

    def _calculate_position_size(self):
        """Calculate position size using fixed lot size approach."""
        return self.lot_size
```

### Stop Loss and Take Profit

#### Dynamic SL/TP Calculation

```python
def _calculate_dynamic_sl_tp(self, order_type, entry_price, atr_value):
    """
    Calculate dynamic stop loss and take profit based on ATR.

    Args:
        order_type: 'BUY' or 'SELL'
        entry_price: Entry price
        atr_value: Average True Range value

    Returns:
        Tuple[float, float]: Stop loss and take profit prices
    """
    if self.use_dynamic_sl_tp and atr_value is not None:
        # Use ATR-based calculation
        sl_distance = atr_value * self.atr_multiplier_sl  # Default: 1.5x ATR
        tp_distance = atr_value * self.atr_multiplier_tp  # Default: 3.0x ATR

        if order_type == 'BUY':
            sl = entry_price - sl_distance
            tp = entry_price + tp_distance
        else:  # SELL
            sl = entry_price + sl_distance
            tp = entry_price - tp_distance
    else:
        # Use fixed pip-based calculation
        point = self.symbol_info['point']
        if order_type == 'BUY':
            sl = entry_price - self.default_sl_pips * point  # Default: 200 pips
            tp = entry_price + self.default_tp_pips * point  # Default: 400 pips
        else:  # SELL
            sl = entry_price + self.default_sl_pips * point
            tp = entry_price - self.default_tp_pips * point

    return sl, tp
```

#### Trailing Stop Implementation

```python
def apply_trailing_stop(self, trail_points=100):
    """
    Apply trailing stop to all open positions.

    Args:
        trail_points: Trailing stop distance in points
    """
    positions = self.order_manager.get_open_positions()

    for position in positions:
        ticket = position['ticket']
        symbol = position['symbol']
        position_type = position['type']
        current_price = self.mt5.get_current_price(symbol)
        stop_loss = position['sl']

        # Get symbol point value
        symbol_info = self.mt5.get_symbol_info(symbol)
        point = symbol_info.get('point', 0.00001)

        # Calculate trailing distance
        trailing_distance = trail_points * point

        if position_type == 0:  # BUY position
            new_stop_loss = current_price - trailing_distance
            # Only update if new SL is higher than current SL
            if new_stop_loss > stop_loss:
                self._modify_position_stop_loss(ticket, symbol, new_stop_loss)

        elif position_type == 1:  # SELL position
            new_stop_loss = current_price + trailing_distance
            # Only update if new SL is lower than current SL
            if new_stop_loss < stop_loss:
                self._modify_position_stop_loss(ticket, symbol, new_stop_loss)
```

## Daily Risk Management

### Daily Loss Limits

```python
def _check_daily_loss_limit(self):
    """
    Check if daily loss limit has been reached.

    Returns:
        bool: True if trading should continue, False if limit reached
    """
    current_balance = self._get_account_balance()
    daily_loss_pct = (self.daily_start_balance - current_balance) / self.daily_start_balance

    if daily_loss_pct >= self.max_daily_loss:  # Default: 5%
        logger.warning(f"Daily loss limit reached: {daily_loss_pct:.2%} > {self.max_daily_loss:.2%}")
        return False

    return True
```

### Maximum Position Limits

```python
def _check_max_positions(self):
    """
    Check if maximum number of positions has been reached.

    Returns:
        bool: True if new positions can be opened, False otherwise
    """
    position_status = self.get_position_status()
    current_positions = len(position_status['positions'])

    if current_positions >= self.max_open_positions:  # Default: 3
        logger.warning(f"Maximum positions reached: {current_positions}/{self.max_open_positions}")
        return False

    return True
```

### Daily Trading Limits

```python
class OrderManager:
    def __init__(self, mt5_connector):
        # Daily tracking variables
        self.daily_start_balance = self._get_account_balance()
        self.daily_trades = 0
        self.max_daily_trades = 10  # Maximum trades per day

    def execute_order(self, order_type, volume, stop_loss=None, take_profit=None):
        # Check daily limits before placing order
        if not self._check_daily_loss_limit():
            return None

        if not self._check_max_positions():
            return None

        if self.daily_trades >= self.max_daily_trades:
            logger.warning("Daily trade limit reached")
            return None

        # Place order and update counters
        ticket = self._place_order(order_type, volume, stop_loss, take_profit)
        if ticket:
            self.daily_trades += 1

        return ticket
```

## Portfolio Risk Management

### Risk Distribution Across Terminals

```python
def get_terminal_risk_profile(self, terminal_id):
    """
    Get risk profile for a specific terminal.

    Args:
        terminal_id: Terminal ID (1-5)

    Returns:
        Dict: Risk profile parameters
    """
    terminal_config = MT5_TERMINALS[terminal_id]

    risk_profile = {
        'position_size_factor': terminal_config.get('position_size_factor', 1.0),
        'sl_multiplier': terminal_config.get('sl_multiplier', 1.5),
        'tp_multiplier': terminal_config.get('tp_multiplier', 3.0),
        'max_risk_per_trade': terminal_config.get('max_risk_per_trade', 0.02),
        'max_positions': terminal_config.get('max_positions', 1)
    }

    return risk_profile
```

### Correlation Risk Management

```python
def check_correlation_risk(self, symbol, direction):
    """
    Check correlation risk before opening new position.

    Args:
        symbol: Trading symbol
        direction: 'BUY' or 'SELL'

    Returns:
        bool: True if position can be opened, False if correlation risk too high
    """
    current_positions = self.get_open_positions()

    # Count positions in same direction
    same_direction_count = 0
    for position in current_positions:
        if position['type'] == direction:
            same_direction_count += 1

    # Maximum 2 positions in same direction
    if same_direction_count >= 2:
        logger.warning(f"Correlation risk: Already have {same_direction_count} {direction} positions")
        return False

    return True
```

## Signal-Based Risk Controls

### Signal Strength Validation

```python
def execute_trades(self, symbol, signal, atr_value=None, signals=None):
    """
    Execute trades with signal-based risk controls.

    Args:
        symbol: Trading symbol
        signal: Combined trading signal (-1 to 1)
        atr_value: ATR value for volatility assessment
        signals: Multi-timeframe signals
    """
    # Signal strength thresholds
    min_signal_strength = 0.6  # Minimum signal strength to trade
    strong_signal_threshold = 0.8  # Strong signal threshold

    # Risk controls based on signal strength
    if abs(signal) < min_signal_strength:
        logger.info(f"Signal too weak for {symbol}: {signal:.3f}")
        return

    # Adjust position size based on signal strength
    base_position_size = self._calculate_position_size()

    if abs(signal) >= strong_signal_threshold:
        # Increase position size for strong signals
        position_size = base_position_size * 1.2
    else:
        # Use base position size for moderate signals
        position_size = base_position_size

    # Apply maximum position size limit
    max_position_size = base_position_size * 1.5
    position_size = min(position_size, max_position_size)
```

### Multi-Timeframe Risk Validation

```python
def validate_multi_timeframe_risk(self, signals):
    """
    Validate risk across multiple timeframes.

    Args:
        signals: Dictionary of signals for each timeframe

    Returns:
        bool: True if risk is acceptable, False otherwise
    """
    # Check signal alignment across timeframes
    signal_directions = []
    for timeframe, signal in signals.items():
        if abs(signal) > 0.3:  # Only consider significant signals
            signal_directions.append(1 if signal > 0 else -1)

    # Require at least 60% alignment
    if len(signal_directions) > 0:
        alignment = abs(sum(signal_directions)) / len(signal_directions)
        if alignment < 0.6:
            logger.warning("Poor signal alignment across timeframes")
            return False

    return True
```

## Risk Configuration

### Risk Parameters

```python
# Risk management configuration
RISK_CONFIG = {
    "max_risk_per_trade": 0.02,      # 2% of account per trade
    "max_open_positions": 3,          # Maximum 3 open positions
    "max_daily_loss": 0.05,          # 5% daily loss limit
    "max_daily_trades": 10,          # Maximum 10 trades per day
    "position_size_factor": 1.0,     # Position size multiplier
    "sl_multiplier": 1.5,            # Stop loss ATR multiplier
    "tp_multiplier": 3.0,            # Take profit ATR multiplier
    "trail_points": 100,             # Trailing stop distance
    "correlation_limit": 2           # Maximum correlated positions
}
```

### Terminal-Specific Risk Profiles

```python
# Terminal risk profiles
TERMINAL_RISK_PROFILES = {
    1: {  # ARIMA Terminal
        "max_risk_per_trade": 0.015,  # Conservative: 1.5%
        "position_size_factor": 0.8,
        "sl_multiplier": 2.0,         # Wider stops for statistical model
        "max_positions": 1
    },
    2: {  # LSTM Terminal
        "max_risk_per_trade": 0.02,   # Standard: 2%
        "position_size_factor": 1.0,
        "sl_multiplier": 1.5,
        "max_positions": 2
    },
    3: {  # TFT Terminal
        "max_risk_per_trade": 0.025,  # Aggressive: 2.5%
        "position_size_factor": 1.2,
        "sl_multiplier": 1.2,         # Tighter stops for advanced model
        "max_positions": 2
    },
    4: {  # LSTM+ARIMA Ensemble
        "max_risk_per_trade": 0.02,
        "position_size_factor": 1.1,
        "sl_multiplier": 1.5,
        "max_positions": 2
    },
    5: {  # TFT+ARIMA Ensemble
        "max_risk_per_trade": 0.025,
        "position_size_factor": 1.3,
        "sl_multiplier": 1.3,
        "max_positions": 2
    }
}
```

Last updated: 2025-06-06 16:35:00
