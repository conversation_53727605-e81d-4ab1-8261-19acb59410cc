"""
Initialize Terminal Models Script.
This script initializes models for all terminals based on the standardized model assignments:
- Terminal 1: ARIMA
- Terminal 2: LSTM
- Terminal 3: TFT
- Terminal 4: LSTM + ARIMA ensemble
- Terminal 5: TFT + ARIMA ensemble

Usage:
    python scripts/initialize_terminal_models.py [options]

Options:
    --terminals 1 3 5    Initialize specific terminals (default: all terminals)
    --symbol ETHUSD.a    Initialize for a specific symbol (default: BTCUSD.a)
    --timeframes M5 H1   Initialize for specific timeframes (default: all timeframes)
    --force-retrain      Force retraining of existing models
"""
import argparse
import logging
import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from model.model_manager import ModelManager
from data.data_manager import DataManager
from utils.path_utils import (
    get_terminal_model_type,
    TERMINAL_MODEL_TYPES
)

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/initialize_models.log')
    ]
)
logger = logging.getLogger('scripts.initialize_terminal_models')

def main():
    """
    Main function to initialize models for all terminals.

    This script enforces the standardized terminal-model mapping:
    - Terminal 1: ARIMA
    - Terminal 2: LSTM
    - Terminal 3: TFT
    - Terminal 4: LSTM + ARIMA ensemble
    - Terminal 5: TFT + ARIMA ensemble
    """
    parser = argparse.ArgumentParser(description='Initialize models for all terminals')
    parser.add_argument('--terminals', nargs='+', type=int, help='Terminal IDs to initialize')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Symbol to initialize')
    parser.add_argument('--timeframes', nargs='+', type=str, help='Timeframes to initialize')
    parser.add_argument('--force-retrain', action='store_true', help='Force retraining of existing models')
    parser.add_argument('--validate-paths', action='store_true', help='Validate model paths after initialization')

    args = parser.parse_args()

    # Create data and model managers
    data_manager = DataManager()
    model_manager = ModelManager()

    # Get terminals to initialize
    if args.terminals:
        terminals = args.terminals
        # Validate terminal IDs
        for terminal_id in terminals:
            if terminal_id not in TERMINAL_MODEL_TYPES:
                logger.error(f"Invalid terminal ID: {terminal_id}. Valid terminal IDs are: {list(TERMINAL_MODEL_TYPES.keys())}")
                return
    else:
        terminals = list(TERMINAL_MODEL_TYPES.keys())

    # Get timeframes to initialize
    if args.timeframes:
        timeframes = args.timeframes
        # Validate timeframes
        for timeframe in timeframes:
            if timeframe not in ['M5', 'M15', 'M30', 'H1', 'H4']:
                logger.error(f"Invalid timeframe: {timeframe}. Valid timeframes are: M5, M15, M30, H1, H4")
                return
    else:
        timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']

    # Display terminal-model mapping
    logger.info("Terminal-Model Mapping:")
    for terminal_id, model_type in TERMINAL_MODEL_TYPES.items():
        if terminal_id in terminals:
            logger.info(f"Terminal {terminal_id}: {model_type.upper()}")

    # Initialize models
    logger.info(f"Initializing models for terminals {terminals}, symbol {args.symbol}, timeframes {timeframes}")

    # Check if data is available for all timeframes
    missing_data = False
    for timeframe in timeframes:
        for terminal_id in terminals:
            df = data_manager.load_historical_data(
                symbol=args.symbol,
                timeframe=timeframe,
                terminal_id=terminal_id
            )

            if df is None or len(df) == 0:
                logger.error(f"No data found for terminal {terminal_id}, symbol {args.symbol}, timeframe {timeframe}")
                missing_data = True

    if missing_data:
        logger.error("Please collect data first using the data collection script")
        logger.error("Run: python scripts/collect_historical_data.py --symbol " + args.symbol + " --timeframes " + " ".join(timeframes))
        return

    # Initialize models
    results = model_manager.initialize_terminal_models(
        symbol=args.symbol,
        timeframes=timeframes,
        terminals=terminals,
        force_retrain=args.force_retrain
    )

    # Print results
    logger.info("\nModel initialization results:")
    for terminal_id, terminal_results in results.items():
        model_type = get_terminal_model_type(terminal_id)
        logger.info(f"Terminal {terminal_id} ({model_type.upper()}):")
        for timeframe, success in terminal_results.items():
            status = "SUCCESS" if success else "FAILED"
            logger.info(f"  {timeframe}: {status}")

    # Print summary
    total_models = len(terminals) * len(timeframes)
    successful_models = sum(sum(1 for success in terminal_results.values() if success) for terminal_results in results.values())
    success_rate = (successful_models / total_models) * 100 if total_models > 0 else 0

    logger.info(f"\nSummary: Successfully initialized {successful_models} out of {total_models} models ({success_rate:.1f}%)")

    # Validate model paths if requested
    if args.validate_paths:
        logger.info("\nValidating model paths...")
        import os
        from utils.path_utils import get_model_path, get_metrics_path

        for terminal_id in terminals:
            model_type = get_terminal_model_type(terminal_id)
            logger.info(f"Terminal {terminal_id} ({model_type.upper()}):")

            for timeframe in timeframes:
                model_path = get_model_path(args.symbol, timeframe, model_type)
                metrics_path = get_metrics_path(args.symbol, timeframe, model_type)

                model_exists = os.path.exists(model_path)
                metrics_exist = os.path.exists(metrics_path)

                model_status = "EXISTS" if model_exists else "MISSING"
                metrics_status = "EXISTS" if metrics_exist else "MISSING"

                logger.info(f"  {timeframe}:")
                logger.info(f"    Model: {model_status} - {model_path}")
                logger.info(f"    Metrics: {metrics_status} - {metrics_path}")

    # Print next steps
    logger.info("\nNext steps:")
    logger.info("1. Verify model performance using the model evaluation script:")
    logger.info(f"   python scripts/evaluate_models.py --symbol {args.symbol} --timeframes {' '.join(timeframes)}")
    logger.info("2. Start trading with the trained models:")
    logger.info(f"   python scripts/run_trading_bot.py --symbol {args.symbol} --timeframes {' '.join(timeframes)}")

if __name__ == '__main__':
    main()
