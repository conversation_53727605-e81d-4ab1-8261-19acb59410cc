"""
Unified Trading Bot Module.
This module implements a unified trading bot that can operate in single-terminal
or multi-terminal mode, supporting different model types for each terminal.
It integrates all components for data collection, model management, trading,
and performance monitoring.
"""
import logging
import time
import os
import signal
import sys
import traceback
import json
from typing import Dict, List, Optional, Tuple, Union, Any
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
import MetaTrader5 as mt5

from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from trading.trading_strategy import TradingStrategy
from trading.terminal_manager import TerminalManager
from model.model_manager import ModelManager
from model.model_loader import ModelLoader
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator, WalkForwardOptimizer
from utils.path_utils import get_model_path, get_metrics_path, get_terminal_model_type
from monitoring.performance_monitor import PerformanceMonitor
from monitoring.dashboard import Dashboard
from utils.logger import setup_logger
from utils.error_handler import ErrorTracker, handle_exceptions, retry
from utils.helpers import get_current_time, get_current_timestamp, format_timestamp
from utils.mt5_initializer import initialize_mt5_once, initialize_all_terminals, get_terminal_status
from utils.path_utils import VALID_TIMEFRAMES
from config.trading_config import TRADING_CONFIG
from config.credentials import MT5_TERMINALS

class TradingBot:
    """
    Unified Trading Bot class that integrates all components.
    Can operate in single-terminal or multi-terminal mode.
    """
    def __init__(self, terminal_id: Optional[int] = None, multi_terminal: bool = False, symbol: str = None):
        """
        Initialize TradingBot.

        Args:
            terminal_id: MT5 terminal ID to connect to (None for multi-terminal mode)
            multi_terminal: Whether to operate in multi-terminal mode
            symbol: Symbol to trade (defaults to TRADING_CONFIG['symbol'])
        """
        # Set up logger
        self.logger = setup_logger('trading_bot')
        self.logger.info("Initializing Trading Bot...")

        # Initialize error tracker
        self.error_tracker = ErrorTracker()

        # Initialize MT5 connector
        self.mt5_connector = MT5Connector()

        # Initialize all MT5 terminals in portable mode
        self.logger.info("Initializing all MT5 terminals in portable mode...")
        connected_terminals = self.mt5_connector.connection_manager.initialize_all_terminals()
        self.logger.info(f"Successfully initialized {len(connected_terminals)} MT5 terminals")

        # Set multi-terminal mode
        self.multi_terminal = multi_terminal
        self.terminal_id = terminal_id
        self.symbol = symbol or TRADING_CONFIG.get('symbol', 'BTCUSD.a')
        self.timeframes = TRADING_CONFIG.get('timeframes', ['M5', 'M15', 'M30', 'H1', 'H4'])

        # Initialize components
        self.data_collector = DataCollector(self.mt5_connector)
        self.feature_engineer = FeatureEngineer(data_collector=self.data_collector)
        self.model_manager = ModelManager()
        self.model_evaluator = ModelEvaluator()
        self.performance_monitor = PerformanceMonitor()
        self.dashboard = Dashboard(self.performance_monitor)

        # Initialize state variables
        self.is_running = False
        self.trading_thread = None
        self.monitoring_thread = None
        self.last_model_update = get_current_timestamp()
        self.last_performance_update = get_current_timestamp()
        self.predictions = {}
        self.last_prediction_time = {}

        # Load performance data
        self.performance_monitor.load_data()

        if self.multi_terminal:
            # Initialize terminal manager for multi-terminal mode
            self.terminal_manager = TerminalManager(
                mt5_connector=self.mt5_connector,
                model_manager=self.model_manager,
                data_collector=self.data_collector,
                feature_engineer=self.feature_engineer,
                model_evaluator=self.model_evaluator
            )
            self.logger.info("Initialized in multi-terminal mode")
        else:
            # Connect to specific terminal for single-terminal mode
            if self.terminal_id is None:
                self.terminal_id = 1  # Default to terminal 1

            if not self.mt5_connector.connect_terminal(self.terminal_id):
                self.logger.error(f"Failed to connect to MT5 terminal {self.terminal_id}")
                raise ConnectionError(f"Failed to connect to MT5 terminal {self.terminal_id}")

            # Initialize order manager for this terminal
            self.order_manager = OrderManager(self.mt5_connector)

            # Initialize trading strategy
            self.trading_strategy = TradingStrategy(
                mt5_connector=self.mt5_connector,
                order_manager=self.order_manager,
                model_manager=self.model_manager,
                data_collector=self.data_collector,
                feature_engineer=self.feature_engineer,
                model_evaluator=self.model_evaluator
            )

            # Load model assignments for this terminal
            self.model_assignments = self._load_model_assignments()

            self.logger.info(f"Initialized in single-terminal mode for Terminal {self.terminal_id}")

        # Load models
        self._load_models()

        self.logger.info("Trading Bot initialized successfully")

    @handle_exceptions
    def start(self):
        """Start the trading bot."""
        if self.is_running:
            self.logger.warning("Trading bot is already running")
            return

        self.logger.info("Starting Trading Bot...")

        # Set running flag
        self.is_running = True

        # Start dashboard
        self.dashboard.start()

        # Start trading thread
        self.trading_thread = threading.Thread(target=self._trading_loop)
        self.trading_thread.daemon = True
        self.trading_thread.start()

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()

        self.logger.info("Trading Bot started")

    @handle_exceptions
    def stop(self):
        """Stop the trading bot."""
        if not self.is_running:
            self.logger.warning("Trading bot is not running")
            return

        self.logger.info("Stopping Trading Bot...")

        # Clear running flag
        self.is_running = False

        # Wait for threads to finish
        if self.trading_thread:
            self.trading_thread.join(timeout=10)

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10)

        # Stop dashboard
        self.dashboard.stop()

        # Disconnect from MT5
        self.mt5_connector.disconnect_terminal()

        self.logger.info("Trading Bot stopped")

    def _trading_loop(self):
        """Trading loop."""
        self.logger.info("Trading loop started")

        while self.is_running:
            try:
                if self.multi_terminal:
                    # Run trading cycle for all terminals
                    results = self.terminal_manager.run_all_terminals()

                    # Log results
                    success_count = sum(1 for success in results.values() if success)
                    self.logger.info(f"Trading cycle completed: {success_count}/{len(results)} terminals successful")
                else:
                    # Run trading cycle for single terminal
                    self.trading_strategy.run_trading_cycle()

                # Check if models need to be updated
                current_time = get_current_timestamp()
                if current_time - self.last_model_update > TRADING_CONFIG['model']['retraining_interval'] * 3600:
                    self.logger.info("Updating models...")

                    if self.multi_terminal:
                        self.terminal_manager.update_all_models()
                    else:
                        self.trading_strategy.update_models()

                    self.last_model_update = current_time

                # Sleep for a while
                time.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                self.error_tracker.add_error("TradingError", str(e), traceback.format_exc())
                time.sleep(60)  # Wait a bit before retrying

        self.logger.info("Trading loop stopped")

    def _monitoring_loop(self):
        """Monitoring loop."""
        self.logger.info("Monitoring loop started")

        while self.is_running:
            try:
                # Update performance metrics
                current_time = get_current_timestamp()
                if current_time - self.last_performance_update > 300:  # Update every 5 minutes
                    self._update_performance()
                    self.last_performance_update = current_time

                # Sleep for a while
                time.sleep(10)

            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {str(e)}")
                self.error_tracker.add_error("MonitoringError", str(e), traceback.format_exc())
                time.sleep(60)  # Wait a bit before retrying

        self.logger.info("Monitoring loop stopped")

    def _update_performance(self):
        """Update performance metrics for all terminals."""
        try:
            if self.multi_terminal:
                # Get active terminals
                active_terminals = self.terminal_manager.get_active_terminals()

                for terminal_id in active_terminals:
                    # Connect to terminal
                    if not self.mt5_connector.connect_terminal(terminal_id):
                        self.logger.error(f"Failed to connect to terminal {terminal_id}")
                        continue

                    # Get terminal info
                    terminal_info = self.terminal_manager.get_terminal_status(terminal_id)

                    # Get account info
                    account_info = self.mt5_connector.get_account_info()
                    if account_info is None:
                        self.logger.error(f"Failed to get account info for terminal {terminal_id}")
                        continue

                    # Add equity point
                    self.performance_monitor.add_equity_point(
                        timestamp=pd.Timestamp.now(),
                        equity=account_info['equity'],
                        balance=account_info['balance']
                    )

                    # Get open positions
                    positions = self.mt5_connector.get_open_positions()
                    if positions is not None:
                        self.logger.info(f"Terminal {terminal_id} ({terminal_info['name']}) - Open positions: {len(positions)}")
                        for pos in positions:
                            self.logger.info(f"  {pos['symbol']} {pos['type']} {pos['volume']} lots, Profit: {pos['profit']}")

                    # Get daily performance
                    order_manager = self.terminal_manager.order_managers.get(terminal_id)
                    if order_manager:
                        daily_performance = order_manager.get_daily_performance()
                        self.logger.info(f"Terminal {terminal_id} ({terminal_info['name']}) - Daily performance: {daily_performance}")
            else:
                # Get account info
                account_info = self.mt5_connector.get_account_info()
                if account_info is None:
                    self.logger.error("Failed to get account info")
                    return

                # Add equity point
                self.performance_monitor.add_equity_point(
                    timestamp=pd.Timestamp.now(),
                    equity=account_info['equity'],
                    balance=account_info['balance']
                )

                # Get open positions
                positions = self.mt5_connector.get_open_positions()
                if positions is not None:
                    self.logger.info(f"Terminal {self.terminal_id} - Open positions: {len(positions)}")
                    for pos in positions:
                        self.logger.info(f"  {pos['symbol']} {pos['type']} {pos['volume']} lots, Profit: {pos['profit']}")

                # Get daily performance
                daily_performance = self.order_manager.get_daily_performance()
                self.logger.info(f"Terminal {self.terminal_id} - Daily performance: {daily_performance}")

        except Exception as e:
            self.logger.error(f"Error updating performance: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    @handle_exceptions
    def train_models(self, start_date: str, end_date: str = None):
        """
        Train models for all timeframes.

        Args:
            start_date: Start date for training data
            end_date: End date for training data
        """
        self.logger.info(f"Training models from {start_date} to {end_date or 'now'}...")

        if self.multi_terminal:
            # Train models for all terminals
            results = self.terminal_manager.train_all_models(start_date, end_date)

            # Log results
            success_count = sum(1 for success in results.values() if success)
            self.logger.info(f"Model training completed: {success_count}/{len(results)} models trained successfully")
        else:
            # Train models for single terminal
            self.trading_strategy.train_models(start_date, end_date)
            self.logger.info("Models trained successfully")

    @handle_exceptions
    def run_backtest(self, start_date: str, end_date: str = None):
        """
        Run backtest.

        Args:
            start_date: Start date for backtest
            end_date: End date for backtest

        Returns:
            pd.DataFrame: Backtest results
        """
        self.logger.info(f"Running backtest from {start_date} to {end_date or 'now'}...")
        results = self.trading_strategy.backtest(start_date, end_date)
        self.logger.info("Backtest completed")
        return results

    @handle_exceptions
    def run_walk_forward_optimization(self, start_date: str, end_date: str = None):
        """
        Run walk-forward optimization.

        Args:
            start_date: Start date for optimization
            end_date: End date for optimization
        """
        self.logger.info(f"Running walk-forward optimization from {start_date} to {end_date or 'now'}...")

        # Get data
        df = self.data_collector.get_historical_data(
            timeframe='M30',
            start_date=start_date,
            end_date=end_date
        )

        if df is None or len(df) == 0:
            self.logger.error("Failed to get data for walk-forward optimization")
            return

        # Create features
        df_features = self.feature_engineer.create_features(df)

        # Initialize walk-forward optimizer
        optimizer = WalkForwardOptimizer()

        # Generate windows
        windows = optimizer.generate_windows(df_features, date_column=None)

        # Plot windows
        optimizer.plot_windows(df_features, windows, 'monitoring/plots/walk_forward_windows.png')

        self.logger.info("Walk-forward optimization completed")

    @handle_exceptions
    def generate_performance_report(self):
        """Generate a performance report."""
        self.logger.info("Generating performance report...")
        self.performance_monitor.generate_performance_report()
        self.logger.info("Performance report generated")

    @handle_exceptions
    def get_status(self) -> Dict:
        """
        Get the current status of the trading bot.

        Returns:
            Dict: Status information
        """
        # Get error counts
        error_counts = self.error_tracker.get_error_counts()

        if self.multi_terminal:
            # Get terminal statuses
            terminal_statuses = {}
            for terminal_id in self.terminal_manager.get_active_terminals():
                terminal_statuses[terminal_id] = self.terminal_manager.get_terminal_status(terminal_id)

            # Create status
            status = {
                'is_running': self.is_running,
                'mode': 'multi-terminal',
                'terminal_statuses': terminal_statuses,
                'error_counts': error_counts,
                'last_model_update': format_timestamp(self.last_model_update),
                'last_performance_update': format_timestamp(self.last_performance_update)
            }
        else:
            # Get account info
            account_info = self.mt5_connector.get_account_info()

            # Get open positions
            positions = self.mt5_connector.get_open_positions()

            # Get daily performance
            daily_performance = self.order_manager.get_daily_performance()

            # Create status
            status = {
                'is_running': self.is_running,
                'mode': 'single-terminal',
                'terminal_id': self.terminal_id,
                'account_info': account_info,
                'open_positions': positions,
                'daily_performance': daily_performance,
                'error_counts': error_counts,
                'last_model_update': format_timestamp(self.last_model_update),
                'last_performance_update': format_timestamp(self.last_performance_update)
            }

        return status

    def _load_model_assignments(self) -> Dict[str, Dict[str, str]]:
        """
        Load model assignments for this terminal.

        Returns:
            Dict[str, Dict[str, str]]: Model assignments for each timeframe
        """
        # Try to load from assignments.json
        assignments_path = 'model/assignments.json'

        if os.path.exists(assignments_path):
            try:
                with open(assignments_path, 'r') as f:
                    assignments = json.load(f)

                # Get assignments for this terminal
                if str(self.terminal_id) in assignments:
                    terminal_assignments = assignments[str(self.terminal_id)]

                    # Get model assignments for this symbol
                    if 'models' in terminal_assignments and self.symbol in terminal_assignments['models']:
                        self.logger.info(f"Loaded model assignments for Terminal {self.terminal_id}")
                        return terminal_assignments['models'][self.symbol]
            except Exception as e:
                self.logger.error(f"Error loading model assignments: {str(e)}")

        # If no assignments found, use default assignments based on terminal ID
        self.logger.warning(f"No model assignments found for Terminal {self.terminal_id}, using defaults")

        # Default model type based on terminal ID
        model_types = {
            1: 'arima',
            2: 'lstm',
            3: 'tft',
            4: 'lstm_arima',
            5: 'tft_arima'
        }

        model_type = model_types.get(self.terminal_id, 'arima')

        # Create default assignments
        default_assignments = {}
        for timeframe in self.timeframes:
            default_assignments[timeframe] = {
                'model_path': get_model_path(self.symbol, timeframe, model_type),
                'metrics_path': get_metrics_path(self.symbol, timeframe, model_type),
                'is_best': False,
                'note': f"Default {model_type} model for {self.symbol} ({timeframe})"
            }

        return default_assignments

    def _load_models(self):
        """Load models for all timeframes."""
        self.logger.info("Loading models...")

        if self.multi_terminal:
            # In multi-terminal mode, load models for all terminals
            for terminal_id in range(1, 6):  # Terminals 1-5
                model_type = get_terminal_model_type(terminal_id)

                for timeframe in self.timeframes:
                    model_name = f"{self.symbol}_{timeframe}_{model_type}"

                    # Try to load the model
                    if self.model_manager.load_model(model_name):
                        self.logger.info(f"Successfully loaded model {model_name} for Terminal {terminal_id}")
                    else:
                        self.logger.warning(f"Failed to load model {model_name} for Terminal {terminal_id}")
        else:
            # In single-terminal mode, load models for this terminal
            if hasattr(self, 'model_assignments'):
                # Load models based on assignments
                self.models = {}

                for timeframe, assignment in self.model_assignments.items():
                    model_path = assignment['model_path']

                    try:
                        # Extract model name from path for loading
                        model_name = os.path.splitext(os.path.basename(model_path))[0]

                        # Load model using the correct method
                        if self.model_manager.load_model(model_name, model_path=model_path):
                            # Get the loaded model from the model manager's models dictionary
                            self.models[timeframe] = self.model_manager.models.get(model_name)
                            self.logger.info(f"Loaded model for {timeframe} from {model_path}")
                        else:
                            self.logger.error(f"Failed to load model for {timeframe} from {model_path}")
                    except Exception as e:
                        self.logger.error(f"Error loading model for {timeframe}: {str(e)}")
                        import traceback
                        self.logger.error(traceback.format_exc())
            else:
                # Load models by name
                for timeframe in self.timeframes:
                    model_name = f"{self.symbol}_{timeframe}"

                    # Try to load the model
                    if self.model_manager.load_model(model_name):
                        self.logger.info(f"Successfully loaded model {model_name}")
                    else:
                        self.logger.warning(f"Failed to load model {model_name}, it may need to be trained")

                # Check if M5 model is loaded
                m5_model_name = f"{self.symbol}_M5"
                if m5_model_name not in self.model_manager.list_models():
                    self.logger.warning(f"M5 model {m5_model_name} is not loaded, trading decisions may be suboptimal")

def signal_handler(sig, frame):
    """Signal handler for graceful shutdown."""
    print("Shutting down...")
    if 'bot' in globals():
        bot.stop()
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

if __name__ == "__main__":
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description='Unified Trading Bot')
    parser.add_argument('--terminal', type=int, default=None, help='MT5 terminal ID (1-5, None for multi-terminal mode)')
    parser.add_argument('--multi-terminal', action='store_true', help='Run in multi-terminal mode')
    parser.add_argument('--symbol', type=str, default=None, help='Symbol to trade (default from config)')
    parser.add_argument('--train', action='store_true', help='Train models')
    parser.add_argument('--backtest', action='store_true', help='Run backtest')
    parser.add_argument('--start-date', type=str, default='2022-01-01', help='Start date for training/backtest')
    parser.add_argument('--end-date', type=str, default=None, help='End date for training/backtest')
    parser.add_argument('--report', action='store_true', help='Generate performance report')
    parser.add_argument('--optimize', action='store_true', help='Run walk-forward optimization')

    args = parser.parse_args()

    # Determine mode
    multi_terminal = args.multi_terminal or args.terminal is None
    terminal_id = None if multi_terminal else args.terminal

    # Create trading bot
    bot = TradingBot(
        terminal_id=terminal_id,
        multi_terminal=multi_terminal,
        symbol=args.symbol
    )

    # Run commands
    if args.train:
        bot.train_models(args.start_date, args.end_date)

    if args.backtest:
        results = bot.run_backtest(args.start_date, args.end_date)
        results.to_csv('monitoring/data/backtest_results.csv')

    if args.report:
        bot.generate_performance_report()

    if args.optimize:
        bot.run_walk_forward_optimization(args.start_date, args.end_date)

    # Start bot if no commands were given
    if not (args.train or args.backtest or args.report or args.optimize):
        bot.start()

        try:
            # Keep the main thread alive
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            bot.stop()
