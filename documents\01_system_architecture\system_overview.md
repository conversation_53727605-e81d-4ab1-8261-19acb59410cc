# System Architecture

This document provides an overview of the trading system architecture based on the actual codebase implementation.

## Overview

The trading system is a multi-terminal, multi-model algorithmic trading system that uses 5 MT5 terminals, each specializing in a specific model type:
- **Terminal 1**: ARIMA (Statistical time series forecasting)
- **Terminal 2**: LSTM (Deep learning sequence modeling)
- **Terminal 3**: TFT (Transformer-based forecasting)
- **Terminal 4**: LSTM+ARIMA ensemble (Hybrid approach)
- **Terminal 5**: TFT+ARIMA ensemble (Advanced hybrid approach)

The system follows a modular architecture with clear separation of concerns and standardized interfaces between components.

## System Components

The trading system consists of the following main components:

### 1. Core Trading Infrastructure

#### MT5 Connection Layer
- **MT5Connector**: Handles connection to MT5 terminals
- **MT5Initializer**: Manages terminal initialization and configuration
- **TerminalManager**: Coordinates multiple terminals for multi-timeframe trading

#### Order Management
- **OrderManager**: Handles order execution and position management
- **PositionManager**: Manages open positions and risk controls
- **SymbolManager**: Manages trading symbols and their configurations

### 2. Data Management Layer

#### Data Collection
- **DataCollector**: Collects historical OHLCV data from MT5 terminals
- **DataManager**: Manages data storage, retrieval, and versioning
- **TechnicalIndicators**: Calculates technical indicators for feature engineering

#### Data Validation
- **DataValidator**: Validates data quality and completeness
- **FeatureValidator**: Validates feature consistency and distributions
- **DataQualityManager**: Comprehensive data quality management

Key features:
- Standardized path structure for data storage
- Multi-terminal data collection and merging
- Real-time data validation and quality checks
- Parquet-based storage for efficient data handling

### 3. Model Training and Management

#### Model Training
- **ModelManager**: Central model management and training coordination
- **ModelTrainer**: LSTM model training with PyTorch
- **TFTModelTrainer**: Temporal Fusion Transformer training
- **ARIMAModelTrainer**: ARIMA statistical model training
- **EnsembleModel**: Combines multiple models for improved performance

#### Model Evaluation
- **ModelEvaluator**: Comprehensive model performance evaluation
- **ModelComparison**: Compares different model types and configurations
- **WalkForwardOptimizer**: Implements walk-forward optimization
- **CrossValidation**: Time-series cross-validation for model selection

#### Model Storage and Loading
- **ModelLoader**: Secure model loading with PyTorch 2.6+ compatibility
- **TorchSecurity**: Security utilities for safe model serialization
- Standardized path structure for model storage by type and terminal

### 4. Trading Strategy and Execution

#### Strategy Implementation
- **TradingStrategy**: Core trading strategy implementation
- **SignalGenerator**: Generates trading signals from model predictions
- **RiskManager**: Implements risk management rules and position sizing
- **MultiTimeframeStrategy**: Coordinates signals across multiple timeframes

#### Feature Engineering
- **FeatureEngineer**: Creates technical indicators and features
- **FeatureSelection**: Selects optimal features for model training
- **DataProcessor**: Preprocesses data for model consumption

### 5. High-Level Coordination

#### Trading Bot
- **TradingBot**: Main orchestrator for the entire trading system
- **TerminalManager**: Manages multiple MT5 terminals
- **PerformanceMonitor**: Real-time performance monitoring
- **Dashboard**: Web-based monitoring and visualization interface

#### Monitoring and Analysis
- **PerformanceVisualizer**: Creates performance charts and reports
- **ModelDecayMonitor**: Detects when models need retraining
- **TimeSeriesVisualizer**: Visualizes market data and predictions

## Data Flow

The data flow in the trading system is as follows:

1. **Data Collection**:
   - MT5 terminals collect historical OHLCV data
   - Data is processed with technical indicators
   - Processed data is saved using the standardized path structure

2. **Model Training**:
   - Processed data is loaded
   - Data is prepared for training (splitting into train/val/test sets)
   - Models are trained
   - Trained models are saved using the standardized path structure

3. **Prediction**:
   - Latest data is loaded
   - Trained models are loaded
   - Models make predictions
   - Predictions are used to generate trading signals

4. **Trading**:
   - Trading signals are generated
   - Risk management rules are applied
   - Trades are executed through MT5 terminals
   - Open positions and orders are monitored

## Directory Structure

The trading system has the following directory structure:

```
MT5 Trading System/
├── analysis/                # Market analysis and feature engineering
│   ├── feature_engineering.py  # Feature engineering and preprocessing
│   ├── feature_selection.py    # Feature selection algorithms
│   └── visualization/       # Advanced visualization tools
├── config/                  # Configuration files
│   ├── central_config.py    # Central configuration management
│   ├── credentials.py       # Terminal credentials
│   ├── trading_config.py    # Trading configuration
│   └── logging_config.py    # Logging configuration
├── data/                    # Data collection and processing
│   ├── data_collector.py    # Data collection from MT5
│   ├── data_manager.py      # Data management with Parquet storage
│   ├── technical_indicators.py # Technical indicators calculation
│   ├── storage/             # Data storage directories
│   └── validation/          # Data validation tools
├── model/                   # Model implementation and training
│   ├── arima_model.py       # ARIMA statistical model
│   ├── lstm_model.py        # LSTM neural network model
│   ├── tft_model.py         # Temporal Fusion Transformer
│   ├── ensemble_model.py    # Ensemble model combinations
│   ├── model_trainer.py     # Model training coordination
│   ├── model_evaluator.py   # Model evaluation and testing
│   ├── model_manager.py     # Model management
│   ├── saved_models/        # Saved model files
│   └── visualizations/      # Model visualization outputs
├── trading/                 # Trading strategy and execution
│   ├── mt5_connector.py     # MT5 connection management
│   ├── trading_strategy.py  # Core trading strategy
│   ├── order_manager.py     # Order execution and management
│   ├── terminal_manager.py  # Multi-terminal coordination
│   └── symbol_manager.py    # Symbol configuration management
├── utils/                   # Utility functions
│   ├── mt5_initializer.py   # MT5 initialization
│   ├── path_utils.py        # Standardized path utilities
│   ├── error_handler.py     # Error handling and recovery
│   └── torch_security.py    # PyTorch security utilities
├── monitoring/              # Performance monitoring
│   ├── performance_monitor.py # Real-time performance monitoring
│   └── dashboard.py         # Web-based dashboard
├── scripts/                 # Utility scripts
│   ├── collect_historical_data.py # Data collection script
│   ├── initialize_terminal_models.py # Model initialization
│   └── verify_paths.py      # Path verification
├── documents/               # Documentation (5 organized subfolders)
├── dashboards/              # Generated dashboard files
├── logs/                    # Log files
├── trading_bot.py           # Main trading bot class
├── run_trading_bot.py       # Main execution script
└── train_models.py          # Model training script
```

Last updated: 2025-06-06 15:30:00
