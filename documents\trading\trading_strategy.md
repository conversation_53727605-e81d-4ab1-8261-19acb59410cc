# Trading Strategy Module

## Overview

The Trading Strategy module implements trading strategies based on multiple model predictions (LSTM, TFT, ARIMA, and ensembles). It is implemented in the `trading_strategy.py` file.

## TradingStrategy Class

The `TradingStrategy` class is the main class in this module. It provides methods for generating trading signals and executing trades.

### Initialization

```python
from trading.trading_strategy import TradingStrategy
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_manager import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator

# Initialize components
mt5_connector = MT5Connector()
mt5_connector.connect_terminal(terminal_id=1)
order_manager = OrderManager(mt5_connector)
model_manager = ModelManager()
data_collector = DataCollector(mt5_connector)
feature_engineer = FeatureEngineer(data_collector=data_collector)
model_evaluator = ModelEvaluator()

# Initialize trading strategy
strategy = TradingStrategy(
    mt5_connector=mt5_connector,
    order_manager=order_manager,
    model_manager=model_manager,
    data_collector=data_collector,
    feature_engineer=feature_engineer,
    model_evaluator=model_evaluator
)
```

**Parameters**:
- `mt5_connector` (MT5Connector): MT5Connector instance
- `order_manager` (OrderManager): OrderManager instance
- `model_manager` (ModelManager): ModelManager instance
- `data_collector` (DataCollector): DataCollector instance
- `feature_engineer` (FeatureEngineer): FeatureEngineer instance
- `model_evaluator` (ModelEvaluator): ModelEvaluator instance

### Methods

#### train_models

Train models for all timeframes.

```python
strategy.train_models(
    start_date="2022-01-01",
    end_date="2022-12-31"
)
```

**Parameters**:
- `start_date` (str): Start date for training data
- `end_date` (str): End date for training data

#### update_models

Update models with recent data.

```python
strategy.update_models(lookback_days=30)
```

**Parameters**:
- `lookback_days` (int): Number of days to look back for updating

#### generate_signals

Generate trading signals for all timeframes.

```python
signals = strategy.generate_signals()
```

**Returns**:
- `Dict[str, float]`: Dictionary of signals for each timeframe

#### combine_signals

Combine signals from multiple timeframes.

```python
combined_signal = strategy.combine_signals(signals)
```

**Parameters**:
- `signals` (Dict[str, float]): Dictionary of signals for each timeframe

**Returns**:
- `float`: Combined signal

#### execute_trades

Execute trades based on the signal.

```python
strategy.execute_trades(signal=0.75, atr_value=0.02)
```

**Parameters**:
- `signal` (float): Trading signal
- `atr_value` (float): ATR value for dynamic SL/TP calculation

#### apply_trailing_stop

Apply trailing stop to open positions.

```python
strategy.apply_trailing_stop(trail_points=100)
```

**Parameters**:
- `trail_points` (int): Trailing stop distance in points

#### run_trading_cycle

Run a complete trading cycle.

```python
strategy.run_trading_cycle()
```

#### backtest

Run backtest on historical data.

```python
results = strategy.backtest(
    start_date="2022-01-01",
    end_date="2022-12-31"
)
```

**Parameters**:
- `start_date` (str): Start date for backtest
- `end_date` (str): End date for backtest

**Returns**:
- `pd.DataFrame`: Backtest results

## Trading Strategy Logic

The trading strategy implemented in the TradingStrategy class works as follows:

### Signal Generation

1. **Data Collection**:
   - Get latest data for each timeframe
   - Create features for the model
   - Normalize features

2. **Model Prediction**:
   - Get the latest sequence for each timeframe
   - Generate predictions using the LSTM model
   - Convert predictions to signals

3. **Timeframe Specialization**:
   - Each terminal specializes in a specific timeframe:
     - Terminal 1 (Pepperstone Demo 1): M5 timeframe with conservative risk profile
     - Terminal 2 (Pepperstone Demo 2): M15 timeframe with moderate risk profile
     - Terminal 3 (IC Markets Demo 1): M30 timeframe with moderate-aggressive risk profile
     - Terminal 4 (IC Markets Demo 2): H1 timeframe with aggressive risk profile
   - Terminal 5 (IC Markets Demo 3): Implements signal fusion across all timeframes

4. **Signal Combination**:
   - Terminal 5 combines signals from multiple timeframes
   - Applies weights based on timeframe importance and historical performance
   - Identifies confluence where multiple timeframes agree
   - Calculates signal strength based on multi-timeframe confirmation

### Trade Execution

1. **Signal Evaluation**:
   - Check if the signal is strong enough to trade
   - Determine the trade direction (buy or sell)

2. **Position Management**:
   - Check current positions
   - Close positions if signal changes direction
   - Close positions if signal becomes weak

3. **Order Placement**:
   - Calculate position size
   - Calculate stop loss and take profit levels
   - Place market order

4. **Risk Management**:
   - Apply trailing stop to open positions
   - Monitor drawdown and daily loss
   - Close positions if risk limits are exceeded

## Backtesting

The backtesting process is as follows:

1. **Data Preparation**:
   - Get historical data for all timeframes
   - Create features for the model
   - Normalize features

2. **Model Training**:
   - Train models on the first part of the data
   - Use the trained models for the backtest

3. **Signal Generation**:
   - Generate signals for each timeframe
   - Combine signals from multiple timeframes

4. **Trade Simulation**:
   - Simulate trades based on the signals
   - Calculate profit/loss for each trade
   - Track equity curve

5. **Performance Evaluation**:
   - Calculate performance metrics
   - Generate performance report

## Usage Examples

### Training Models

```python
from trading_strategy import TradingStrategy
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_trainer import ModelTrainer
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluation import ModelEvaluator

# Initialize components
mt5_connector = MT5Connector()
mt5_connector.connect_terminal(terminal_id=1)
order_manager = OrderManager(mt5_connector)
model_trainer = ModelTrainer(input_size=32, hidden_size=64, num_layers=2, output_size=1)
data_collector = DataCollector(mt5_connector)
feature_engineer = FeatureEngineer()
model_evaluator = ModelEvaluator()

# Initialize trading strategy
strategy = TradingStrategy(
    mt5_connector=mt5_connector,
    order_manager=order_manager,
    model_trainer=model_trainer,
    data_collector=data_collector,
    feature_engineer=feature_engineer,
    model_evaluator=model_evaluator
)

# Train models
strategy.train_models(
    start_date="2022-01-01",
    end_date="2022-12-31"
)
```

### Running a Trading Cycle

```python
from trading_strategy import TradingStrategy
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_trainer import ModelTrainer
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluation import ModelEvaluator

# Initialize components
mt5_connector = MT5Connector()
mt5_connector.connect_terminal(terminal_id=1)
order_manager = OrderManager(mt5_connector)
model_trainer = ModelTrainer(input_size=32, hidden_size=64, num_layers=2, output_size=1)
data_collector = DataCollector(mt5_connector)
feature_engineer = FeatureEngineer()
model_evaluator = ModelEvaluator()

# Initialize trading strategy
strategy = TradingStrategy(
    mt5_connector=mt5_connector,
    order_manager=order_manager,
    model_trainer=model_trainer,
    data_collector=data_collector,
    feature_engineer=feature_engineer,
    model_evaluator=model_evaluator
)

# Run trading cycle
strategy.run_trading_cycle()
```

### Running a Backtest

```python
from trading_strategy import TradingStrategy
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_trainer import ModelTrainer
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluation import ModelEvaluator

# Initialize components
mt5_connector = MT5Connector()
mt5_connector.connect_terminal(terminal_id=1)
order_manager = OrderManager(mt5_connector)
model_trainer = ModelTrainer(input_size=32, hidden_size=64, num_layers=2, output_size=1)
data_collector = DataCollector(mt5_connector)
feature_engineer = FeatureEngineer()
model_evaluator = ModelEvaluator()

# Initialize trading strategy
strategy = TradingStrategy(
    mt5_connector=mt5_connector,
    order_manager=order_manager,
    model_trainer=model_trainer,
    data_collector=data_collector,
    feature_engineer=feature_engineer,
    model_evaluator=model_evaluator
)

# Run backtest
results = strategy.backtest(
    start_date="2022-01-01",
    end_date="2022-12-31"
)

# Print backtest results
print(f"Total Return: {results['equity'].iloc[-1] - 1.0:.2%}")
print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
print(f"Max Drawdown: {results['max_drawdown']:.2%}")
print(f"Win Rate: {results['win_rate']:.2%}")
print(f"Profit Factor: {results['profit_factor']:.2f}")
print(f"Total Trades: {results['total_trades']}")
```

## Signal Thresholds

The trading strategy uses the following signal thresholds:

- **Strong Buy Signal**: signal > 0.7
- **Buy Signal**: signal > 0.3
- **Neutral Signal**: -0.3 <= signal <= 0.3
- **Sell Signal**: signal < -0.3
- **Strong Sell Signal**: signal < -0.7

These thresholds determine when to open and close positions:

- Open long position: signal > 0.7
- Open short position: signal < -0.7
- Close long position: signal < 0.3
- Close short position: signal > -0.3

## Risk Management

The trading strategy implements timeframe-specific risk management techniques:

### Timeframe-Specific Risk Profiles

Each terminal implements a risk profile tailored to its timeframe:

#### Conservative (M5 - Terminal 1)
- Smaller position sizes (0.5-1% account risk per trade)
- Tighter stop losses (10-20 pips)
- Profit targets at 1.5-2:1 reward-to-risk ratio
- Higher win rate target (60%+)
- Quick trade execution with minimal slippage

#### Moderate (M15 - Terminal 2)
- Medium position sizes (1-2% account risk per trade)
- Balanced stop losses (20-40 pips)
- Profit targets at 2-2.5:1 reward-to-risk ratio
- Balanced win rate target (55%+)
- Balance between speed and accuracy

#### Moderate-Aggressive (M30 - Terminal 3)
- Larger position sizes (1.5-2.5% account risk per trade)
- Wider stop losses (40-60 pips)
- Profit targets at 2.5-3:1 reward-to-risk ratio
- Lower win rate acceptable (50%+)
- Focus on trend identification and following

#### Aggressive (H1 - Terminal 4)
- Largest position sizes (2-3% account risk per trade)
- Strategic stop losses (60-100 pips)
- Profit targets at 3-4:1 reward-to-risk ratio
- Lower win rate acceptable (45%+)
- Focus on major trend identification and key level recognition

#### Dynamic (Multi-TF - Terminal 5)
- Variable position sizing based on signal strength (0.5-3%)
- Adaptive stop losses based on volatility
- Profit targets determined by confluence points
- Win rate expectations vary by setup type
- Focus on cross-timeframe confirmation

### Position Sizing

- Timeframe-specific position sizing based on risk profile
- Dynamic adjustment based on signal strength and market volatility
- Proportional allocation across terminals based on historical performance

### Stop Loss and Take Profit

- Timeframe-appropriate stop loss and take profit levels
- Dynamic adjustment based on ATR scaled to timeframe
- Trailing stops optimized for each timeframe's characteristics

### Risk Limits

- Maximum risk per trade varies by timeframe
- Maximum combined exposure across all terminals
- Maximum daily loss with circuit breaker functionality
- Drawdown limits with automatic risk reduction

## Model Decay Detection

The trading strategy uses the ModelEvaluator to detect model decay:

1. **Performance Tracking**:
   - Track prediction accuracy
   - Track profit factor
   - Track consecutive losses

2. **Decay Detection**:
   - Check if accuracy falls below threshold
   - Check if profit factor falls below threshold
   - Check if consecutive losses exceed threshold
   - Check for declining trend in accuracy

3. **Model Retraining**:
   - If decay is detected, retrain the models
   - Use recent data for retraining

## Dependencies

The TradingStrategy class depends on the following:

- **MT5Connector**: For connecting to MT5 terminals
- **OrderManager**: For order execution
- **ModelTrainer**: For model training and prediction
- **DataCollector**: For data collection
- **FeatureEngineer**: For feature engineering
- **ModelEvaluator**: For model evaluation
- **config/trading_config.py**: For trading configuration

## Configuration

The TradingStrategy class is configured through the `config/trading_config.py` file:

```python
TRADING_CONFIG = {
    # Symbol configuration
    "symbol": "BTCUSD",
    "lot_size": 0.02,  # Fixed lot size

    # Timeframes for analysis
    "timeframes": {
        "M5": "5m",   # 5-minute timeframe
        "M15": "15m", # 15-minute timeframe
        "M30": "30m"  # 30-minute timeframe
    },

    # Risk management
    "risk_management": {
        "max_risk_per_trade": 0.02,  # Maximum risk per trade (2% of account balance)
        "max_open_positions": 3,     # Maximum number of open positions
        "max_daily_loss": 0.05,      # Maximum daily loss (5% of account balance)
    },

    # Stop loss and take profit
    "sl_tp": {
        "use_dynamic_sl_tp": True,   # Use dynamic stop loss and take profit
        "default_sl_pips": 100,      # Default stop loss in pips
        "default_tp_pips": 200,      # Default take profit in pips
        "atr_multiplier_sl": 1.5,    # ATR multiplier for stop loss
        "atr_multiplier_tp": 3.0,    # ATR multiplier for take profit
        "atr_period": 14             # ATR period
    },

    # Additional configuration...
}
```

## Limitations

The TradingStrategy class has the following limitations:

- **Market Conditions**: Performance may vary in different market conditions
- **Model Limitations**: LSTM models have inherent limitations in predicting financial markets
- **Execution Latency**: There may be latency between signal generation and order execution
- **Slippage**: There may be slippage between the expected and actual execution price
- **Market Hours**: The strategy does not account for market hours and liquidity

## Best Practices

When using the TradingStrategy class, follow these best practices:

1. **Backtesting**: Always backtest the strategy before trading live
2. **Risk Management**: Implement proper risk management
3. **Model Monitoring**: Monitor model performance and detect decay
4. **Continuous Learning**: Update models with new data
5. **Diversification**: Trade multiple timeframes and symbols
6. **Logging**: Use logging for debugging and monitoring
7. **Error Handling**: Implement proper error handling
8. **Testing**: Test the strategy in a demo account before trading live
