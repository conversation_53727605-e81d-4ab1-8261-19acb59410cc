"""
Model Loader Module.
This module provides functionality for loading trained models.
"""
import logging
import os
import pickle
import torch
from datetime import datetime
from typing import Dict, <PERSON>, Any
from model.lstm_model import TradingLSTM
from model.tft_model_pytorch import TemporalFusionTransformer
from model.arima_model import ARIMAModel
from model.ensemble_model import EnsembleModel
from utils.torch_security import safe_load, register_safe_classes

# Register custom classes as safe globals
register_safe_classes()

# Configure logger
logger = logging.getLogger('model.loader')

class ModelLoader:
    """
    Model Loader class for loading trained models.
    """
    def __init__(self):
        """Initialize ModelLoader."""
        self.model_cache = {}  # Cache for loaded models

    def load_model(self, model_path: str) -> Any:
        """
        Load a model from file.

        Args:
            model_path: Path to the model file

        Returns:
            Any: Loaded model
        """
        # Check if model is already in cache
        if model_path in self.model_cache:
            logger.debug(f"Using cached model: {model_path}")
            return self.model_cache[model_path]

        # Check if file exists
        if not os.path.exists(model_path):
            logger.error(f"Model file not found: {model_path}")
            return None

        try:
            # Determine model type from file extension
            if model_path.endswith('.pth'):
                model = self._load_torch_model(model_path)
            elif model_path.endswith('.pkl'):
                model = self._load_pickle_model(model_path)
            else:
                logger.error(f"Unsupported model file format: {model_path}")
                return None

            # Cache model
            self.model_cache[model_path] = model

            logger.info(f"Successfully loaded model from {model_path}")
            return model

        except Exception as e:
            logger.error(f"Error loading model from {model_path}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _load_torch_model(self, model_path: str) -> Union[TradingLSTM, TemporalFusionTransformer]:
        """
        Load a PyTorch model from file.

        Args:
            model_path: Path to the model file

        Returns:
            Union[TradingLSTM, TemporalFusionTransformer]: Loaded PyTorch model
        """
        # Determine model type from filename
        if 'lstm' in model_path.lower():
            model_type = 'lstm'
        elif 'tft' in model_path.lower():
            model_type = 'tft'
        else:
            logger.error(f"Unknown PyTorch model type: {model_path}")
            return None

        try:
            # Load model metadata
            metadata_path = model_path.replace('.pth', '_metadata.pkl')

            if os.path.exists(metadata_path):
                with open(metadata_path, 'rb') as f:
                    metadata = pickle.load(f)
            else:
                logger.warning(f"Model metadata not found: {metadata_path}")
                metadata = {}

            # Get model parameters
            input_size = metadata.get('input_size', 10)
            hidden_size = metadata.get('hidden_size', 64)
            num_layers = metadata.get('num_layers', 2)
            output_size = metadata.get('output_size', 1)

            # Create model instance
            if model_type == 'lstm':
                model = TradingLSTM(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    output_size=output_size
                )
            else:  # tft
                model = TemporalFusionTransformer(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    output_size=output_size
                )

            # Load model weights
            device = torch.device('cpu')
            model.load_state_dict(safe_load(model_path, map_location=device))
            model.to(device)
            model.eval()

            return model

        except Exception as e:
            logger.error(f"Error loading PyTorch model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _load_pickle_model(self, model_path: str) -> Union[ARIMAModel, EnsembleModel]:
        """
        Load a pickled model from file.

        Args:
            model_path: Path to the model file

        Returns:
            Union[ARIMAModel, EnsembleModel]: Loaded pickled model
        """
        try:
            # Load model from pickle file
            with open(model_path, 'rb') as f:
                model = pickle.load(f)

            return model

        except Exception as e:
            logger.error(f"Error loading pickled model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def clear_cache(self):
        """Clear the model cache."""
        self.model_cache.clear()
        logger.info("Model cache cleared")

    def get_model_info(self, model_path: str) -> Dict[str, Any]:
        """
        Get information about a model.

        Args:
            model_path: Path to the model file

        Returns:
            Dict[str, Any]: Model information
        """
        # Load model if not already in cache
        if model_path not in self.model_cache:
            model = self.load_model(model_path)
            if model is None:
                return {}
        else:
            model = self.model_cache[model_path]

        # Get model type
        if isinstance(model, TradingLSTM):
            model_type = 'lstm'
        elif isinstance(model, TemporalFusionTransformer):
            model_type = 'tft'
        elif isinstance(model, ARIMAModel):
            model_type = 'arima'
        elif isinstance(model, EnsembleModel):
            model_type = 'ensemble'
        else:
            model_type = 'unknown'

        # Get model parameters
        if model_type in ['lstm', 'tft']:
            params = {
                'input_size': model.input_size,
                'hidden_size': model.hidden_size,
                'num_layers': model.num_layers,
                'output_size': model.output_size
            }
        elif model_type == 'arima':
            params = {
                'order': model.order,
                'seasonal_order': model.seasonal_order
            }
        elif model_type == 'ensemble':
            params = {
                'model_types': model.model_types,
                'weights': model.weights
            }
        else:
            params = {}

        # Get model file info
        file_info = {
            'path': model_path,
            'size': os.path.getsize(model_path) if os.path.exists(model_path) else 0,
            'modified': datetime.fromtimestamp(os.path.getmtime(model_path)).strftime('%Y-%m-%d %H:%M:%S') if os.path.exists(model_path) else None
        }

        return {
            'model_type': model_type,
            'parameters': params,
            'file_info': file_info
        }

    def load_metrics(self, metrics_path: str) -> Dict[str, Any]:
        """
        Load model metrics from file.

        Args:
            metrics_path: Path to the metrics file

        Returns:
            Dict[str, Any]: Model metrics
        """
        # Check if file exists
        if not os.path.exists(metrics_path):
            logger.error(f"Metrics file not found: {metrics_path}")
            return {}

        try:
            # Load metrics from JSON file
            import json
            with open(metrics_path, 'r') as f:
                metrics = json.load(f)

            logger.info(f"Successfully loaded metrics from {metrics_path}")
            return metrics

        except Exception as e:
            logger.error(f"Error loading metrics from {metrics_path}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {}
