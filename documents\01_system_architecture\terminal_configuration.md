# Terminal Configuration

This document describes the configuration of the 5 MT5 terminals used in the trading system.

## Overview

The MT5 Trading System uses 5 separate MetaTrader 5 terminals, each configured for a specific model type and broker. This multi-terminal approach provides redundancy, diversification, and specialized model deployment.

## Terminal Assignments

### Terminal 1: ARIMA Models (Pepperstone Demo 1)
- **Model Type**: ARIMA (Statistical time series forecasting)
- **Broker**: Pepperstone
- **Server**: mt5-demo01.pepperstone.com
- **Path**: `C:\Users\<USER>\Desktop\MT5 Pepper 03\terminal64.exe`
- **Login**: ********
- **Specialization**: Statistical patterns, mean-reversion, works with limited data

### Terminal 2: LSTM Models (Pepperstone Demo 2)
- **Model Type**: LSTM (Deep learning sequence modeling)
- **Broker**: Pepperstone
- **Server**: mt5-demo01.pepperstone.com
- **Path**: `C:\Users\<USER>\Desktop\MT5 Pepper 02\terminal64.exe`
- **Login**: ********
- **Specialization**: Complex patterns, sequence learning, non-linear relationships

### Terminal 3: TFT Models (IC Markets Demo 1)
- **Model Type**: TFT (Transformer-based forecasting)
- **Broker**: IC Markets
- **Server**: mt5-demo.icmarkets.com
- **Path**: `C:\Users\<USER>\Desktop\MT5 IC 01\terminal64.exe`
- **Login**: ********
- **Specialization**: Advanced patterns with attention mechanisms, interpretability

### Terminal 4: LSTM+ARIMA Ensemble (IC Markets Demo 2)
- **Model Type**: LSTM+ARIMA Ensemble (Hybrid approach)
- **Broker**: IC Markets
- **Server**: mt5-demo.icmarkets.com
- **Path**: `C:\Users\<USER>\Desktop\MT5 IC 02\terminal64.exe`
- **Login**: 52236863
- **Specialization**: Combines strengths of statistical and deep learning approaches

### Terminal 5: TFT+ARIMA Ensemble (IC Markets Demo 3)
- **Model Type**: TFT+ARIMA Ensemble (Advanced hybrid approach)
- **Broker**: IC Markets
- **Server**: mt5-demo.icmarkets.com
- **Path**: `C:\Users\<USER>\Desktop\MT5 IC 03\terminal64.exe`
- **Login**: ********
- **Specialization**: Combines transformer capabilities with statistical robustness

## Configuration Requirements

### MT5 Terminal Settings

For each terminal, the following settings must be configured:

1. **Algo Trading**: Must be enabled (green button in toolbar)
2. **Expert Advisors**: Must be allowed to trade
   - Tools → Options → Expert Advisors
   - Check "Allow automated trading"
   - Check "Allow DLL imports"
3. **Market Watch**: Add required symbols (BTCUSD.a, ETHUSD.a)
4. **Account**: Demo account with sufficient balance

### System Configuration

The terminal configuration is defined in `config/credentials.py`:

```python
MT5_TERMINALS = {
    1: {
        'name': 'Pepperstone Demo 1',
        'path': 'C:/Users/<USER>/Desktop/MT5 Pepper 03/terminal64.exe',
        'login': ********,
        'password': 'your_password',
        'server': 'mt5-demo01.pepperstone.com',
        'model_type': 'arima'
    },
    2: {
        'name': 'Pepperstone Demo 2',
        'path': 'C:/Users/<USER>/Desktop/MT5 Pepper 02/terminal64.exe',
        'login': ********,
        'password': 'your_password',
        'server': 'mt5-demo01.pepperstone.com',
        'model_type': 'lstm'
    },
    3: {
        'name': 'IC Markets Demo 1',
        'path': 'C:/Users/<USER>/Desktop/MT5 IC 01/terminal64.exe',
        'login': ********,
        'password': 'your_password',
        'server': 'mt5-demo.icmarkets.com',
        'model_type': 'tft'
    },
    4: {
        'name': 'IC Markets Demo 2',
        'path': 'C:/Users/<USER>/Desktop/MT5 IC 02/terminal64.exe',
        'login': 52236863,
        'password': 'your_password',
        'server': 'mt5-demo.icmarkets.com',
        'model_type': 'lstm_arima'
    },
    5: {
        'name': 'IC Markets Demo 3',
        'path': 'C:/Users/<USER>/Desktop/MT5 IC 03/terminal64.exe',
        'login': ********,
        'password': 'your_password',
        'server': 'mt5-demo.icmarkets.com',
        'model_type': 'tft_arima'
    }
}
```

## Initialization Process

The system initializes all terminals using the `MT5Initializer` class:

1. **Primary Terminal**: Terminal 1 is initialized first as the primary connection
2. **Secondary Terminals**: Terminals 2-5 are marked as initialized due to MT5 API limitations
3. **Algo Trading Verification**: Each terminal's algo trading status is verified
4. **Connection Management**: The `MT5ConnectionManager` maintains connections to all terminals

## Verification Commands

To verify terminal configuration:

```bash
# Check all terminals
python run_mt5_initializer.py --monitor

# Validate complete system
python validate_complete_system.py
```

## Troubleshooting

### Common Issues

1. **Terminal Not Found**: Check path in credentials.py
2. **Algo Trading Disabled**: Enable in MT5 terminal toolbar
3. **Expert Advisors Blocked**: Enable in Tools → Options → Expert Advisors
4. **Connection Timeout**: Check internet connection and server status

### Verification Steps

1. Ensure all 5 terminals are installed in separate directories
2. Verify each terminal can connect to its respective server
3. Check that algo trading is enabled in each terminal
4. Confirm symbols are available in Market Watch

Last updated: 2025-06-06 15:55:00
