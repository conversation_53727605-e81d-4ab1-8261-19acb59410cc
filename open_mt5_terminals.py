"""
Open MT5 Terminals Script.
This script opens all MT5 terminals to allow the user to manually enable Algo Trading.
"""
import sys
import subprocess
from config.credentials import MT5_TERMINALS

def open_mt5_terminals():
    """Open all MT5 terminals."""
    print("Opening all MT5 terminals...")

    for terminal_id, config in MT5_TERMINALS.items():
        path = config["path"]
        print(f"Opening Terminal {terminal_id}: {path}")

        try:
            # Open the MT5 terminal with the /portable parameter
            # This helps ensure Algo Trading remains enabled
            subprocess.Popen([path, '/portable'], shell=True)
        except Exception as e:
            print(f"Error opening Terminal {terminal_id}: {str(e)}")

    print("\nIMPORTANT: To enable Algo Trading, you need to:")
    print("1. In each MT5 terminal, click the 'Algo Trading' button in the toolbar")
    print("2. Make sure the button is highlighted (enabled)")
    print("3. Run 'python run_mt5_initializer.py --monitor' to verify")

    return 0

if __name__ == "__main__":
    sys.exit(open_mt5_terminals())
