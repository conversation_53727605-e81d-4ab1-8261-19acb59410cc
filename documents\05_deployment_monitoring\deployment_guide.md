# Deployment Guide

This document provides detailed instructions for installing and setting up the trading system.

## System Requirements

### Hardware Requirements

- **CPU**: Intel Core i5/i7/i9 or AMD Ryzen 5/7/9 (8+ cores recommended)
- **RAM**: 16GB minimum, 32GB+ recommended
- **Storage**: 500GB SSD minimum
- **GPU**: NVIDIA GPU with CUDA support (for faster model training)

### Software Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Python**: Python 3.8 or higher
- **MT5 Terminals**: 5 separate MetaTrader 5 installations
- **Git**: For version control

## Installation Steps

### Step 1: Install Python

1. Download Python 3.8 or higher from [python.org](https://www.python.org/downloads/)
2. Run the installer and check "Add Python to PATH"
3. Verify the installation by opening a command prompt and running:
   ```bash
   python --version
   ```

### Step 2: Install Git

1. Download Git from [git-scm.com](https://git-scm.com/downloads)
2. Run the installer with default settings
3. Verify the installation by opening a command prompt and running:
   ```bash
   git --version
   ```

### Step 3: Clone the Repository

1. Open a command prompt
2. Navigate to the directory where you want to install the trading system
3. Clone the repository:
   ```bash
   git clone https://github.com/r70pro/BTCUSD-Trading-Bot.git
   cd BTCUSD-Trading-Bot
   ```

### Step 4: Create a Virtual Environment

1. Create a virtual environment:
   ```bash
   python -m venv venv
   ```
2. Activate the virtual environment:
   - Windows:
     ```bash
     venv\Scripts\activate
     ```
   - Linux/Mac:
     ```bash
     source venv/bin/activate
     ```

### Step 5: Install Dependencies

1. Upgrade pip:
   ```bash
   pip install --upgrade pip
   ```
2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

### Step 6: Install MT5 Terminals

1. Download MetaTrader 5 from [metatrader5.com](https://www.metatrader5.com/en/download)
2. Install 5 separate instances of MetaTrader 5:
   - Terminal 1: Default installation
   - Terminal 2: Install to a different directory (e.g., `C:\Program Files\MetaTrader 5 - 2`)
   - Terminal 3: Install to a different directory (e.g., `C:\Program Files\MetaTrader 5 - 3`)
   - Terminal 4: Install to a different directory (e.g., `C:\Program Files\MetaTrader 5 - 4`)
   - Terminal 5: Install to a different directory (e.g., `C:\Program Files\MetaTrader 5 - 5`)

### Step 7: Configure MT5 Terminals

For each MT5 terminal:

1. Open the terminal
2. Create a demo account or log in to an existing account
3. Enable Algo Trading:
   - Click the "Algo Trading" button in the toolbar
   - Verify that the button is highlighted (enabled)
4. Allow Expert Advisors to trade:
   - Click "Tools" > "Options"
   - Go to the "Expert Advisors" tab
   - Check "Allow automated trading"
   - Check "Allow DLL imports"
   - Click "OK"
5. Add symbols to Market Watch:
   - Press Ctrl+M to open Market Watch
   - Right-click in the Market Watch window
   - Select "Show All"
   - Alternatively, right-click and select "Symbols" to choose specific symbols

### Step 8: Configure the Trading System

1. Edit `config/credentials.py` to set the paths to your MT5 terminals:
   ```python
   MT5_TERMINALS = {
       1: {
           'name': 'Pepperstone Demo 1',
           'path': 'C:/Users/<USER>/Desktop/MT5 Pepper 03/terminal64.exe',
           'login': ********,
           'password': 'your_password',
           'server': 'mt5-demo01.pepperstone.com'
       },
       2: {
           'name': 'Pepperstone Demo 2',
           'path': 'C:/Users/<USER>/Desktop/MT5 Pepper 02/terminal64.exe',
           'login': 61362035,
           'password': 'your_password',
           'server': 'mt5-demo01.pepperstone.com'
       },
       # ... more terminals ...
   }
   ```
2. Edit `config/trading_config.py` to customize the trading system settings

### Step 9: Create Required Directories

The system will automatically create required directories, but you can create them manually:
```bash
mkdir -p data/storage/historical
mkdir -p data/storage/features
mkdir -p model/saved_models/lstm
mkdir -p model/saved_models/tft
mkdir -p model/saved_models/arima
mkdir -p model/saved_models/ensemble
mkdir -p model/visualizations
mkdir -p logs
```

### Step 10: Verify the Installation

1. Run the system verification:
   ```bash
   python validate_complete_system.py
   ```
2. Check that all components are working correctly

## Installing CUDA for GPU Support (Optional)

If you have an NVIDIA GPU, you can install CUDA for faster model training:

1. Check your GPU compatibility on the [NVIDIA CUDA GPUs](https://developer.nvidia.com/cuda-gpus) page
2. Download and install the CUDA Toolkit from [NVIDIA CUDA Downloads](https://developer.nvidia.com/cuda-downloads)
3. Download and install cuDNN from [NVIDIA cuDNN Downloads](https://developer.nvidia.com/cudnn)
4. Verify the installation by running:
   ```bash
   python -c "import torch; print(torch.cuda.is_available())"
   ```
   This should output `True` if CUDA is installed correctly.

Last updated: 2025-06-06 15:40:00
