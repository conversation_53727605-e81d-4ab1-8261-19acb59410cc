# Troubleshooting Guide

This document provides solutions for common issues that may arise when using the trading system.

## MT5 Connection Issues

### Terminal Not Found

**Issue**: The system cannot find the MT5 terminal.

**Solutions**:
1. Check that the terminal path in `config/credentials.py` is correct
2. Verify that the terminal is installed and accessible
3. Try running the terminal manually to ensure it works
4. Check the logs for error messages

**Example Error**:
```
ERROR: Failed to initialize MT5 with Terminal 1
ERROR: MT5 error code: 10007
```

### Algo Trading Disabled

**Issue**: Algo Trading is disabled in the MT5 terminal.

**Solutions**:
1. Open the MT5 terminal
2. Click the "Algo Trading" button in the toolbar
3. Verify that the button is highlighted (enabled)
4. Check the logs for error messages

**Example Error**:
```
ERROR: Algo Trading is disabled for Terminal 1
ERROR: Please enable Algo Trading in the MT5 terminal
```

### Expert Advisors Not Allowed to Trade

**Issue**: Expert Advisors are not allowed to trade.

**Solutions**:
1. Open the MT5 terminal
2. Click "Tools" > "Options"
3. Go to the "Expert Advisors" tab
4. Check "Allow automated trading"
5. Check "Allow DLL imports"
6. Click "OK"
7. Check the logs for error messages

**Example Error**:
```
ERROR: Expert Advisors are not allowed to trade for Terminal 1
ERROR: Please enable Expert Advisors in the MT5 terminal
```

### Connection Timeout

**Issue**: The system times out when connecting to the MT5 terminal.

**Solutions**:
1. Check your internet connection
2. Verify that the MT5 terminal is connected to the server
3. Try restarting the MT5 terminal
4. Try increasing the `max_retries` and `retry_delay` parameters
5. Check the logs for error messages

**Example Error**:
```
ERROR: Connection timeout for Terminal 1
ERROR: Failed to connect after 5 retries
```

## Data Collection Issues

### Missing Data

**Issue**: The system cannot collect data for a specific symbol or timeframe.

**Solutions**:
1. Check that the symbol is added to Market Watch in the MT5 terminal
2. Verify that the timeframe is enabled in the MT5 terminal
3. Check the logs for error messages
4. Try collecting data for a different symbol or timeframe

**Example Error**:
```
ERROR: Failed to collect data for BTCUSD.a M5
ERROR: Symbol not found in Market Watch
```

### Inconsistent Data

**Issue**: The collected data is inconsistent or contains gaps.

**Solutions**:
1. Check the data validation logs
2. Try collecting data for a shorter time period
3. Use the `DataCleaner` class to clean the data
4. Check the logs for error messages

**Example Error**:
```
ERROR: Inconsistent data detected for BTCUSD.a M5
ERROR: Gaps detected in time series: 10 missing timestamps
```

### Data Storage Issues

**Issue**: The system cannot save or load data.

**Solutions**:
1. Check that the data directory exists and is writable
2. Verify that the data path is correct
3. Check the logs for error messages
4. Try using a different data path

**Example Error**:
```
ERROR: Failed to save data to data/storage/historical/terminal_1/BTCUSD.a_M5/20230101_20250101_terminal_1_20250425123456.parquet
ERROR: Directory not found
```

## Model Training Issues

### GPU Not Available

**Issue**: The system cannot use the GPU for model training.

**Solutions**:
1. Check that CUDA is installed correctly
2. Verify that your GPU is CUDA-compatible
3. Check the logs for error messages
4. Try using CPU for training by setting `use_gpu=False`

**Example Error**:
```
ERROR: CUDA not available
ERROR: Please install CUDA or set use_gpu=False
```

### Out of Memory

**Issue**: The system runs out of memory during model training.

**Solutions**:
1. Reduce the batch size
2. Reduce the model size
3. Use a smaller dataset
4. Try using CPU for training by setting `use_gpu=False`
5. Check the logs for error messages

**Example Error**:
```
ERROR: CUDA out of memory
ERROR: Try reducing batch_size or model size
```

### Model Convergence Issues

**Issue**: The model does not converge during training.

**Solutions**:
1. Increase the number of epochs
2. Adjust the learning rate
3. Try a different model architecture
4. Check the data for issues
5. Check the logs for error messages

**Example Error**:
```
WARNING: Model not converging after 100 epochs
WARNING: Try adjusting learning_rate or increasing epochs
```

### Model Saving/Loading Issues

**Issue**: The system cannot save or load models.

**Solutions**:
1. Check that the model directory exists and is writable
2. Verify that the model path is correct
3. Check the logs for error messages
4. Try using a different model path

**Example Error**:
```
ERROR: Failed to save model to model/saved_models/lstm/BTCUSD.a_M5_lstm.pth
ERROR: Directory not found
```

Last updated: 2025-06-06 15:45:00
