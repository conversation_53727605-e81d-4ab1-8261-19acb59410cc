"""
Symbol Manager Module.
This module handles symbol management for the trading bot.
"""
import logging
import os
import re
import traceback
from typing import Dict, List, Optional
import pandas as pd
from config.trading_config import TRADING_CONFIG
from trading.mt5_connector import MT5Connector

# Configure logger
logger = logging.getLogger('trading.symbol_manager')

class SymbolManager:
    """
    Symbol Manager class for handling symbol operations.
    """
    def __init__(self, mt5_connector: MT5Connector):
        """
        Initialize SymbolManager.

        Args:
            mt5_connector: MT5Connector instance
        """
        self.mt5 = mt5_connector
        self.symbols_config = TRADING_CONFIG.get('symbols', {})
        self.symbols_dir = 'data/symbols'
        self.symbol_info_file = os.path.join(self.symbols_dir, 'symbol_info.parquet')
        
        # Create symbols directory if it doesn't exist
        os.makedirs(self.symbols_dir, exist_ok=True)
        
        # Load existing symbol info
        self.symbol_info = self._load_symbol_info()
        
    def _load_symbol_info(self) -> pd.DataFrame:
        """
        Load symbol information from file.
        
        Returns:
            pd.DataFrame: Symbol information
        """
        if os.path.exists(self.symbol_info_file):
            try:
                return pd.read_parquet(self.symbol_info_file)
            except Exception as e:
                logger.error(f"Error loading symbol info: {str(e)}")
                return pd.DataFrame(columns=['symbol', 'description', 'pip_value', 'min_lot', 'max_lot', 
                                            'lot_step', 'point', 'digits', 'added_date', 'last_updated',
                                            'data_quality', 'terminals_available'])
        else:
            return pd.DataFrame(columns=['symbol', 'description', 'pip_value', 'min_lot', 'max_lot', 
                                        'lot_step', 'point', 'digits', 'added_date', 'last_updated',
                                        'data_quality', 'terminals_available'])
    
    def _save_symbol_info(self):
        """Save symbol information to file."""
        try:
            self.symbol_info.to_parquet(self.symbol_info_file, index=False)
            logger.info(f"Symbol information saved to {self.symbol_info_file}")
        except Exception as e:
            logger.error(f"Error saving symbol info: {str(e)}")
    
    def validate_symbol_format(self, symbol: str) -> bool:
        """
        Validate symbol format (XXXYYY or XXXYYY.a).
        
        Args:
            symbol: Symbol to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        # Basic pattern for standard forex/crypto pairs
        pattern = r'^[A-Z]{3,6}(\.[a-z])?$'
        return bool(re.match(pattern, symbol))
    
    def check_symbol_availability(self, symbol: str) -> Dict[int, bool]:
        """
        Check if a symbol is available on all terminals.
        
        Args:
            symbol: Symbol to check
            
        Returns:
            Dict[int, bool]: Dictionary of terminal_id -> availability
        """
        availability = {}
        
        # Check each terminal
        for terminal_id in self.mt5.connected_terminals:
            # Connect to the terminal
            if self.mt5.connect_terminal(terminal_id):
                # Check if symbol is available
                symbol_info = self.mt5.get_symbol_info(symbol)
                availability[terminal_id] = symbol_info is not None

                if symbol_info is not None:
                    logger.info(f"Symbol {symbol} is available on Terminal {terminal_id}")
                else:
                    logger.warning(f"Symbol {symbol} is NOT available on Terminal {terminal_id}")
            else:
                availability[terminal_id] = False
                logger.warning(f"Could not connect to Terminal {terminal_id}")
        
        return availability
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """
        Get detailed information about a symbol.
        
        Args:
            symbol: Symbol to get info for
            
        Returns:
            Optional[Dict]: Symbol information or None if not available
        """
        # Check if symbol exists in any terminal
        for terminal_id in self.mt5.connected_terminals:
            # Connect to the terminal
            if self.mt5.connect_terminal(terminal_id):
                # Get symbol info
                symbol_info = self.mt5.get_symbol_info(symbol)
                if symbol_info is not None:
                    # Convert to dictionary
                    info = {
                        'symbol': symbol,
                        'description': symbol_info.get('name', symbol),  # Use name as description
                        'pip_value': symbol_info.get('tick_value', 0.0),
                        'min_lot': symbol_info.get('volume_min', 0.01),
                        'max_lot': symbol_info.get('volume_max', 100.0),
                        'lot_step': symbol_info.get('volume_step', 0.01),
                        'point': symbol_info.get('point', 0.00001),
                        'digits': symbol_info.get('digits', 5),
                        'added_date': pd.Timestamp.now(),
                        'last_updated': pd.Timestamp.now(),
                        'data_quality': 'unknown',
                        'terminals_available': str(list(self.check_symbol_availability(symbol).keys()))
                    }
                    return info
        
        return None
    
    def add_symbol(self, symbol: str) -> bool:
        """
        Add a new symbol to the system.
        
        Args:
            symbol: Symbol to add
            
        Returns:
            bool: True if added successfully, False otherwise
        """
        try:
            # Validate symbol format
            if not self.validate_symbol_format(symbol):
                logger.error(f"Invalid symbol format: {symbol}")
                return False
            
            # Check if symbol already exists
            if symbol in self.symbol_info['symbol'].values:
                logger.warning(f"Symbol {symbol} already exists")
                return True
            
            # Check symbol availability
            availability = self.check_symbol_availability(symbol)
            if not any(availability.values()):
                logger.error(f"Symbol {symbol} is not available on any terminal")
                return False
            
            # Get symbol info
            info = self.get_symbol_info(symbol)
            if info is None:
                logger.error(f"Could not get information for symbol {symbol}")
                return False
            
            # Add to symbol info DataFrame
            self.symbol_info = pd.concat([
                self.symbol_info, 
                pd.DataFrame([info])
            ], ignore_index=True)
            
            # Save symbol info
            self._save_symbol_info()
            
            logger.info(f"Symbol {symbol} added successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error adding symbol {symbol}: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def remove_symbol(self, symbol: str) -> bool:
        """
        Remove a symbol from the system.
        
        Args:
            symbol: Symbol to remove
            
        Returns:
            bool: True if removed successfully, False otherwise
        """
        try:
            # Check if symbol exists
            if symbol not in self.symbol_info['symbol'].values:
                logger.warning(f"Symbol {symbol} does not exist")
                return False
            
            # Remove from symbol info DataFrame
            self.symbol_info = self.symbol_info[self.symbol_info['symbol'] != symbol]
            
            # Save symbol info
            self._save_symbol_info()
            
            logger.info(f"Symbol {symbol} removed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error removing symbol {symbol}: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def get_all_symbols(self) -> List[str]:
        """
        Get all symbols in the system.
        
        Returns:
            List[str]: List of symbols
        """
        return self.symbol_info['symbol'].tolist()
    
    def get_available_symbols(self) -> List[str]:
        """
        Get all symbols available on at least one terminal.
        
        Returns:
            List[str]: List of available symbols
        """
        available_symbols = []
        
        for symbol in self.get_all_symbols():
            availability = self.check_symbol_availability(symbol)
            if any(availability.values()):
                available_symbols.append(symbol)
        
        return available_symbols
