# Storage Organization

This document describes the standardized storage organization used throughout the trading system.

## Overview

The trading system uses a standardized directory structure for organizing data, models, and outputs. This ensures consistency, maintainability, and easy navigation across all components.

## Directory Structure

```
MT5 Trading System/
├── data/                    # Data storage and processing
│   ├── storage/             # Main data storage
│   │   ├── historical/      # Historical OHLCV data
│   │   │   ├── terminal_1/  # Terminal 1 (ARIMA) data
│   │   │   ├── terminal_2/  # Terminal 2 (LSTM) data
│   │   │   ├── terminal_3/  # Terminal 3 (TFT) data
│   │   │   ├── terminal_4/  # Terminal 4 (LSTM+ARIMA) data
│   │   │   └── terminal_5/  # Terminal 5 (TFT+ARIMA) data
│   │   └── features/        # Processed feature data
│   │       ├── terminal_1/  # Terminal 1 features
│   │       ├── terminal_2/  # Terminal 2 features
│   │       ├── terminal_3/  # Terminal 3 features
│   │       ├── terminal_4/  # Terminal 4 features
│   │       └── terminal_5/  # Terminal 5 features
│   └── validation/          # Data validation results
├── model/                   # Model storage and management
│   ├── saved_models/        # Trained model files
│   │   ├── arima/           # ARIMA models (Terminal 1)
│   │   ├── lstm/            # LSTM models (Terminal 2)
│   │   ├── tft/             # TFT models (Terminal 3)
│   │   └── ensemble/        # Ensemble models (Terminals 4-5)
│   ├── metrics/             # Model performance metrics
│   └── visualizations/      # Model visualization outputs
├── monitoring/              # Performance monitoring
│   ├── dashboard/           # Dashboard files
│   ├── data/                # Performance data
│   └── plots/               # Performance plots
└── logs/                    # System logs
```

## Data Storage Paths

### Historical Data

Historical OHLCV data is stored using the following path structure:

```
data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
```

**Examples:**
```
data/storage/historical/terminal_1/BTCUSD.a_M5/20230101_20250101_terminal_1_20250425123456.parquet
data/storage/historical/terminal_2/BTCUSD.a_M15/20230101_20250101_terminal_2_20250425123456.parquet
data/storage/historical/terminal_3/ETHUSD.a_H1/20230101_20250101_terminal_3_20250425123456.parquet
```

### Feature Data

Processed feature data with technical indicators is stored using:

```
data/storage/features/terminal_{terminal_id}/{symbol}_{timeframe}/features_terminal_{terminal_id}_{timestamp}.parquet
```

**Examples:**
```
data/storage/features/terminal_1/BTCUSD.a_M5/features_terminal_1_20250425123456.parquet
data/storage/features/terminal_2/BTCUSD.a_M15/features_terminal_2_20250425123456.parquet
```

## Model Storage Paths

### ARIMA Models (Terminal 1)

```
model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
model/saved_models/arima/{symbol}_{timeframe}_arima_metrics.json
```

**Examples:**
```
model/saved_models/arima/BTCUSD.a_M5_arima.pkl
model/saved_models/arima/BTCUSD.a_M5_arima_metrics.json
```

### LSTM Models (Terminal 2)

```
model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
model/saved_models/lstm/{symbol}_{timeframe}_lstm_metrics.json
```

**Examples:**
```
model/saved_models/lstm/BTCUSD.a_M5_lstm.pth
model/saved_models/lstm/BTCUSD.a_M5_lstm_metrics.json
```

### TFT Models (Terminal 3)

```
model/saved_models/tft/{symbol}_{timeframe}_tft.pth
model/saved_models/tft/{symbol}_{timeframe}_tft_metrics.json
```

**Examples:**
```
model/saved_models/tft/BTCUSD.a_M5_tft.pth
model/saved_models/tft/BTCUSD.a_M5_tft_metrics.json
```

### Ensemble Models (Terminals 4-5)

```
model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima.pkl
model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima.pkl
```

**Examples:**
```
model/saved_models/ensemble/BTCUSD.a_M5_lstm_arima.pkl
model/saved_models/ensemble/BTCUSD.a_M5_tft_arima.pkl
```

## Visualization Storage

### Model Visualizations

```
model/visualizations/{model_type}/{symbol}_{timeframe}/{visualization_type}.png
```

**Examples:**
```
model/visualizations/lstm/BTCUSD.a_M5/predictions.png
model/visualizations/tft/BTCUSD.a_M5/training_history.png
model/visualizations/ensemble/BTCUSD.a_M5/model_comparison.png
```

### Dashboard Visualizations

```
monitoring/dashboard/{visualization_type}.png
```

**Examples:**
```
monitoring/dashboard/equity_curve.png
monitoring/dashboard/drawdown.png
monitoring/dashboard/monthly_returns.png
```

## Performance Data Storage

### Trading Performance

```
monitoring/data/{data_type}.csv
```

**Examples:**
```
monitoring/data/trade_history.csv
monitoring/data/equity_curve.csv
monitoring/data/performance_metrics.csv
monitoring/data/daily_returns.csv
monitoring/data/monthly_returns.csv
```

## Path Utilities

The system provides utility functions in `utils/path_utils.py` for generating standardized paths:

```python
from utils.path_utils import get_terminal_data_path, get_model_path

# Get data path for a specific terminal
data_path = get_terminal_data_path(
    terminal_id=1,
    symbol="BTCUSD.a",
    timeframe="M5"
)

# Get model path for a specific model type
model_path = get_model_path(
    model_type="arima",
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

## File Naming Conventions

### Timestamps

All files include timestamps in the format: `YYYYMMDDHHMMSS`

### Symbols

Symbol names use the format: `{BASE}{QUOTE}.a` (e.g., `BTCUSD.a`, `ETHUSD.a`)

### Timeframes

Standard timeframe abbreviations:
- M5: 5-minute
- M15: 15-minute
- M30: 30-minute
- H1: 1-hour
- H4: 4-hour

## Data Management Best Practices

1. **Consistent Naming**: Always use the standardized path utilities
2. **Version Control**: Include timestamps in file names for versioning
3. **Organization**: Keep related files in the same directory structure
4. **Cleanup**: Regularly clean up old files to manage disk space
5. **Backup**: Implement regular backups of critical data and models

Last updated: 2025-06-06 16:00:00
