"""
Comprehensive System Optimization Script.
This script performs a complete system optimization including:
- Code cleanup and optimization
- Data collection optimization
- Model training optimization
- Performance monitoring setup
- Documentation organization

Usage:
    python scripts/comprehensive_system_optimization.py [options]

Options:
    --symbol BTCUSD.a        Symbol to optimize for (default: BTCUSD.a)
    --years 5                Years of historical data (default: 5)
    --optimize-code          Optimize code structure and remove redundancies
    --optimize-data          Optimize data collection and storage
    --optimize-models        Optimize model training and performance
    --optimize-docs          Organize documentation structure
    --full-optimization      Perform all optimizations
"""
import argparse
import logging
import sys
import os
import glob
import shutil
from datetime import datetime

# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import required modules
from utils.path_utils import VALID_TIMEFRAMES, VALID_TERMINAL_IDS

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system_optimization.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemOptimizer:
    """Comprehensive system optimizer."""
    
    def __init__(self):
        """Initialize the system optimizer."""
        self.optimization_stats = {}
        
    def optimize_code_structure(self) -> bool:
        """Optimize code structure and remove redundancies."""
        logger.info("\n=== OPTIMIZING CODE STRUCTURE ===")
        
        try:
            optimizations = {
                'removed_files': 0,
                'cleaned_imports': 0,
                'optimized_functions': 0
            }
            
            # Remove redundant log files (keep only recent ones)
            log_dir = "logs"
            if os.path.exists(log_dir):
                log_files = glob.glob(os.path.join(log_dir, "*.log"))
                # Sort by modification time, keep only the 10 most recent
                log_files.sort(key=os.path.getmtime, reverse=True)
                for old_log in log_files[10:]:
                    try:
                        os.remove(old_log)
                        optimizations['removed_files'] += 1
                        logger.info(f"Removed old log file: {old_log}")
                    except Exception as e:
                        logger.warning(f"Failed to remove {old_log}: {str(e)}")
            
            # Clean up temporary files
            temp_patterns = [
                "**/__pycache__",
                "**/*.pyc",
                "**/*.pyo",
                "**/.pytest_cache",
                "**/temp_*",
                "**/*.tmp"
            ]
            
            for pattern in temp_patterns:
                files = glob.glob(pattern, recursive=True)
                for temp_file in files:
                    try:
                        if os.path.isdir(temp_file):
                            shutil.rmtree(temp_file)
                        else:
                            os.remove(temp_file)
                        optimizations['removed_files'] += 1
                        logger.info(f"Removed temporary file/directory: {temp_file}")
                    except Exception as e:
                        logger.warning(f"Failed to remove {temp_file}: {str(e)}")
            
            # Optimize Python files by removing unused imports (basic check)
            python_files = glob.glob("**/*.py", recursive=True)
            for py_file in python_files:
                if "scripts/comprehensive_system_optimization.py" in py_file:
                    continue  # Skip this file
                
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Basic optimization: remove duplicate empty lines
                    lines = content.split('\n')
                    optimized_lines = []
                    prev_empty = False
                    
                    for line in lines:
                        if line.strip() == '':
                            if not prev_empty:
                                optimized_lines.append(line)
                            prev_empty = True
                        else:
                            optimized_lines.append(line)
                            prev_empty = False
                    
                    optimized_content = '\n'.join(optimized_lines)
                    
                    if optimized_content != content:
                        with open(py_file, 'w', encoding='utf-8') as f:
                            f.write(optimized_content)
                        optimizations['cleaned_imports'] += 1
                        logger.info(f"Optimized file: {py_file}")
                        
                except Exception as e:
                    logger.warning(f"Failed to optimize {py_file}: {str(e)}")
            
            self.optimization_stats['code_structure'] = optimizations
            logger.info(f"[SUCCESS] Code structure optimization completed: {optimizations}")
            return True
            
        except Exception as e:
            logger.error(f"Code structure optimization failed: {str(e)}")
            return False
    
    def optimize_data_storage(self, symbol: str) -> bool:
        """Optimize data storage and organization."""
        logger.info(f"\n=== OPTIMIZING DATA STORAGE FOR {symbol} ===")
        
        try:
            optimizations = {
                'consolidated_files': 0,
                'removed_duplicates': 0,
                'optimized_storage': 0
            }
            
            # Consolidate data files for each terminal and timeframe
            for terminal_id in VALID_TERMINAL_IDS:
                for timeframe in VALID_TIMEFRAMES:
                    data_dir = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}"
                    
                    if not os.path.exists(data_dir):
                        continue
                    
                    # Find all parquet files
                    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
                    
                    if len(parquet_files) <= 1:
                        continue
                    
                    # Sort files by modification time (newest first)
                    parquet_files.sort(key=os.path.getmtime, reverse=True)
                    
                    # Keep the newest file, remove others
                    newest_file = parquet_files[0]
                    for old_file in parquet_files[1:]:
                        try:
                            os.remove(old_file)
                            optimizations['removed_duplicates'] += 1
                            logger.info(f"Removed duplicate data file: {old_file}")
                        except Exception as e:
                            logger.warning(f"Failed to remove {old_file}: {str(e)}")
                    
                    # Optimize the remaining file (if it's large, consider compression)
                    try:
                        import pandas as pd
                        df = pd.read_parquet(newest_file)
                        
                        # Re-save with optimized compression
                        df.to_parquet(newest_file, compression='snappy', index=False)
                        optimizations['optimized_storage'] += 1
                        logger.info(f"Optimized storage for: {newest_file}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to optimize storage for {newest_file}: {str(e)}")
            
            self.optimization_stats['data_storage'] = optimizations
            logger.info(f"[SUCCESS] Data storage optimization completed: {optimizations}")
            return True
            
        except Exception as e:
            logger.error(f"Data storage optimization failed: {str(e)}")
            return False
    
    def optimize_model_storage(self, symbol: str) -> bool:
        """Optimize model storage and organization."""
        logger.info(f"\n=== OPTIMIZING MODEL STORAGE FOR {symbol} ===")
        
        try:
            optimizations = {
                'organized_models': 0,
                'cleaned_old_models': 0,
                'verified_paths': 0
            }
            
            # Ensure proper directory structure
            model_dirs = [
                "model/saved_models/arima",
                "model/saved_models/lstm",
                "model/saved_models/tft",
                "model/saved_models/ensemble",
                "model/visualizations"
            ]
            
            for model_dir in model_dirs:
                if not os.path.exists(model_dir):
                    os.makedirs(model_dir, exist_ok=True)
                    logger.info(f"Created model directory: {model_dir}")
            
            # Clean up old model files that don't follow the standard naming convention
            for model_dir in model_dirs:
                if not os.path.exists(model_dir):
                    continue
                
                model_files = glob.glob(os.path.join(model_dir, "*"))
                for model_file in model_files:
                    if os.path.isfile(model_file):
                        filename = os.path.basename(model_file)
                        
                        # Check if file follows standard naming convention
                        # Standard: {symbol}_{timeframe}_{model_type}.{ext}
                        if not any(tf in filename for tf in VALID_TIMEFRAMES):
                            # This file doesn't follow standard naming
                            try:
                                # Move to backup directory
                                backup_dir = os.path.join(model_dir, "backup")
                                os.makedirs(backup_dir, exist_ok=True)
                                backup_path = os.path.join(backup_dir, filename)
                                shutil.move(model_file, backup_path)
                                optimizations['cleaned_old_models'] += 1
                                logger.info(f"Moved non-standard model file to backup: {filename}")
                            except Exception as e:
                                logger.warning(f"Failed to move {model_file}: {str(e)}")
            
            # Verify model paths are correct
            from utils.path_utils import get_model_path, get_metrics_path, TERMINAL_MODEL_TYPES
            
            for terminal_id in VALID_TERMINAL_IDS:
                model_type = TERMINAL_MODEL_TYPES[terminal_id]
                
                for timeframe in VALID_TIMEFRAMES:
                    model_path = get_model_path(symbol, timeframe, model_type, terminal_id)
                    metrics_path = get_metrics_path(symbol, timeframe, model_type, terminal_id)
                    
                    # Ensure directories exist
                    os.makedirs(os.path.dirname(model_path), exist_ok=True)
                    os.makedirs(os.path.dirname(metrics_path), exist_ok=True)
                    
                    optimizations['verified_paths'] += 1
            
            self.optimization_stats['model_storage'] = optimizations
            logger.info(f"[SUCCESS] Model storage optimization completed: {optimizations}")
            return True
            
        except Exception as e:
            logger.error(f"Model storage optimization failed: {str(e)}")
            return False
    
    def optimize_documentation(self) -> bool:
        """Optimize documentation structure."""
        logger.info("\n=== OPTIMIZING DOCUMENTATION STRUCTURE ===")
        
        try:
            optimizations = {
                'organized_docs': 0,
                'created_structure': 0,
                'updated_docs': 0
            }
            
            # Ensure proper documentation structure
            doc_structure = {
                "documents/01_system_architecture": [
                    "system_overview.md",
                    "terminal_configuration.md",
                    "data_flow_diagram.md",
                    "component_relationships.md"
                ],
                "documents/02_data_management": [
                    "data_collection_process.md",
                    "data_validation_procedures.md",
                    "storage_organization.md",
                    "quality_assurance.md"
                ],
                "documents/03_model_training": [
                    "model_architectures.md",
                    "training_procedures.md",
                    "ensemble_methods.md",
                    "performance_metrics.md"
                ],
                "documents/04_trading_operations": [
                    "trading_strategies.md",
                    "risk_management.md",
                    "execution_procedures.md",
                    "monitoring_protocols.md"
                ],
                "documents/05_deployment_monitoring": [
                    "deployment_guide.md",
                    "performance_monitoring.md",
                    "troubleshooting_guide.md",
                    "maintenance_procedures.md"
                ]
            }
            
            # Create directory structure
            for doc_dir, doc_files in doc_structure.items():
                if not os.path.exists(doc_dir):
                    os.makedirs(doc_dir, exist_ok=True)
                    optimizations['created_structure'] += 1
                    logger.info(f"Created documentation directory: {doc_dir}")
                
                # Create placeholder files if they don't exist
                for doc_file in doc_files:
                    doc_path = os.path.join(doc_dir, doc_file)
                    if not os.path.exists(doc_path):
                        with open(doc_path, 'w', encoding='utf-8') as f:
                            f.write(f"# {doc_file.replace('_', ' ').replace('.md', '').title()}\n\n")
                            f.write(f"*This document is part of the {doc_dir.split('/')[-1]} documentation.*\n\n")
                            f.write("## Overview\n\n")
                            f.write("*Content to be added.*\n\n")
                            f.write(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        optimizations['updated_docs'] += 1
                        logger.info(f"Created documentation file: {doc_path}")
            
            # Create main documentation index
            index_path = "documents/README.md"
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write("# MT5 Multi-Terminal Trading System Documentation\n\n")
                f.write("This documentation provides comprehensive information about the MT5 multi-terminal trading system.\n\n")
                f.write("## Documentation Structure\n\n")
                
                for doc_dir, doc_files in doc_structure.items():
                    section_name = doc_dir.split('/')[-1].replace('_', ' ').title()
                    f.write(f"### {section_name}\n\n")
                    for doc_file in doc_files:
                        file_title = doc_file.replace('_', ' ').replace('.md', '').title()
                        f.write(f"- [{file_title}]({doc_dir}/{doc_file})\n")
                    f.write("\n")
                
                f.write(f"\nLast updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            optimizations['organized_docs'] += 1
            
            self.optimization_stats['documentation'] = optimizations
            logger.info(f"[SUCCESS] Documentation optimization completed: {optimizations}")
            return True
            
        except Exception as e:
            logger.error(f"Documentation optimization failed: {str(e)}")
            return False
    
    def generate_optimization_report(self) -> None:
        """Generate a comprehensive optimization report."""
        logger.info("\n" + "="*80)
        logger.info("COMPREHENSIVE SYSTEM OPTIMIZATION REPORT")
        logger.info("="*80)
        
        total_optimizations = 0
        
        for category, stats in self.optimization_stats.items():
            logger.info(f"\n🔧 {category.upper().replace('_', ' ')}:")
            category_total = 0
            for metric, count in stats.items():
                logger.info(f"  {metric.replace('_', ' ').title()}: {count}")
                category_total += count
            logger.info(f"  Total: {category_total}")
            total_optimizations += category_total
        
        logger.info(f"\n[SUMMARY] OVERALL OPTIMIZATION SUMMARY:")
        logger.info(f"Total optimizations performed: {total_optimizations}")
        logger.info(f"Categories optimized: {len(self.optimization_stats)}")

        # System status
        logger.info("\n" + "="*80)
        logger.info("[SUCCESS] SYSTEM OPTIMIZATION COMPLETED SUCCESSFULLY")
        logger.info("\nNext steps:")
        logger.info("1. Run system validation:")
        logger.info("   python scripts/validate_and_train_system.py")
        logger.info("2. Start optimized data collection:")
        logger.info("   python scripts/optimize_data_collection.py --parallel")
        logger.info("3. Begin trading operations:")
        logger.info("   python run_trading_bot.py --multi-terminal")
        logger.info("="*80)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Comprehensive system optimization')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Symbol to optimize for')
    parser.add_argument('--years', type=int, default=5, help='Years of historical data')
    parser.add_argument('--optimize-code', action='store_true', help='Optimize code structure')
    parser.add_argument('--optimize-data', action='store_true', help='Optimize data storage')
    parser.add_argument('--optimize-models', action='store_true', help='Optimize model storage')
    parser.add_argument('--optimize-docs', action='store_true', help='Organize documentation')
    parser.add_argument('--full-optimization', action='store_true', help='Perform all optimizations')
    
    args = parser.parse_args()
    
    # If full optimization is requested, enable all optimizations
    if args.full_optimization:
        args.optimize_code = True
        args.optimize_data = True
        args.optimize_models = True
        args.optimize_docs = True
    
    logger.info("Starting comprehensive system optimization...")
    logger.info(f"Symbol: {args.symbol}")
    logger.info(f"Optimize code: {args.optimize_code}")
    logger.info(f"Optimize data: {args.optimize_data}")
    logger.info(f"Optimize models: {args.optimize_models}")
    logger.info(f"Optimize docs: {args.optimize_docs}")
    
    # Initialize optimizer
    optimizer = SystemOptimizer()
    
    success_count = 0
    total_count = 0
    
    # Perform optimizations
    if args.optimize_code:
        total_count += 1
        if optimizer.optimize_code_structure():
            success_count += 1
    
    if args.optimize_data:
        total_count += 1
        if optimizer.optimize_data_storage(args.symbol):
            success_count += 1
    
    if args.optimize_models:
        total_count += 1
        if optimizer.optimize_model_storage(args.symbol):
            success_count += 1
    
    if args.optimize_docs:
        total_count += 1
        if optimizer.optimize_documentation():
            success_count += 1
    
    # Generate report
    optimizer.generate_optimization_report()
    
    if success_count == total_count and total_count > 0:
        logger.info("All optimizations completed successfully!")
        return 0
    else:
        logger.warning(f"Some optimizations failed: {success_count}/{total_count} successful")
        return 1

if __name__ == '__main__':
    sys.exit(main())
