"""
Test script for terminal manager.
"""
import sys
import logging
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append('.')

# Import the terminal manager
from crypto_trading_bot.core.terminal_manager import TerminalManager

def test_terminal_manager():
    """Test the terminal manager."""
    logger.info("Testing terminal manager...")
    
    # Create a terminal manager
    manager = TerminalManager()
    
    # Initialize terminals
    if manager.initialize_terminals():
        logger.info("Successfully initialized terminals")
        
        # Get active terminals
        active_terminals = manager.get_active_terminals()
        logger.info(f"Active terminals: {active_terminals}")
        
        # Check if Algo Trading is enabled
        if manager.check_algo_trading_enabled():
            logger.info("Algo Trading is ENABLED")
        else:
            logger.warning("Algo Trading is DISABLED")
            logger.info("Please enable Algo Trading in the MT5 terminal")
        
        # Shutdown terminals
        manager.shutdown_terminals()
        logger.info("Successfully shutdown terminals")
    else:
        logger.error("Failed to initialize terminals")
        return 1
    
    return 0

def main():
    """Main function."""
    logger.info("Running terminal manager tests...")
    
    # Test the terminal manager
    result = test_terminal_manager()
    
    if result == 0:
        logger.info("All tests passed")
        return 0
    else:
        logger.error("Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
