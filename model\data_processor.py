"""
Data Processor Module.
This module handles data preprocessing for model training.
"""
import logging
import numpy as np
import pandas as pd
from typing import List, Tuple
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from config.trading_config import TRADING_CONFIG

# Configure logger
logger = logging.getLogger('model.data_processor')

class DataProcessor:
    """
    Data Processor class for preparing data for model training.
    """
    def __init__(
        self,
        sequence_length: int = None,
        target_column: str = 'returns',
        scaler_type: str = 'standard'
    ):
        """
        Initialize DataProcessor.

        Args:
            sequence_length: Length of sequences for time series models
            target_column: Target column for prediction
            scaler_type: Type of scaler ('standard' or 'minmax')
        """
        self.sequence_length = sequence_length or TRADING_CONFIG['model']['sequence_length']
        self.target_column = target_column
        self.scaler_type = scaler_type
        self.feature_scaler = None
        self.target_scaler = None
        
    def _create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create features from raw data.
        
        Args:
            df: Raw data
            
        Returns:
            pd.DataFrame: Data with features
        """
        # Make a copy to avoid modifying the original
        data = df.copy()
        
        # Calculate returns
        data['returns'] = data['close'].pct_change()
        
        # Calculate log returns
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # Calculate volatility
        data['volatility'] = data['returns'].rolling(window=20).std()
        
        # Calculate price momentum
        data['momentum_1'] = data['returns'].shift(1)
        data['momentum_5'] = data['returns'].rolling(window=5).mean().shift(1)
        data['momentum_10'] = data['returns'].rolling(window=10).mean().shift(1)
        
        # Calculate moving averages
        data['sma_5'] = data['close'].rolling(window=5).mean()
        data['sma_10'] = data['close'].rolling(window=10).mean()
        data['sma_20'] = data['close'].rolling(window=20).mean()
        
        # Calculate exponential moving averages
        data['ema_5'] = data['close'].ewm(span=5, adjust=False).mean()
        data['ema_10'] = data['close'].ewm(span=10, adjust=False).mean()
        data['ema_20'] = data['close'].ewm(span=20, adjust=False).mean()
        
        # Calculate MACD
        data['macd'] = data['ema_12'] - data['ema_26']
        data['macd_signal'] = data['macd'].ewm(span=9, adjust=False).mean()
        data['macd_hist'] = data['macd'] - data['macd_signal']
        
        # Calculate RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # Calculate Bollinger Bands
        data['bb_middle'] = data['close'].rolling(window=20).mean()
        data['bb_std'] = data['close'].rolling(window=20).std()
        data['bb_upper'] = data['bb_middle'] + 2 * data['bb_std']
        data['bb_lower'] = data['bb_middle'] - 2 * data['bb_std']
        data['bb_width'] = (data['bb_upper'] - data['bb_lower']) / data['bb_middle']
        
        # Calculate volume features
        data['volume_change'] = data['volume'].pct_change()
        data['volume_ma_5'] = data['volume'].rolling(window=5).mean()
        data['volume_ma_10'] = data['volume'].rolling(window=10).mean()
        
        # Drop NaN values
        data = data.dropna()
        
        return data
        
    def _scale_data(self, X: np.ndarray, y: np.ndarray, fit: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """
        Scale features and target.
        
        Args:
            X: Features
            y: Target
            fit: Whether to fit the scaler
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: Scaled features and target
        """
        if fit:
            # Create scalers
            if self.scaler_type == 'standard':
                self.feature_scaler = StandardScaler()
                self.target_scaler = StandardScaler()
            else:
                self.feature_scaler = MinMaxScaler()
                self.target_scaler = MinMaxScaler()
                
            # Fit scalers
            if len(X.shape) == 3:
                # For 3D data (batch_size, sequence_length, features)
                # Reshape to 2D for scaling
                batch_size, seq_len, n_features = X.shape
                X_2d = X.reshape(-1, n_features)
                X_scaled_2d = self.feature_scaler.fit_transform(X_2d)
                X_scaled = X_scaled_2d.reshape(batch_size, seq_len, n_features)
            else:
                # For 2D data
                X_scaled = self.feature_scaler.fit_transform(X)
                
            # Scale target
            if len(y.shape) == 1:
                y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
            else:
                y_scaled = self.target_scaler.fit_transform(y)
        else:
            # Transform using existing scalers
            if len(X.shape) == 3:
                # For 3D data
                batch_size, seq_len, n_features = X.shape
                X_2d = X.reshape(-1, n_features)
                X_scaled_2d = self.feature_scaler.transform(X_2d)
                X_scaled = X_scaled_2d.reshape(batch_size, seq_len, n_features)
            else:
                # For 2D data
                X_scaled = self.feature_scaler.transform(X)
                
            # Scale target
            if len(y.shape) == 1:
                y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
            else:
                y_scaled = self.target_scaler.transform(y)
                
        return X_scaled, y_scaled
        
    def _create_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Create sequences for time series models with optimized performance.

        Args:
            data: Data with features

        Returns:
            Tuple[np.ndarray, np.ndarray, List[str]]: Features, target, and feature names
        """
        # Get feature columns (all except target)
        feature_columns = [col for col in data.columns if col != self.target_column]

        # Calculate number of sequences
        num_sequences = len(data) - self.sequence_length
        if num_sequences <= 0:
            return np.array([]), np.array([]), feature_columns

        # OPTIMIZED: Pre-allocate arrays and use vectorized operations
        feature_data = data[feature_columns].values.astype(np.float32)
        target_data = data[self.target_column].values.astype(np.float32)

        # Pre-allocate result arrays
        X = np.zeros((num_sequences, self.sequence_length, len(feature_columns)), dtype=np.float32)
        y = np.zeros(num_sequences, dtype=np.float32)

        # Vectorized sequence creation
        for i in range(num_sequences):
            X[i] = feature_data[i:i+self.sequence_length]
            y[i] = target_data[i+self.sequence_length]

        return X, y, feature_columns
        
    def prepare_data(
        self,
        df: pd.DataFrame,
        sequence_length: int = None,
        train_split: float = None,
        val_split: float = None
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, List[str]]:
        """
        Prepare data for model training.
        
        Args:
            df: Raw data
            sequence_length: Length of sequences for time series models
            train_split: Train split ratio
            val_split: Validation split ratio
            
        Returns:
            Tuple: X_train, X_val, X_test, y_train, y_val, y_test, feature_names
        """
        # Set parameters
        self.sequence_length = sequence_length or self.sequence_length
        train_split = train_split or TRADING_CONFIG['model']['train_test_split']
        val_split = val_split or TRADING_CONFIG['model']['validation_split']
        
        # Create features
        data = self._create_features(df)
        
        # Create sequences
        X, y, feature_names = self._create_sequences(data)
        
        # Split data
        train_size = int(len(X) * train_split)
        val_size = int(len(X) * val_split)
        
        X_train, y_train = X[:train_size], y[:train_size]
        X_val, y_val = X[train_size:train_size+val_size], y[train_size:train_size+val_size]
        X_test, y_test = X[train_size+val_size:], y[train_size+val_size:]
        
        # Scale data
        X_train, y_train = self._scale_data(X_train, y_train, fit=True)
        X_val, y_val = self._scale_data(X_val, y_val, fit=False)
        X_test, y_test = self._scale_data(X_test, y_test, fit=False)
        
        return X_train, X_val, X_test, y_train, y_val, y_test, feature_names
