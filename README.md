# MT5 Multi-Terminal Trading System

A sophisticated multi-terminal MetaTrader 5 trading system with advanced machine learning models for automated trading across multiple timeframes and symbols.

## 🚀 Quick Start

### System Setup
```bash
# Complete system setup with optimization
python scripts/master_system_setup.py --symbol BTCUSD.a --years 5

# Quick setup for testing (faster, minimal data)
python scripts/master_system_setup.py --quick-setup --symbol BTCUSD.a
```

### Manual Setup
```bash
# 1. System optimization
python scripts/comprehensive_system_optimization.py --full-optimization

# 2. Data collection
python scripts/optimize_data_collection.py --symbol BTCUSD.a --parallel --validate

# 3. System validation and training
python scripts/validate_and_train_system.py --symbol BTCUSD.a --years 5

# 4. Start trading
python run_trading_bot.py --dashboard
```

## 🏗️ System Architecture

### Terminal-Model Mapping
| Terminal | Model Type | Purpose | Storage Path |
|----------|------------|---------|--------------|
| Terminal 1 | ARIMA | Time series forecasting | `model/saved_models/arima/` |
| Terminal 2 | LSTM | Deep learning sequences | `model/saved_models/lstm/` |
| Terminal 3 | TFT | Temporal fusion transformer | `model/saved_models/tft/` |
| Terminal 4 | LSTM + ARIMA | Ensemble combination | `model/saved_models/ensemble/` |
| Terminal 5 | TFT + ARIMA | Advanced ensemble | `model/saved_models/ensemble/` |

### Data Organization
```
data/storage/historical/
├── terminal_1/
│   ├── BTCUSD.a_M5/
│   ├── BTCUSD.a_M15/
│   ├── BTCUSD.a_M30/
│   ├── BTCUSD.a_H1/
│   └── BTCUSD.a_H4/
├── terminal_2/
│   └── ... (same structure)
└── ... (terminals 3-5)
```

### Model Storage Structure
```
model/saved_models/
├── arima/
│   ├── BTCUSD.a_M5_arima.pkl
│   ├── BTCUSD.a_M5_arima_metrics.json
│   └── ... (all timeframes)
├── lstm/
│   ├── BTCUSD.a_M5_lstm.pth
│   ├── BTCUSD.a_M5_lstm_metrics.json
│   └── ... (all timeframes)
├── tft/
│   └── ... (similar structure)
└── ensemble/
    ├── BTCUSD.a_M5_lstm_arima.pkl
    ├── BTCUSD.a_M5_tft_arima.pkl
    └── ... (ensemble models)
```

## 🔧 Features

### Core Functionality
- **Multi-Terminal Management**: Simultaneous operation of 5 MT5 terminals
- **Advanced ML Models**: LSTM, TFT, ARIMA, and ensemble combinations
- **Automated Data Collection**: Historical data across multiple timeframes (M5, M15, M30, H1, H4)
- **Real-time Trading**: Signal generation and automated execution
- **Performance Monitoring**: Comprehensive analytics and visualization
- **Risk Management**: Built-in position sizing and risk controls

### Optimization Features
- **Parallel Data Collection**: Faster data gathering with concurrent processing
- **Model Performance Comparison**: Statistical significance testing across models
- **Automated Model Selection**: Best model identification per timeframe
- **System Validation**: Comprehensive health checks and quality assurance
- **Documentation Organization**: Structured documentation in 5 logical categories

- **Multi-Symbol Support**:
  - Trade any symbol in format 'XXXYYY.a'
  - Independent configuration for each symbol
  - Automatic data collection and validation

- **Data-Driven Timeframe Selection**:
  - Automatically selects the best timeframe for each model and symbol
  - Supported timeframes: M5, M15, M30, H1, H4
  - Based on performance metrics (direction accuracy, profit factor, Sharpe ratio)

- **Advanced Trading Features**:
  - Dynamic Stop-Loss and Take-Profit based on market volatility
  - Continuous learning and adaptation to changing market conditions
  - Comprehensive performance metrics tracking
  - Model decay detection and monitoring
  - Walk-forward optimization for realistic performance assessment
  - Efficient data storage using Parquet format
  - Thorough data validation and quality assurance

## Project Structure

```
MT5 Trading System/
├── analysis/                # Market analysis and feature engineering
│   ├── feature_engineering.py  # Feature engineering and preprocessing
│   └── visualization/       # Advanced visualization tools
├── config/                  # Configuration files
│   ├── credentials.py       # MT5 terminal configuration
│   ├── trading_config.py    # Trading parameters
│   └── logging_config.py    # Logging configuration
├── data/                    # Data collection and preprocessing
│   ├── data_collector.py    # Data collection from MT5
│   ├── data_manager.py      # Data management with Parquet storage
│   └── validation/          # Data validation tools
├── documents/               # Detailed documentation
│   ├── 01_system_architecture/  # System architecture docs
│   ├── 02_data_management/      # Data management docs
│   ├── 03_model_training/       # Model training docs
│   ├── 04_trading_operations/   # Trading operations docs
│   └── 05_deployment_monitoring/ # Deployment and monitoring docs
├── logs/                    # Log files
├── model/                   # Machine learning models
│   ├── model_manager.py     # Model management and training
│   ├── model_evaluator.py   # Model evaluation and testing
│   ├── arima_model.py       # ARIMA model implementation
│   ├── lstm_model.py        # LSTM model implementation
│   ├── tft_model.py         # TFT model implementation
│   ├── ensemble_model.py    # Ensemble model implementation
│   └── saved_models/        # Saved model files
├── monitoring/              # Performance monitoring and visualization
│   ├── performance_monitor.py  # Performance tracking
│   └── dashboard.py         # Real-time dashboard
├── scripts/                 # Utility scripts
│   ├── master_system_setup.py  # Complete system setup
│   ├── comprehensive_system_optimization.py  # System optimization
│   ├── optimize_data_collection.py  # Data collection optimization
│   ├── validate_and_train_system.py  # System validation and training
│   ├── add_symbol.py        # Add new trading symbols
│   ├── initialize_terminal_models.py  # Initialize terminal models
│   └── optimize_timeframes.py  # Timeframe optimization
├── trading/                 # Trading strategy and order execution
│   ├── mt5_connector.py     # MT5 connection and order execution
│   ├── order_manager.py     # Order management
│   └── trading_strategy.py  # Trading strategy implementation
├── utils/                   # Utility functions
│   ├── mt5_initializer.py   # MT5 initialization
│   ├── path_utils.py        # Standardized path utilities
│   └── mt5_connection_manager.py  # MT5 connection management
├── README.md                # Project documentation
├── run_trading_bot.py       # Main execution script
├── trading_bot.py           # Main trading bot class
├── train_models.py          # Model training script
├── train_improved_models.py # Improved model training
└── generate_dashboard.py    # Dashboard generation
```

## Installation

1. Clone the repository:
```
git clone https://github.com/r70pro/BTCUSD-Trading-Bot.git
cd BTCUSD-Trading-Bot
```

2. Install the required packages:
```
pip install -r requirements.txt
```

3. Configure your MT5 terminals in `config/credentials.py`.

## Documentation

All documentation is organized in the `documents` directory:

### System Architecture
- [System Overview](documents/01_system_architecture/system_overview.md)
- [Terminal Configuration](documents/01_system_architecture/terminal_configuration.md)
- [Data Flow Diagram](documents/01_system_architecture/data_flow_diagram.md)
- [Component Relationships](documents/01_system_architecture/component_relationships.md)

### Data Management
- [Data Collection Process](documents/02_data_management/data_collection_process.md)
- [Data Validation Procedures](documents/02_data_management/data_validation_procedures.md)
- [Storage Organization](documents/02_data_management/storage_organization.md)
- [Quality Assurance](documents/02_data_management/quality_assurance.md)

### Model Training
- [Model Architectures](documents/03_model_training/model_architectures.md)
- [Training Procedures](documents/03_model_training/training_procedures.md)
- [Ensemble Methods](documents/03_model_training/ensemble_methods.md)
- [Performance Metrics](documents/03_model_training/performance_metrics.md)

### Trading Operations
- [Trading Strategies](documents/04_trading_operations/trading_strategies.md)
- [Risk Management](documents/04_trading_operations/risk_management.md)
- [Execution Procedures](documents/04_trading_operations/execution_procedures.md)
- [Monitoring Protocols](documents/04_trading_operations/monitoring_protocols.md)

### Deployment and Monitoring
- [Deployment Guide](documents/05_deployment_monitoring/deployment_guide.md)
- [System Monitoring](documents/05_deployment_monitoring/system_monitoring.md)
- [Performance Tracking](documents/05_deployment_monitoring/performance_tracking.md)
- [Maintenance Procedures](documents/05_deployment_monitoring/maintenance_procedures.md)

## Usage

### Adding a Trading Symbol

To add a new symbol to the system:

```bash
python scripts/add_symbol.py XXXYYY.a
```

Options:
- `--years N`: Number of years of historical data to collect (default: 5)
- `--no-validate`: Skip data validation
- `--terminals N [N ...]`: Specific terminal IDs to collect data from

Example:
```bash
python scripts/add_symbol.py ETHUSD.a --years 3 --terminals 1 2 3
```

### Initializing Models with Optimal Timeframes

To initialize models for all terminals with the best timeframe for each model type:

```bash
python scripts/initialize_terminal_models.py
```

Options:
- `--terminals 1 3 5`: Initialize specific terminals
- `--symbol ETHUSD.a`: Initialize for a specific symbol
- `--force-retrain`: Force retraining of existing models

### Timeframe Optimization

To run timeframe optimization once:

```bash
python scripts/optimize_timeframes.py
```

To schedule periodic optimization:

```bash
python scripts/optimize_timeframes.py --schedule --interval 168
```

### Training Models

To train models for specific timeframes:

```bash
python train_models.py --start-date 2023-01-01 --end-date 2024-04-01
```

You can customize the training parameters:

```bash
python train_improved_models.py --start-date 2023-01-01 --end-date 2024-04-01
```

### Running a Backtest

```bash
python run_trading_bot.py --backtest --start-date 2020-01-01 --end-date 2023-01-01
```

### Running Walk-forward Optimization

```bash
python run_trading_bot.py --optimize --start-date 2020-01-01 --end-date 2023-01-01
```

### Generating a Performance Report

```bash
python run_trading_bot.py --report
```

### Visualization and Dashboard

```bash
python run_trading_bot.py --dashboard
```

The dashboard provides a comprehensive view of the trading system's performance, including:

- Interactive price charts with technical indicators
- Equity curve and drawdown analysis
- Trade distribution and performance metrics
- Feature importance and correlation analysis
- Model performance and decay monitoring
- Data validation results

### Data Validation

The system includes comprehensive data validation tools with performance optimizations:

```python
from data.validation.data_validator import DataValidator

# Initialize validator
data_validator = DataValidator()

# Add validation rules
data_validator.add_rule(
    rule_name="missing_values_check",
    rule_type="missing_values",
    parameters={
        "columns": ["open", "high", "low", "close", "volume"],
        "threshold": 0.05
    }
)

# Validate data with parallel execution for large datasets
validation_results = data_validator.validate_data(
    df=historical_data,
    parallel=True,
    max_workers=4  # Use 4 CPU cores
)

# Generate comprehensive validation report
report = data_validator.generate_validation_report()
```

Key features:
- Parallel execution for large datasets
- Statistical caching for improved performance
- Comprehensive validation reports
- Robust error handling
- Detailed logging for debugging

### Starting the Trading Bot

#### Step 1: Ensure MT5 Terminals Are Running

Before starting the trading bot, make sure all MT5 terminals are running and Algo Trading is enabled in each terminal:

1. Open all 5 MT5 terminals
2. In each terminal, click the "Algo Trading" button in the toolbar to enable automated trading
3. Verify that the "Algo Trading" button is highlighted, indicating that it's enabled

#### Step 2: Download Recent Data (Recommended)

Before training the models, it's recommended to download the most recent 2 years of BTCUSD data:

```bash
python download_recent_data.py
```

You can customize the date range and timeframes:

```bash
python download_recent_data.py --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

Alternatively, you can use the underlying script directly:

```bash
python scripts/collect_historical_data.py --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

#### Step 3: Train the Models (Optional)

After downloading the data, you can train or update the models with the most recent 2 years of data:

```bash
python train_models.py --start-date 2023-04-25 --end-date 2025-04-25
```

Or train improved models with advanced parameters:

```bash
python train_improved_models.py --start-date 2023-04-25 --end-date 2025-04-25
```

#### Step 4: Start the Trading Bot

To start the trading bot in normal trading mode:

```bash
python run_trading_bot.py
```

To start with the dashboard for real-time monitoring:

```bash
python run_trading_bot.py --dashboard
```

To train models and then start the bot:

```bash
python run_trading_bot.py --train --start-date 2023-04-25 --end-date 2025-04-25
```

For running multiple trading bots simultaneously:

```bash
python scripts/run_trading_bots.py
```

#### Step 5: Monitor Performance

Once the bot is running, you can monitor its performance through:

1. The console output showing signals and trades
2. The dashboard (if enabled) showing real-time performance metrics
3. Log files in the `logs` directory

#### Step 6: Stopping the Bot

To stop the bot, press `Ctrl+C` in the console where it's running. The bot will gracefully shut down, closing any necessary connections.

## Command-Line Reference

The main script `run_trading_bot.py` supports the following arguments:

### Basic Options
- `--terminal ID`: Specify MT5 terminal ID (default: 1)
- `--skip-terminal-open`: Skip opening MT5 terminals automatically
- `--symbol SYMBOL`: Trading symbol (default: BTCUSD.a)

### Trading Operations
- `--train`: Train models
- `--backtest`: Run backtest
- `--optimize`: Run walk-forward optimization
- `--report`: Generate performance report
- `--dashboard`: Start dashboard only

### Date Range Options
- `--start-date DATE`: Start date for training/backtest (default: 2022-01-01)
- `--end-date DATE`: End date for training/backtest (default: current date)

### Visualization Options
- `--generate-dashboard`: Generate visualization dashboard
- `--sample-dashboard`: Generate sample dashboard with dummy data
- `--data-dir DIR`: Data directory for dashboard generation (default: data/storage)
- `--output-dir DIR`: Output directory for dashboard (default: monitoring/dashboard)
- `--days N`: Number of days for sample data generation (default: 90)

### Examples
```bash
# Start trading bot with dashboard
python run_trading_bot.py --dashboard

# Train models for specific date range
python run_trading_bot.py --train --start-date 2023-01-01 --end-date 2024-01-01

# Run backtest
python run_trading_bot.py --backtest --start-date 2023-01-01 --end-date 2024-01-01

# Generate sample dashboard
python run_trading_bot.py --sample-dashboard --days 30

# Skip terminal opening and start specific terminal
python run_trading_bot.py --skip-terminal-open --terminal 2
```

## MT5 Terminal Configuration

The system uses a specialized 5-terminal setup, with each terminal assigned a specific model type and trading strategy:

1. **Terminal ID 1 - ARIMA Model**:
   - Model Type: ARIMA (AutoRegressive Integrated Moving Average)
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Conservative
   - Trading Style: Statistical-based, mean-reversion
   - Position Size: 0.5-1% risk per trade
   - Stop Loss: Tighter (1.0x ATR multiplier)
   - Take Profit: 2:1 reward-to-risk ratio

2. **Terminal ID 2 - LSTM Model**:
   - Model Type: LSTM (Long Short-Term Memory)
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Moderate
   - Trading Style: Deep learning-based, pattern recognition
   - Position Size: 1-2% risk per trade
   - Stop Loss: Balanced (1.5x ATR multiplier)
   - Take Profit: 2.5:1 reward-to-risk ratio

3. **Terminal ID 3 - TFT Model**:
   - Model Type: TFT (Temporal Fusion Transformer)
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Moderate-Aggressive
   - Trading Style: Advanced pattern recognition with attention mechanisms
   - Position Size: 1.5-2.5% risk per trade
   - Stop Loss: Wider (2.0x ATR multiplier)
   - Take Profit: 3:1 reward-to-risk ratio

4. **Terminal ID 4 - LSTM+ARIMA Ensemble**:
   - Model Type: Ensemble combining LSTM and ARIMA
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Aggressive
   - Trading Style: Hybrid approach combining statistical and deep learning
   - Position Size: 2-3% risk per trade
   - Stop Loss: Strategic (2.5x ATR multiplier)
   - Take Profit: 3.5:1 reward-to-risk ratio

5. **Terminal ID 5 - TFT+ARIMA Ensemble with Multi-Timeframe**:
   - Model Type: Ensemble combining TFT and ARIMA with multi-timeframe analysis
   - Timeframe: Multiple timeframes with weighted signals
   - Risk Profile: Dynamic
   - Trading Style: Confirmation-based, high-probability setups
   - Position Size: Dynamic (0.5-3% based on signal strength)
   - Stop Loss: Adaptive based on volatility
   - Take Profit: Determined by confluence points
   - Signal Fusion Weights:
     - M5: 25%
     - M15: 20%
     - M30: 20%
     - H1: 20%
     - H4: 15%

This multi-model, multi-timeframe approach allows the system to:
1. Capture different market dynamics and trading opportunities
2. Leverage the strengths of each model type
3. Automatically select the optimal timeframe for each model and symbol
4. Provide high-probability trade setups based on model consensus

## Performance Metrics

The bot tracks the following performance metrics:

- Total Return
- Sharpe Ratio
- Sortino Ratio
- Maximum Drawdown
- Win Rate
- Profit Factor
- Expectancy
- Average Trade Duration

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

Trading cryptocurrencies involves significant risk and can result in the loss of your invested capital. The BTCUSD Trading Bot is provided for educational and informational purposes only. Always consult with a qualified professional before making any investment decisions.
