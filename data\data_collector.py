"""
Data Collector Module.
This module handles data collection from MT5 terminals and provides methods for data preprocessing.

It includes functionality for:
- Collecting historical data from MT5 terminals
- Collecting data from multiple terminals and merging it
- Preprocessing and validating data
- Saving and loading data from storage
"""
import logging
import time
import traceback
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
import pandas as pd
import MetaTrader5 as mt5
from trading.mt5_connector import MT5Connector
from config.trading_config import TRADING_CONFIG

from data.data_manager import DataManager
from data.validation.data_validator import DataValidator
from data.technical_indicators import add_all_indicators
from utils.mt5_initializer import initialize_mt5_once, initialize_all_terminals

# Configure logger
logger = logging.getLogger('data.collector')

class DataCollector:
    """
    Data Collector class for handling data collection from MT5 terminals and data preprocessing.
    """
    def __init__(self, mt5_connector: MT5Connector, terminal_id: Optional[int] = None):
        """
        Initialize DataCollector.

        Args:
            mt5_connector: MT5Connector instance
            terminal_id: Terminal ID (optional)
        """
        self.mt5 = mt5_connector
        self.terminal_id = terminal_id
        self.symbol = TRADING_CONFIG.get('symbol', 'BTCUSD.a')  # Default symbol
        self.timeframes = TRADING_CONFIG.get('timeframes', {'M5': {}, 'M15': {}, 'M30': {}, 'H1': {}, 'H4': {}})
        self.data_cache = {}  # Cache for historical data (symbol_timeframe -> data)
        self.data_manager = DataManager()  # Initialize data manager
        self.auto_save = TRADING_CONFIG.get('auto_save_data', True)  # Auto-save data flag

        # Initialize data validator (with default rules)
        self.data_validator = DataValidator()

    def _generate_timeframe_data(self, timeframe: str, start_date: str, end_date: str = None, symbol: str = None) -> Optional[pd.DataFrame]:
        """
        Generate data for a specific timeframe from M1 data if direct retrieval fails.
        This method can be used as a fallback when direct timeframe data retrieval fails.

        Args:
            timeframe: Target timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format (optional)
            symbol: Symbol to get data for (defaults to self.symbol)

        Returns:
            pd.DataFrame: Resampled data or None if failed
        """
        # Use provided symbol or default to self.symbol
        symbol_to_use = symbol if symbol is not None else self.symbol

        logger.info(f"Attempting to generate {timeframe} data from M1 data for {symbol_to_use}")

        # Map timeframe to pandas resample rule
        timeframe_to_rule = {
            'M5': '5T',
            'M15': '15T',
            'M30': '30T',
            'H1': '1H',
            'H4': '4H',
            'D1': '1D'
        }

        if timeframe not in timeframe_to_rule:
            logger.error(f"Unsupported timeframe for generation: {timeframe}")
            return None

        resample_rule = timeframe_to_rule[timeframe]

        # Get M1 data with a slightly extended range to ensure we have enough data
        # For higher timeframes like H4, we need more padding
        padding_days = 1
        if timeframe in ['H1', 'H4', 'D1']:
            padding_days = 5  # Use more padding for higher timeframes

        start_date_dt = pd.to_datetime(start_date) - timedelta(days=padding_days)
        # Ensure date is timezone-naive
        if start_date_dt.tzinfo is not None:
            start_date_dt = start_date_dt.replace(tzinfo=None)
        start_date_extended = start_date_dt.strftime('%Y-%m-%d')

        if end_date:
            end_date_dt = pd.to_datetime(end_date) + timedelta(days=padding_days)
            # Ensure date is timezone-naive
            if end_date_dt.tzinfo is not None:
                end_date_dt = end_date_dt.replace(tzinfo=None)
            end_date_extended = end_date_dt.strftime('%Y-%m-%d')
        else:
            end_date_extended = None

        logger.info(f"Extended date range for M1 data: {start_date_extended} to {end_date_extended or 'now'}")

        # Try to get M1 data from storage first
        logger.info(f"Attempting to load M1 data from storage for {symbol_to_use}...")
        m1_data = self.load_historical_data_from_storage('M1', start_date_extended, end_date_extended, symbol=symbol_to_use)

        # If not available in storage, get from MT5
        if m1_data is None or len(m1_data) == 0:
            logger.info(f"M1 data not found in storage for {symbol_to_use}, retrieving from MT5...")
            m1_data = self.mt5.get_historical_data(
                symbol=symbol_to_use,
                timeframe='M1',
                start_date=start_date_extended,
                end_date=end_date_extended
            )

        if m1_data is None or len(m1_data) == 0:
            logger.error(f"Failed to get M1 data for {symbol_to_use} to generate {timeframe} data")
            return None

        # Resample to target timeframe
        logger.info(f"Resampling {len(m1_data)} M1 bars to {timeframe} for {symbol_to_use}")
        resampled_data = m1_data.resample(resample_rule).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'tick_volume': 'sum',
            'spread': 'mean',
            'real_volume': 'sum'
        })

        # Filter to the requested date range
        resampled_data = resampled_data.loc[start_date:end_date] if end_date else resampled_data.loc[start_date:]

        logger.info(f"Generated {len(resampled_data)} {timeframe} bars from M1 data for {symbol_to_use}")
        return resampled_data

    def get_historical_data(
        self,
        timeframe: str,
        start_date: str,
        end_date: str = None,
        num_bars: int = None,
        use_cache: bool = True,
        symbol: str = None,
        skip_validation: bool = False
    ) -> Optional[pd.DataFrame]:
        """
        Get historical data for the symbol.
        This method follows the MT5 connection guide best practices by using the MT5Connector
        which ensures Algo Trading remains enabled.

        Args:
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format (optional)
            num_bars: Number of bars to retrieve (optional)
            use_cache: Whether to use cached data if available
            symbol: Symbol to get data for (defaults to self.symbol)
            skip_validation: Whether to skip data validation (faster for large datasets)

        Returns:
            pd.DataFrame: Historical data or None if failed
        """
        try:
            # Use provided symbol or default to self.symbol
            symbol_to_use = symbol if symbol is not None else self.symbol

            # Check if data is in cache (fast in-memory lookup)
            cache_key = f"{symbol_to_use}_{timeframe}_{start_date}_{end_date}_{num_bars}"
            if use_cache and cache_key in self.data_cache:
                logger.debug(f"Using cached data for {symbol_to_use} ({timeframe})")
                return self.data_cache[cache_key].copy()

            # Try to load from storage first (faster than MT5 retrieval)
            if start_date and not num_bars:
                logger.debug(f"Attempting to load {timeframe} data from storage for {symbol_to_use}...")
                stored_df = self.load_historical_data_from_storage(
                    timeframe=timeframe,
                    start_date=start_date,
                    end_date=end_date,
                    symbol=symbol_to_use
                )

                if stored_df is not None and len(stored_df) > 0:
                    logger.info(f"Loaded {len(stored_df)} {timeframe} bars from storage for {symbol_to_use}")

                    # Cache data for future use
                    if use_cache:
                        self.data_cache[cache_key] = stored_df.copy()

                    return stored_df

            # Check if MT5 connector is initialized
            if not self.mt5.check_connection():
                logger.error(f"MT5 is not connected. Cannot retrieve historical data for {symbol_to_use} ({timeframe})")
                logger.info("Please check that Terminal 1 is running and accessible")
                logger.info("Refer to mt5_connection_guide.md for detailed instructions")
                return None

            # Log data retrieval attempt
            date_range = f"from {start_date}" + (f" to {end_date}" if end_date else "")
            bars_info = f" ({num_bars} bars)" if num_bars else ""
            logger.info(f"Retrieving {timeframe} data for {symbol_to_use} {date_range}{bars_info}")

            # Get data from MT5
            df = self.mt5.get_historical_data(
                symbol=symbol_to_use,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                num_bars=num_bars
            )

            # If MT5 data retrieval fails, try alternative methods
            if df is None or len(df) == 0:
                # Try to generate data from M1 data for any supported timeframe
                logger.warning(f"Direct {timeframe} data retrieval failed for {symbol_to_use}, attempting to generate from M1 data")
                df = self._generate_timeframe_data(timeframe, start_date, end_date, symbol=symbol_to_use)

                # If still no data, try to load from storage
                if df is None or len(df) == 0:
                    logger.warning(f"MT5 data retrieval failed for {symbol_to_use} ({timeframe}), attempting to load from storage")
                    df = self.load_historical_data_from_storage(timeframe, start_date, end_date, symbol=symbol_to_use)

                    if df is not None and len(df) > 0:
                        logger.info(f"Successfully loaded {len(df)} {timeframe} bars from storage for {symbol_to_use}")
                    else:
                        logger.error(f"Failed to get historical data for {symbol_to_use} ({timeframe})")
                        logger.info("This might be due to no data available for the specified period or symbol")
                        logger.info("Please check that the symbol exists and data is available for the specified date range")
                        return None

            # Log success
            logger.info(f"Successfully retrieved {len(df)} {timeframe} bars for {symbol_to_use}")

            # Add technical indicators to the data
            try:
                logger.info(f"Adding technical indicators to {symbol_to_use} ({timeframe}) data")
                df = add_all_indicators(df)
                logger.info(f"Successfully added technical indicators to {symbol_to_use} ({timeframe}) data")
            except Exception as e:
                logger.error(f"Failed to add technical indicators to {symbol_to_use} ({timeframe}) data: {str(e)}")
                logger.error(traceback.format_exc())

            # Validate data quality only if needed (skip for large datasets to improve performance)
            validation_results = {}
            validation_passed = True

            if not skip_validation and (len(df) < 50000 or not self.auto_save):
                # Use parallel execution for medium-sized datasets
                use_parallel = len(df) > 10000
                validation_results = self.validate_data(df, symbol_to_use, timeframe, parallel=use_parallel)

                # Check if validation passed
                validation_passed = all(result.get('status') == 'pass' for result in validation_results.values())

                # If validation failed but we still want to use the data, log a warning
                if not validation_passed:
                    failed_rules = [rule for rule, result in validation_results.items() if result.get('status') != 'pass']
                    logger.warning(f"Data validation failed for {symbol_to_use} ({timeframe}), rules: {failed_rules}")

                    # Attempt to fix common issues
                    df_before_fix = df.shape[0]
                    df = self._fix_data_issues(df, validation_results)
                    df_after_fix = df.shape[0]

                    if df_before_fix != df_after_fix:
                        logger.info(f"Fixed data issues for {symbol_to_use} ({timeframe}): removed {df_before_fix - df_after_fix} rows")
                    else:
                        logger.info(f"Attempted to fix data issues for {symbol_to_use} ({timeframe}) without removing rows")
            else:
                logger.debug(f"Skipping validation for large dataset: {len(df)} rows")
                validation_results = {'status': 'skipped'}

            # Cache data
            if use_cache:
                self.data_cache[cache_key] = df.copy()
                logger.debug(f"Cached {len(df)} {timeframe} bars for {symbol_to_use}")

            # Auto-save data if enabled (in a separate thread for large datasets)
            if self.auto_save:
                try:
                    # Prepare metadata
                    metadata = {
                        'source': 'MT5',
                        'terminal_id': self.terminal_id,
                        'retrieval_method': 'historical',
                        'retrieval_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'timeframe': timeframe,
                        'rows': len(df),
                        'columns': list(df.columns),
                        'has_volume': 'volume' in df.columns,
                        'date_range': f"{df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}",
                        'validation_passed': validation_passed,
                        'validation_summary': {rule: result.get('status') for rule, result in validation_results.items()} if validation_results and isinstance(validation_results, dict) and 'status' not in validation_results else {'status': 'skipped'}
                    }

                    # For large datasets, save in a separate thread to avoid blocking
                    if len(df) > 100000:
                        import threading
                        save_thread = threading.Thread(
                            target=self._save_data_thread,
                            args=(df.copy(), symbol_to_use, timeframe, start_date, end_date, metadata)
                        )
                        save_thread.daemon = True
                        save_thread.start()
                        logger.info(f"Saving large dataset for {symbol_to_use} ({timeframe}) in background thread")
                    else:
                        self.data_manager.save_historical_data(
                            df=df,
                            symbol=symbol_to_use,
                            timeframe=timeframe,
                            start_date=start_date,
                            end_date=end_date,
                            terminal_id=self.terminal_id,
                            metadata=metadata
                        )
                        logger.info(f"Auto-saved historical data for {symbol_to_use} ({timeframe}) from terminal {self.terminal_id}")
                except Exception as e:
                    logger.error(f"Failed to auto-save historical data: {str(e)}")
                    logger.debug(traceback.format_exc())

            return df

        except Exception as e:
            logger.error(f"Error retrieving historical data for {self.symbol} ({timeframe}): {str(e)}")
            logger.error(traceback.format_exc())
            return None

    def _save_data_thread(self, df, symbol, timeframe, start_date, end_date, metadata):
        """
        Save data in a separate thread to avoid blocking.

        Args:
            df: DataFrame to save
            symbol: Symbol
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
            metadata: Metadata
        """
        try:
            self.data_manager.save_historical_data(
                df=df,
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                terminal_id=self.terminal_id,
                metadata=metadata
            )
            logger.info(f"Background save completed for {symbol} ({timeframe})")
        except Exception as e:
            logger.error(f"Error in background save thread: {str(e)}")
            logger.error(traceback.format_exc())

    def get_latest_data(
        self,
        timeframe: str,
        num_bars: int = 1000,
        symbol: str = None
    ) -> Optional[pd.DataFrame]:
        """
        Get latest data for the symbol.

        Args:
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            num_bars: Number of bars to retrieve
            symbol: Symbol to get data for (defaults to self.symbol)

        Returns:
            pd.DataFrame: Latest data or None if failed
        """
        try:
            # Use provided symbol or default to self.symbol
            symbol_to_use = symbol if symbol is not None else self.symbol

            # Check if MT5 connector is initialized
            if not self.mt5.check_connection():
                logger.error(f"MT5 is not connected. Cannot retrieve latest data for {symbol_to_use} ({timeframe})")
                logger.info("Please check that Terminal 1 is running and accessible")
                return None

            # Get data from MT5
            df = self.mt5.get_historical_data(
                symbol=symbol_to_use,
                timeframe=timeframe,
                start_date=None,
                end_date=None,
                num_bars=num_bars
            )

            if df is None or len(df) == 0:
                logger.error(f"Failed to get latest data for {symbol_to_use} ({timeframe})")
                return None

            # Auto-save data if enabled
            if self.auto_save:
                try:
                    # Get current date for start_date and end_date
                    current_date = datetime.now().strftime("%Y-%m-%d")

                    # Prepare metadata
                    metadata = {
                        'source': 'MT5',
                        'terminal_id': self.terminal_id,
                        'retrieval_method': 'latest',
                        'retrieval_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'timeframe': timeframe,
                        'rows': len(df),
                        'columns': list(df.columns),
                        'has_volume': 'volume' in df.columns,
                        'date_range': f"{df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}"
                    }

                    self.data_manager.save_historical_data(
                        df=df,
                        symbol=symbol_to_use,
                        timeframe=timeframe,
                        start_date=current_date,
                        end_date=current_date,
                        terminal_id=self.terminal_id,
                        metadata=metadata
                    )
                    logger.info(f"Auto-saved latest data for {symbol_to_use} ({timeframe}) from terminal {self.terminal_id}")
                except Exception as e:
                    logger.error(f"Failed to auto-save latest data: {str(e)}")
                    logger.debug(traceback.format_exc())

            return df

        except Exception as e:
            logger.error(f"Error retrieving latest data for {symbol_to_use} ({timeframe}): {str(e)}")
            logger.error(traceback.format_exc())
            return None

    def get_multi_timeframe_data(
        self,
        start_date: str,
        end_date: str = None,
        use_cache: bool = True,
        symbol: str = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Get data for multiple timeframes.

        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format (optional)
            use_cache: Whether to use cached data if available
            symbol: Symbol to get data for (defaults to self.symbol)

        Returns:
            Dict[str, pd.DataFrame]: Dictionary of dataframes for each timeframe
        """
        result = {}
        symbol_to_use = symbol if symbol is not None else self.symbol

        # Get timeframes for this symbol
        if isinstance(self.timeframes, dict):
            # If timeframes is a dict, use keys
            timeframes_to_use = list(self.timeframes.keys())
        else:
            # Otherwise use the list directly
            timeframes_to_use = self.timeframes

        for tf_key in timeframes_to_use:
            df = self.get_historical_data(
                timeframe=tf_key,
                start_date=start_date,
                end_date=end_date,
                use_cache=use_cache,
                symbol=symbol_to_use
            )

            if df is not None:
                result[tf_key] = df

        return result

    def get_latest_multi_timeframe_data(
        self,
        num_bars: int = 1000,
        symbol: str = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Get latest data for multiple timeframes.

        Args:
            num_bars: Number of bars to retrieve for each timeframe
            symbol: Symbol to get data for (defaults to self.symbol)

        Returns:
            Dict[str, pd.DataFrame]: Dictionary of dataframes for each timeframe
        """
        result = {}
        symbol_to_use = symbol if symbol is not None else self.symbol

        # Get timeframes for this symbol
        if isinstance(self.timeframes, dict):
            # If timeframes is a dict, use keys
            timeframes_to_use = list(self.timeframes.keys())
        else:
            # Otherwise use the list directly
            timeframes_to_use = self.timeframes

        for tf_key in timeframes_to_use:
            df = self.get_latest_data(
                timeframe=tf_key,
                num_bars=num_bars,
                symbol=symbol_to_use
            )

            if df is not None:
                result[tf_key] = df

        return result

    def clear_cache(self):
        """Clear the data cache."""
        self.data_cache = {}
        logger.info("Data cache cleared")

    def load_historical_data_from_storage(self,
                                   timeframe: str,
                                   start_date: Optional[str] = None,
                                   end_date: Optional[str] = None,
                                   version: Optional[str] = None,
                                   symbol: Optional[str] = None,
                                   terminal_id: Optional[int] = None) -> Optional[pd.DataFrame]:
        """
        Load historical data from storage.

        Args:
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            start_date: Start date in 'YYYY-MM-DD' format (optional)
            end_date: End date in 'YYYY-MM-DD' format (optional)
            version: Specific version to load (optional)
            symbol: Symbol to load data for (defaults to self.symbol)
            terminal_id: Terminal ID to load data for (defaults to self.terminal_id)

        Returns:
            pd.DataFrame: Historical data or None if not found
        """
        try:
            symbol_to_use = symbol if symbol is not None else self.symbol
            terminal_id_to_use = terminal_id if terminal_id is not None else self.terminal_id
            df = self.data_manager.load_historical_data(
                symbol=symbol_to_use,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                version=version,
                terminal_id=terminal_id_to_use
            )
            if df is not None:
                logger.info(f"Loaded historical data from storage for {symbol_to_use} ({timeframe})")
                return df
            else:
                logger.warning(f"No historical data found in storage for {symbol_to_use} ({timeframe})")
                return None
        except Exception as e:
            logger.error(f"Failed to load historical data from storage: {e}")
            return None

    def load_features_from_storage(self,
                               timeframe: str,
                               version: Optional[str] = None,
                               symbol: Optional[str] = None,
                               terminal_id: Optional[int] = None) -> Optional[pd.DataFrame]:
        """
        Load feature data from storage.

        Args:
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            version: Specific version to load (optional)
            symbol: Symbol to load data for (defaults to self.symbol)
            terminal_id: Terminal ID to load data for (defaults to self.terminal_id)

        Returns:
            pd.DataFrame: Feature data or None if not found
        """
        try:
            symbol_to_use = symbol if symbol is not None else self.symbol
            terminal_id_to_use = terminal_id if terminal_id is not None else self.terminal_id
            df = self.data_manager.load_features(
                symbol=symbol_to_use,
                timeframe=timeframe,
                version=version,
                terminal_id=terminal_id_to_use
            )
            if df is not None:
                logger.info(f"Loaded feature data from storage for {symbol_to_use} ({timeframe})")
                return df
            else:
                logger.warning(f"No feature data found in storage for {symbol_to_use} ({timeframe})")
                return None
        except Exception as e:
            logger.error(f"Failed to load feature data from storage: {e}")
            return None

    def save_features_to_storage(self, df: pd.DataFrame, timeframe: str, symbol: Optional[str] = None, terminal_id: Optional[int] = None) -> bool:
        """
        Save feature data to storage.

        Args:
            df: DataFrame with features
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            symbol: Symbol to save data for (defaults to self.symbol)
            terminal_id: Terminal ID to save data for (defaults to self.terminal_id)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            symbol_to_use = symbol if symbol is not None else self.symbol
            terminal_id_to_use = terminal_id if terminal_id is not None else self.terminal_id

            # Prepare metadata
            metadata = {
                'source': 'Feature Engineering',
                'terminal_id': terminal_id_to_use,
                'timeframe': timeframe,
                'creation_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'rows': len(df),
                'columns': list(df.columns),
                'feature_count': len([col for col in df.columns if col not in ['open', 'high', 'low', 'close', 'volume']])
            }

            self.data_manager.save_features(
                df=df,
                symbol=symbol_to_use,
                timeframe=timeframe,
                terminal_id=terminal_id_to_use,
                metadata=metadata
            )
            logger.info(f"Saved feature data to storage for {symbol_to_use} ({timeframe})")
            return True
        except Exception as e:
            logger.error(f"Failed to save feature data to storage: {e}")
            return False

    # Legacy methods for backward compatibility
    def save_data_to_csv(self, df: pd.DataFrame, filename: str):
        """
        Save data to CSV file (legacy method).

        Args:
            df: DataFrame to save
            filename: Filename to save to
        """
        logger.warning("save_data_to_csv is deprecated, use data_manager methods instead")
        df.to_csv(f"data/{filename}", index=True)
        logger.info(f"Data saved to data/{filename}")

    def load_data_from_csv(self, filename: str) -> Optional[pd.DataFrame]:
        """
        Load data from CSV file (legacy method).

        Args:
            filename: Filename to load from

        Returns:
            pd.DataFrame: Loaded data or None if failed
        """
        logger.warning("load_data_from_csv is deprecated, use data_manager methods instead")
        try:
            df = pd.read_csv(f"data/{filename}", index_col=0, parse_dates=True)
            logger.info(f"Data loaded from data/{filename}")
            return df
        except Exception as e:
            logger.error(f"Failed to load data from data/{filename}: {str(e)}")
            return None



    def collect_data_from_all_terminals(
        self,
        symbol: str,
        timeframe: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime] = None,
        terminal_ids: List[int] = None
    ) -> Optional[pd.DataFrame]:
        """
        Collect data from all available terminals and merge it.
        Uses the enhanced initialize_all_terminals function to ensure all terminals are properly initialized.

        Args:
            symbol: Symbol to collect data for
            timeframe: Timeframe to collect data for
            start_date: Start date for data collection
            end_date: End date for data collection (defaults to current date)
            terminal_ids: List of terminal IDs to collect data from (defaults to all terminals)

        Returns:
            pd.DataFrame: Merged data from all terminals or None if failed
        """
        # Convert dates to datetime objects if they are strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Calculate date range in days
        date_range_days = (end_date - start_date).days

        # Initialize all terminals first to ensure they're all available
        logger.info("Initializing all terminals before data collection")
        terminal_status = initialize_all_terminals(max_retries=3, retry_delay=5)

        # Get list of initialized terminals
        initialized_terminals = [tid for tid, status in terminal_status.items() if status['initialized']]

        # Use specified terminals if provided, otherwise use all initialized terminals
        if terminal_ids is None:
            terminal_ids = initialized_terminals
        else:
            # Filter to only include terminals that are initialized
            terminal_ids = [tid for tid in terminal_ids if tid in initialized_terminals]

        logger.info(f"Collecting data from {len(terminal_ids)} terminals for {symbol} ({timeframe})")
        logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} ({date_range_days} days)")

        # Collect data from each terminal
        all_dfs = []

        # Try each terminal
        for terminal_id in terminal_ids:
            try:
                logger.info(f"Collecting data from Terminal {terminal_id} for {symbol} ({timeframe})")

                # Initialize MT5 with the terminal using the standardized initializer
                # This ensures consistent initialization across the codebase
                # and maintains Algo Trading functionality in all terminals
                if not initialize_mt5_once(terminal_id):
                    logger.error(f"Failed to initialize MT5 Terminal {terminal_id}")
                    logger.info("Please check that the terminal is running and accessible")
                    logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
                    continue

                # Convert timeframe string to MT5 timeframe constant
                mt5_timeframe = self._get_mt5_timeframe(timeframe)
                if mt5_timeframe is None:
                    logger.error(f"Invalid timeframe: {timeframe}")
                    continue

                # Convert dates to timestamps
                from_timestamp = int(start_date.timestamp())
                to_timestamp = int(end_date.timestamp())

                # Determine the maximum number of days to request at once
                # MT5 has limitations on how much data can be retrieved in a single call
                max_days_per_request = 300  # Default to 300 days per request

                # For higher timeframes, we can request more days at once
                if timeframe in ['H1', 'H4', 'D1']:
                    max_days_per_request = 500

                # For lower timeframes, we need to request fewer days
                if timeframe in ['M1', 'M5']:
                    max_days_per_request = 100

                # Collect data in chunks
                chunks = []
                current_date = start_date

                while current_date < end_date:
                    # Calculate end date for this chunk
                    chunk_end_date = current_date + timedelta(days=max_days_per_request)
                    if chunk_end_date > end_date:
                        chunk_end_date = end_date

                    # Convert dates to timestamps
                    from_timestamp = int(current_date.timestamp())
                    to_timestamp = int(chunk_end_date.timestamp())

                    # Try to get data with different methods
                    chunk_rates = None

                    # Try different approaches with retries
                    for attempt in range(3):
                        try:
                            # First try with copy_rates_range
                            if attempt == 0:
                                logger.info(f"Terminal {terminal_id}: Attempt {attempt+1}: Using copy_rates_range")
                                chunk_rates = mt5.copy_rates_range(symbol, mt5_timeframe, from_timestamp, to_timestamp)
                            # Then try with copy_rates_from
                            elif attempt == 1:
                                logger.info(f"Terminal {terminal_id}: Attempt {attempt+1}: Using copy_rates_from")

                                # Estimate number of bars based on timeframe
                                if timeframe == 'M1':
                                    bars_per_day = 1440  # 24 hours * 60 minutes = 1440 bars per day
                                elif timeframe == 'M5':
                                    bars_per_day = 288  # 24 hours * 12 bars per hour = 288 bars per day
                                elif timeframe == 'M15':
                                    bars_per_day = 96  # 24 hours * 4 bars per hour = 96 bars per day
                                elif timeframe == 'M30':
                                    bars_per_day = 48  # 24 hours * 2 bars per hour = 48 bars per day
                                elif timeframe == 'H1':
                                    bars_per_day = 24  # 24 hours / 1 hour = 24 bars per day
                                elif timeframe == 'H4':
                                    bars_per_day = 6  # 24 hours / 4 hours = 6 bars per day
                                else:
                                    bars_per_day = 10  # Default for larger timeframes

                                # Calculate days in this chunk
                                chunk_days = (chunk_end_date - current_date).days
                                estimated_bars = int(chunk_days * bars_per_day * 1.1)  # Add 10% margin

                                chunk_rates = mt5.copy_rates_from(symbol, mt5_timeframe, from_timestamp, estimated_bars)
                            # Finally try with a smaller chunk
                            else:
                                logger.info(f"Terminal {terminal_id}: Attempt {attempt+1}: Using smaller chunk size")
                                # Try with half the chunk size
                                mid_date = current_date + timedelta(days=max_days_per_request//2)
                                if mid_date > end_date:
                                    mid_date = end_date
                                mid_timestamp = int(mid_date.timestamp())
                                chunk_rates = mt5.copy_rates_range(symbol, mt5_timeframe, from_timestamp, mid_timestamp)

                            # If we got data, break the retry loop
                            if chunk_rates is not None and len(chunk_rates) > 0:
                                break

                        except Exception as e:
                            logger.error(f"Error in attempt {attempt+1} for Terminal {terminal_id}: {str(e)}")
                            time.sleep(1)  # Wait before retrying

                    # If we got data, convert it to DataFrame and add to chunks
                    if chunk_rates is not None and len(chunk_rates) > 0:
                        # Convert to DataFrame
                        chunk_df = pd.DataFrame(chunk_rates)

                        # Convert time column to datetime
                        chunk_df['time'] = pd.to_datetime(chunk_df['time'], unit='s')

                        logger.info(f"Terminal {terminal_id}: Got {len(chunk_df)} bars from {chunk_df['time'].min()} to {chunk_df['time'].max()}")

                        # Add technical indicators to the data
                        try:
                            logger.info(f"Adding technical indicators to chunk data from Terminal {terminal_id}")
                            chunk_df = add_all_indicators(chunk_df)
                            logger.info(f"Successfully added technical indicators to chunk data from Terminal {terminal_id}")
                        except Exception as e:
                            logger.error(f"Failed to add technical indicators to chunk data from Terminal {terminal_id}: {str(e)}")
                            logger.error(traceback.format_exc())

                        # Add to chunks
                        chunks.append(chunk_df)
                    else:
                        logger.warning(f"Terminal {terminal_id}: Failed to get data for chunk {current_date} to {chunk_end_date}")

                    # Move to next chunk
                    current_date = chunk_end_date

                    # Add a small delay between chunks
                    time.sleep(0.5)

                # Combine all chunks
                if chunks:
                    terminal_df = pd.concat(chunks)

                    # Remove duplicates
                    terminal_df = terminal_df.drop_duplicates(subset=['time'])

                    # Sort by time
                    terminal_df = terminal_df.sort_values('time')

                    logger.info(f"Terminal {terminal_id}: Collected {len(terminal_df)} bars from {terminal_df['time'].min()} to {terminal_df['time'].max()}")

                    # Add to all_dfs
                    all_dfs.append(terminal_df)
                else:
                    logger.warning(f"Terminal {terminal_id}: No data collected")

            except Exception as e:
                logger.error(f"Error collecting data from Terminal {terminal_id}: {str(e)}")
                logger.error(traceback.format_exc())

            # Add a small delay between terminals
            time.sleep(1)

        # Merge data from all terminals
        if not all_dfs:
            logger.error(f"Failed to collect data from any terminal for {symbol} ({timeframe})")
            return None

        # Merge all dataframes
        merged_df = pd.concat(all_dfs)

        # Remove duplicates
        merged_df = merged_df.drop_duplicates(subset=['time'])

        # Sort by time
        merged_df = merged_df.sort_values('time')

        logger.info(f"Merged data from all terminals: {len(merged_df)} bars from {merged_df['time'].min()} to {merged_df['time'].max()}")

        return merged_df

    def download_historical_data(
        self,
        symbol: str = None,
        timeframes: List[str] = None,
        start_date: Union[str, datetime] = None,
        end_date: Union[str, datetime] = None,
        terminal_ids: List[int] = None,
        save: bool = True,
        years_to_collect: int = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Download historical data for multiple timeframes and save to standardized paths.

        Required path structure:
        data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet

        Args:
            symbol: Symbol to download data for (defaults to self.symbol)
            timeframes: List of timeframes to download data for (defaults to self.timeframes)
            start_date: Start date for data collection (defaults to years_to_collect years ago)
            end_date: End date for data collection (defaults to current date)
            terminal_ids: List of terminal IDs to collect data from (defaults to all terminals)
            save: Whether to save the data to storage (defaults to True)
            years_to_collect: Number of years of data to collect (defaults to 5)

        Returns:
            Dict[str, pd.DataFrame]: Dictionary of dataframes for each timeframe
        """
        # Use provided symbol or default to self.symbol
        symbol_to_use = symbol if symbol is not None else self.symbol

        # Use provided timeframes or default to self.timeframes
        if timeframes is None:
            if isinstance(self.timeframes, dict):
                # If timeframes is a dict, use keys
                timeframes_to_use = list(self.timeframes.keys())
            else:
                # Otherwise use the list directly
                timeframes_to_use = self.timeframes
        else:
            timeframes_to_use = timeframes

        # Get years_to_collect from config if not provided
        if years_to_collect is None:
            from config.trading_config import TRADING_CONFIG
            years_to_collect = TRADING_CONFIG.get('data_collection', {}).get('years_to_collect', 5)

        # Use default start_date if not provided (years_to_collect years ago)
        if start_date is None:
            start_date = datetime.now() - timedelta(days=years_to_collect*365)
        elif isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)

        # Use default end_date if not provided (current date)
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Format dates for logging and file paths
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        start_date_fmt = start_date.strftime('%Y%m%d')
        end_date_fmt = end_date.strftime('%Y%m%d')

        logger.info(f"Downloading historical data for {symbol_to_use}")
        logger.info(f"Timeframes: {timeframes_to_use}")
        logger.info(f"Date range: {start_date_str} to {end_date_str} ({years_to_collect} years)")

        if terminal_ids:
            logger.info(f"Using specific terminals: {terminal_ids}")
        else:
            logger.info("Using all available terminals")

        # Collect data for each timeframe
        results = {}

        for tf in timeframes_to_use:
            try:
                logger.info(f"\n=== Downloading {tf} data for {symbol_to_use} ===")

                # Collect data from all terminals
                df = self.collect_data_from_all_terminals(
                    symbol=symbol_to_use,
                    timeframe=tf,
                    start_date=start_date,
                    end_date=end_date,
                    terminal_ids=terminal_ids
                )

                if df is not None and len(df) > 0:
                    logger.info(f"Successfully collected {len(df)} {tf} bars for {symbol_to_use}")
                    # Check if index is datetime type before calling strftime
                    if hasattr(df.index, 'dtype') and pd.api.types.is_datetime64_any_dtype(df.index):
                        logger.info(f"Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
                    elif 'time' in df.columns and pd.api.types.is_datetime64_any_dtype(df['time']):
                        logger.info(f"Date range: {df['time'].min().strftime('%Y-%m-%d')} to {df['time'].max().strftime('%Y-%m-%d')}")
                    else:
                        logger.info(f"Date range information not available (index is not datetime type)")

                    # Save data if requested
                    if save:
                        # Save data for each terminal separately
                        if terminal_ids:
                            for terminal_id in terminal_ids:
                                # Prepare metadata
                                metadata = {
                                    'source': 'MT5',
                                    'terminal_id': terminal_id,
                                    'retrieval_method': 'historical',
                                    'retrieval_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                    'timeframe': tf,
                                    'rows': len(df),
                                    'columns': list(df.columns),
                                    'has_volume': 'volume' in df.columns or 'tick_volume' in df.columns,
                                    'date_range': f"{start_date_str} to {end_date_str}",
                                    'years_collected': years_to_collect
                                }

                                # Save using standardized path structure
                                from utils.path_utils import get_historical_data_path

                                # Generate standardized path
                                filepath = get_historical_data_path(
                                    symbol=symbol_to_use,
                                    timeframe=tf,
                                    terminal_id=terminal_id,
                                    start_date=start_date_fmt,
                                    end_date=end_date_fmt,
                                    create_dirs=True
                                )

                                # Save data using data manager
                                self.data_manager.save_data(
                                    df=df,
                                    path=filepath,
                                    metadata=metadata
                                )

                                logger.info(f"Saved data to: {filepath}")

                                if filepath:
                                    logger.info(f"Saved {tf} data for Terminal {terminal_id} to: {filepath}")
                                else:
                                    logger.error(f"Failed to save {tf} data for Terminal {terminal_id}")

                            # Also save merged data when terminal_ids are provided
                            logger.info(f"Saving merged data for {symbol_to_use} ({tf}) from all terminals")
                            metadata = {
                                'source': 'MT5',
                                'retrieval_method': 'historical_merged',
                                'retrieval_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'timeframe': tf,
                                'rows': len(df),
                                'columns': list(df.columns),
                                'has_volume': 'volume' in df.columns or 'tick_volume' in df.columns,
                                'date_range': f"{start_date_str} to {end_date_str}",
                                'years_collected': years_to_collect,
                                'merged_from_terminals': terminal_ids
                            }

                            filepath = self.data_manager.save_historical_data(
                                df=df,
                                symbol=symbol_to_use,
                                timeframe=tf,
                                start_date=start_date_fmt,
                                end_date=end_date_fmt,
                                metadata=metadata
                            )

                            if filepath:
                                logger.info(f"Saved merged {tf} data to: {filepath}")
                            else:
                                logger.error(f"Failed to save merged {tf} data")
                        else:
                            # Save merged data
                            metadata = {
                                'source': 'MT5',
                                'retrieval_method': 'historical_merged',
                                'retrieval_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'timeframe': tf,
                                'rows': len(df),
                                'columns': list(df.columns),
                                'has_volume': 'volume' in df.columns or 'tick_volume' in df.columns,
                                'date_range': f"{start_date_str} to {end_date_str}",
                                'years_collected': years_to_collect
                            }

                            filepath = self.data_manager.save_historical_data(
                                df=df,
                                symbol=symbol_to_use,
                                timeframe=tf,
                                start_date=start_date_fmt,
                                end_date=end_date_fmt,
                                metadata=metadata
                            )

                            if filepath:
                                logger.info(f"Saved merged {tf} data to: {filepath}")
                            else:
                                logger.error(f"Failed to save merged {tf} data")

                    results[tf] = df
                else:
                    logger.error(f"Failed to collect {tf} data for {symbol_to_use}")

            except Exception as e:
                logger.error(f"Error downloading {tf} data for {symbol_to_use}: {str(e)}")
                logger.error(traceback.format_exc())

        # Print summary
        logger.info("\n=== Historical Data Download Summary ===")
        for tf, df in results.items():
            if df is not None and len(df) > 0:
                # Check if index is datetime type before calling strftime
                if hasattr(df.index, 'dtype') and pd.api.types.is_datetime64_any_dtype(df.index):
                    logger.info(f"{tf}: {len(df)} bars from {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
                elif 'time' in df.columns and pd.api.types.is_datetime64_any_dtype(df['time']):
                    logger.info(f"{tf}: {len(df)} bars from {df['time'].min().strftime('%Y-%m-%d')} to {df['time'].max().strftime('%Y-%m-%d')}")
                else:
                    logger.info(f"{tf}: {len(df)} bars (date range information not available)")
            else:
                logger.info(f"{tf}: Failed to collect data")

        # Return the collected data
        return results

    def _get_mt5_timeframe(self, timeframe: str) -> int:
        """
        Convert timeframe string to MT5 timeframe constant.

        Args:
            timeframe: Timeframe string (e.g., 'M5', 'M15', 'M30')

        Returns:
            int: MT5 timeframe constant or None if invalid
        """
        timeframe_map = {
            'M1': mt5.TIMEFRAME_M1,
            'M5': mt5.TIMEFRAME_M5,
            'M15': mt5.TIMEFRAME_M15,
            'M30': mt5.TIMEFRAME_M30,
            'H1': mt5.TIMEFRAME_H1,
            'H4': mt5.TIMEFRAME_H4,
            'D1': mt5.TIMEFRAME_D1,
            'W1': mt5.TIMEFRAME_W1,
            'MN1': mt5.TIMEFRAME_MN1
        }

        return timeframe_map.get(timeframe)

    def validate_data(self, df: pd.DataFrame, symbol: str, timeframe: str, parallel: bool = False) -> Dict[str, Dict]:
        """
        Validate data quality using the data validator.

        Args:
            df: DataFrame to validate
            symbol: Symbol for the data
            timeframe: Timeframe for the data
            parallel: Whether to run validation in parallel

        Returns:
            Dict[str, Dict]: Validation results
        """
        if df is None or df.empty:
            logger.warning(f"Cannot validate empty DataFrame for {symbol} ({timeframe})")
            return {"status": "error", "message": "Empty DataFrame"}

        logger.info(f"Validating data quality for {symbol} ({timeframe})")

        # Make a copy to avoid modifying the original DataFrame
        df_copy = df.copy()

        # Ensure 'time' column is present (might be in the index)
        if 'time' not in df_copy.columns and isinstance(df_copy.index, pd.DatetimeIndex):
            df_copy['time'] = df_copy.index

        # Determine max_workers based on CPU count and dataset size
        max_workers = None
        if parallel and len(df) > 50000:  # For very large datasets
            import multiprocessing
            max_workers = max(1, multiprocessing.cpu_count() - 1)  # Leave one CPU free
            logger.debug(f"Using {max_workers} workers for parallel validation")

        # Run validation with performance optimizations
        start_time = datetime.now()
        validation_results = self.data_validator.validate_data(
            df_copy,
            parallel=parallel,
            max_workers=max_workers
        )
        end_time = datetime.now()
        validation_time = (end_time - start_time).total_seconds()

        # Log validation results with timing information
        if all(result.get('status') == 'pass' for result in validation_results.values()):
            logger.info(f"Data validation passed for {symbol} ({timeframe}) in {validation_time:.2f} seconds")
        else:
            failed_rules = [rule for rule, result in validation_results.items() if result.get('status') != 'pass']
            logger.warning(f"Data validation failed for {symbol} ({timeframe}) in {validation_time:.2f} seconds. Failed rules: {failed_rules}")

            # Log detailed failure information for debugging
            for rule, result in validation_results.items():
                if result.get('status') != 'pass':
                    logger.debug(f"Rule '{rule}' failed: {result.get('message')}")
                    if 'details' in result:
                        logger.debug(f"Details: {result['details']}")

        return validation_results

    def _fix_data_issues(self, df: pd.DataFrame, validation_results: Dict[str, Dict]) -> pd.DataFrame:
        """
        Fix common data issues based on validation results.

        Args:
            df: DataFrame to fix
            validation_results: Validation results from validate_data

        Returns:
            pd.DataFrame: Fixed DataFrame
        """
        if df is None or df.empty:
            logger.warning("Cannot fix issues in empty DataFrame")
            return df

        # Make a copy to avoid modifying the original DataFrame
        df_fixed = df.copy()

        # Fix missing values
        if 'missing_values_check' in validation_results and validation_results['missing_values_check'].get('status') != 'pass':
            logger.info("Fixing missing values")

            # For price columns, use forward fill then backward fill
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df_fixed.columns:
                    df_fixed[col] = df_fixed[col].fillna(method='ffill').fillna(method='bfill')

            # For volume columns, fill with zeros or median
            volume_columns = ['tick_volume', 'real_volume', 'volume']
            for col in volume_columns:
                if col in df_fixed.columns:
                    # If more than 50% of values are missing, fill with zeros
                    # Otherwise, fill with median
                    missing_ratio = df_fixed[col].isna().mean()
                    if missing_ratio > 0.5:
                        df_fixed[col] = df_fixed[col].fillna(0)
                    else:
                        df_fixed[col] = df_fixed[col].fillna(df_fixed[col].median())

        # Fix price integrity issues
        if 'price_integrity_check' in validation_results and validation_results['price_integrity_check'].get('status') != 'pass':
            logger.info("Fixing price integrity issues")

            # Ensure high >= low
            if 'high' in df_fixed.columns and 'low' in df_fixed.columns:
                # Find rows where high < low
                invalid_rows = df_fixed['high'] < df_fixed['low']
                if invalid_rows.any():
                    # Swap high and low values
                    temp = df_fixed.loc[invalid_rows, 'high'].copy()
                    df_fixed.loc[invalid_rows, 'high'] = df_fixed.loc[invalid_rows, 'low']
                    df_fixed.loc[invalid_rows, 'low'] = temp
                    logger.info(f"Fixed {invalid_rows.sum()} rows with high < low")

            # Ensure high >= open and high >= close
            if 'high' in df_fixed.columns:
                if 'open' in df_fixed.columns:
                    invalid_rows = df_fixed['high'] < df_fixed['open']
                    if invalid_rows.any():
                        df_fixed.loc[invalid_rows, 'high'] = df_fixed.loc[invalid_rows, 'open']
                        logger.info(f"Fixed {invalid_rows.sum()} rows with high < open")

                if 'close' in df_fixed.columns:
                    invalid_rows = df_fixed['high'] < df_fixed['close']
                    if invalid_rows.any():
                        df_fixed.loc[invalid_rows, 'high'] = df_fixed.loc[invalid_rows, 'close']
                        logger.info(f"Fixed {invalid_rows.sum()} rows with high < close")

            # Ensure low <= open and low <= close
            if 'low' in df_fixed.columns:
                if 'open' in df_fixed.columns:
                    invalid_rows = df_fixed['low'] > df_fixed['open']
                    if invalid_rows.any():
                        df_fixed.loc[invalid_rows, 'low'] = df_fixed.loc[invalid_rows, 'open']
                        logger.info(f"Fixed {invalid_rows.sum()} rows with low > open")

                if 'close' in df_fixed.columns:
                    invalid_rows = df_fixed['low'] > df_fixed['close']
                    if invalid_rows.any():
                        df_fixed.loc[invalid_rows, 'low'] = df_fixed.loc[invalid_rows, 'close']
                        logger.info(f"Fixed {invalid_rows.sum()} rows with low > close")

        # Fix outliers
        if 'price_outliers_check' in validation_results and validation_results['price_outliers_check'].get('status') != 'pass':
            logger.info("Fixing outliers")

            # Get outlier details
            outlier_details = validation_results['price_outliers_check'].get('details', {})
            outlier_indices = outlier_details.get('outlier_indices', [])

            if outlier_indices:
                # Replace outliers with rolling median
                window_size = 5  # Use 5-bar window for median calculation

                for col in ['open', 'high', 'low', 'close']:
                    if col in df_fixed.columns:
                        # Calculate rolling median
                        rolling_median = df_fixed[col].rolling(window=window_size, center=True, min_periods=1).median()

                        # Replace outliers with rolling median
                        df_fixed.loc[outlier_indices, col] = rolling_median.loc[outlier_indices]

                logger.info(f"Fixed {len(outlier_indices)} outliers")

        # Fix timestamp issues
        if 'timestamp_check' in validation_results and validation_results['timestamp_check'].get('status') != 'pass':
            logger.info("Fixing timestamp issues")

            # Sort by timestamp
            if isinstance(df_fixed.index, pd.DatetimeIndex):
                df_fixed = df_fixed.sort_index()
                logger.info("Sorted DataFrame by timestamp index")
            elif 'time' in df_fixed.columns:
                df_fixed = df_fixed.sort_values('time')
                logger.info("Sorted DataFrame by time column")

        # Remove duplicate timestamps
        if isinstance(df_fixed.index, pd.DatetimeIndex):
            duplicates = df_fixed.index.duplicated()
            if duplicates.any():
                df_fixed = df_fixed[~duplicates]
                logger.info(f"Removed {duplicates.sum()} duplicate timestamps")
        elif 'time' in df_fixed.columns:
            duplicates = df_fixed['time'].duplicated()
            if duplicates.any():
                df_fixed = df_fixed[~duplicates]
                logger.info(f"Removed {duplicates.sum()} duplicate timestamps")

        return df_fixed
