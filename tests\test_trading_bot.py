"""
Test script for the trading bot.
"""
import sys
import logging
import os
import time
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append('.')

# Import the trading bot components
from crypto_trading_bot.core.terminal_manager import TerminalManager
from model.lstm_model import TradingLSTM
from trading.mt5_connector import MT5Connector
from data.data_collector import DataCollector

def test_terminal_manager():
    """Test the terminal manager."""
    logger.info("Testing terminal manager...")

    # Create a terminal manager
    manager = TerminalManager()

    # Initialize terminals
    if manager.initialize_terminals():
        logger.info("Successfully initialized terminals")

        # Get active terminals
        active_terminals = manager.get_active_terminals()
        logger.info(f"Active terminals: {active_terminals}")

        # Check if Algo Trading is enabled
        if manager.check_algo_trading_enabled():
            logger.info("Algo Trading is ENABLED")
        else:
            logger.warning("Algo Trading is DISABLED")
            logger.info("Please enable Algo Trading in the MT5 terminal")

        # Shutdown terminals
        manager.shutdown_terminals()
        logger.info("Successfully shutdown terminals")
        return True
    else:
        logger.error("Failed to initialize terminals")
        return False

def test_data_collection():
    """Test data collection."""
    logger.info("Testing data collection...")

    # Create a connector
    connector = MT5Connector()

    # Connect to Terminal 3 (IC Markets)
    if connector.connect_terminal(3):
        logger.info("Successfully connected to Terminal 3")

        # Create a data collector
        collector = DataCollector(connector)

        # Get historical data
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')

        logger.info(f"Getting historical data from {start_date} to {end_date}...")
        df = collector.get_historical_data(
            timeframe='M5',
            start_date=start_date,
            end_date=end_date,
            use_cache=False
        )

        if df is not None:
            logger.info(f"Retrieved {len(df)} M5 bars")
            logger.info(df.head())

            # Disconnect from the terminal
            connector.disconnect_terminal()
            logger.info("Successfully disconnected from Terminal 3")
            return True
        else:
            logger.error("Failed to get historical data")

            # Disconnect from the terminal
            connector.disconnect_terminal()
            logger.info("Successfully disconnected from Terminal 3")
            return False
    else:
        logger.error("Failed to connect to Terminal 3")
        return False

def test_model_loading():
    """Test loading the LSTM model."""
    logger.info("Testing LSTM model loading...")

    try:
        # Import torch
        import torch

        # Add TradingLSTM to safe globals for PyTorch 2.6+ security model
        torch.serialization.add_safe_globals([TradingLSTM])

        # Check if the model file exists
        model_path = 'model/saved_models/BTCUSD.a_M5.pth'
        if not os.path.exists(model_path):
            logger.error(f"Model file not found: {model_path}")
            return False

        # Load the model
        logger.info(f"Loading model from {model_path}...")
        model_data = torch.load(model_path)

        logger.info("Model loaded successfully")
        logger.info(f"Model type: {type(model_data)}")

        # Check if the model is a dictionary
        if isinstance(model_data, dict):
            logger.info("Model is a dictionary")

            # Check if the model contains the model state dict
            if 'model_state_dict' in model_data:
                logger.info("Model contains model_state_dict")

                # Create a new model
                input_size = model_data.get('input_size', 32)
                hidden_size = model_data.get('hidden_size', 64)
                num_layers = model_data.get('num_layers', 2)
                output_size = model_data.get('output_size', 1)

                logger.info(f"Creating model with input_size={input_size}, hidden_size={hidden_size}, num_layers={num_layers}, output_size={output_size}")

                model = TradingLSTM(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    output_size=output_size
                )

                # Load the model state dict
                model.load_state_dict(model_data['model_state_dict'])

                logger.info("Model state dict loaded successfully")
                return True
            else:
                logger.error("Model does not contain model_state_dict")
                return False
        else:
            logger.error(f"Model is not a dictionary: {type(model_data)}")
            return False
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main function."""
    logger.info("Running trading bot tests...")

    # Test the terminal manager
    if not test_terminal_manager():
        logger.error("Terminal manager test failed")
        return 1

    # Test data collection
    if not test_data_collection():
        logger.error("Data collection test failed")
        return 1

    # Test model loading
    if not test_model_loading():
        logger.error("Model loading test failed")
        return 1

    logger.info("All tests passed")
    return 0

if __name__ == "__main__":
    sys.exit(main())
