# Trading Strategies

This document describes the trading strategies implemented in the MT5 Trading System.

## Overview

The MT5 Trading System implements a multi-model, multi-timeframe trading strategy that leverages different machine learning models across 5 terminals. Each terminal specializes in a specific model type, providing diversified trading signals and risk distribution.

## Strategy Architecture

### Multi-Terminal Approach

The system uses 5 MT5 terminals, each running a specialized trading strategy:

1. **Terminal 1 (ARIMA)**: Statistical trend-following strategy
2. **Terminal 2 (LSTM)**: Deep learning pattern recognition strategy
3. **Terminal 3 (TFT)**: Transformer-based multi-horizon strategy
4. **Terminal 4 (LSTM+ARIMA)**: Hybrid ensemble strategy
5. **Terminal 5 (TFT+ARIMA)**: Advanced ensemble strategy

### Multi-Timeframe Analysis

Each terminal analyzes multiple timeframes simultaneously:
- **M5**: Short-term signals and entry timing
- **M15**: Medium-term trend confirmation
- **M30**: Intermediate trend analysis
- **H1**: Long-term trend direction
- **H4**: Major trend confirmation

## Signal Generation

### Model-Based Predictions

Each model generates price predictions based on historical data and technical indicators:

```python
# Example signal generation process
def generate_trading_signal(model, data, timeframe):
    # 1. Prepare features
    features = prepare_features(data)

    # 2. Generate prediction
    prediction = model.predict(features)

    # 3. Calculate signal strength
    signal_strength = calculate_signal_strength(prediction, data)

    # 4. Determine direction
    direction = "BUY" if prediction > current_price else "SELL"

    return {
        'direction': direction,
        'strength': signal_strength,
        'confidence': model.get_confidence(),
        'timeframe': timeframe
    }
```

### Signal Aggregation

Signals from multiple timeframes are aggregated using weighted voting:

```python
def aggregate_signals(signals):
    # Timeframe weights (higher for longer timeframes)
    weights = {
        'M5': 0.1,
        'M15': 0.15,
        'M30': 0.2,
        'H1': 0.25,
        'H4': 0.3
    }

    # Calculate weighted signal
    weighted_signal = sum(
        signal['strength'] * weights[signal['timeframe']]
        for signal in signals
    )

    return weighted_signal
```

## Entry Conditions

### Primary Entry Signals

1. **Trend Alignment**: Multiple timeframes show same direction
2. **Signal Strength**: Aggregated signal exceeds threshold (0.6)
3. **Model Confidence**: Individual model confidence > 70%
4. **Risk Management**: Position size within risk limits

### Entry Logic

```python
def should_enter_trade(signals, risk_manager):
    # Check signal alignment
    aligned_signals = check_signal_alignment(signals)
    if not aligned_signals:
        return False

    # Check signal strength
    aggregated_strength = aggregate_signals(signals)
    if aggregated_strength < 0.6:
        return False

    # Check risk limits
    if not risk_manager.can_open_position():
        return False

    return True
```

## Exit Conditions

### Stop Loss

- **Fixed Stop Loss**: 2% of entry price
- **Trailing Stop**: Activated after 1% profit
- **Time-based Stop**: Close after 24 hours if no profit

### Take Profit

- **Target 1**: 1.5% profit (close 50% of position)
- **Target 2**: 3% profit (close remaining 50%)
- **Dynamic Target**: Adjusted based on volatility

### Exit Logic

```python
def check_exit_conditions(position, current_price):
    entry_price = position['entry_price']
    direction = position['direction']

    if direction == "BUY":
        # Stop loss
        if current_price <= entry_price * 0.98:
            return "STOP_LOSS"

        # Take profit
        if current_price >= entry_price * 1.015:
            return "TAKE_PROFIT_1"
        if current_price >= entry_price * 1.03:
            return "TAKE_PROFIT_2"

    elif direction == "SELL":
        # Stop loss
        if current_price >= entry_price * 1.02:
            return "STOP_LOSS"

        # Take profit
        if current_price <= entry_price * 0.985:
            return "TAKE_PROFIT_1"
        if current_price <= entry_price * 0.97:
            return "TAKE_PROFIT_2"

    return None
```

## Position Sizing

### Risk-Based Sizing

Position size is calculated based on account balance and risk tolerance:

```python
def calculate_position_size(account_balance, risk_per_trade, stop_loss_pips):
    # Risk per trade (default: 1% of account)
    risk_amount = account_balance * risk_per_trade

    # Position size based on stop loss
    pip_value = 10  # For BTCUSD.a
    position_size = risk_amount / (stop_loss_pips * pip_value)

    # Apply maximum position limits
    max_position = account_balance * 0.1  # Max 10% of account
    position_size = min(position_size, max_position)

    return position_size
```

### Dynamic Sizing

Position size is adjusted based on:
- **Model Confidence**: Higher confidence = larger position
- **Signal Strength**: Stronger signals = larger position
- **Market Volatility**: Higher volatility = smaller position
- **Recent Performance**: Better performance = larger position

## Risk Management

### Portfolio Risk

- **Maximum Risk per Trade**: 1% of account balance
- **Maximum Daily Risk**: 5% of account balance
- **Maximum Open Positions**: 3 per symbol
- **Correlation Limits**: Maximum 2 correlated positions

### Model-Specific Risk

Each terminal has individual risk limits:
- **Terminal Risk**: 0.5% per terminal per trade
- **Model Drawdown Limit**: 10% maximum drawdown
- **Performance Monitoring**: Automatic model disabling if performance degrades

## Strategy Performance

### Key Metrics

- **Win Rate**: Target 55-60%
- **Risk-Reward Ratio**: Minimum 1:1.5
- **Maximum Drawdown**: Target <15%
- **Sharpe Ratio**: Target >1.0
- **Profit Factor**: Target >1.3

### Performance Monitoring

```python
def monitor_strategy_performance():
    metrics = {
        'total_trades': get_total_trades(),
        'win_rate': calculate_win_rate(),
        'profit_factor': calculate_profit_factor(),
        'sharpe_ratio': calculate_sharpe_ratio(),
        'max_drawdown': calculate_max_drawdown()
    }

    # Check performance thresholds
    if metrics['win_rate'] < 0.45:
        alert_low_win_rate()

    if metrics['max_drawdown'] > 0.20:
        reduce_position_sizes()

    return metrics
```

## Strategy Optimization

### Walk-Forward Optimization

The system uses walk-forward optimization to adapt to changing market conditions:

1. **Training Period**: 6 months of historical data
2. **Testing Period**: 1 month forward testing
3. **Reoptimization**: Monthly parameter updates
4. **Validation**: Out-of-sample performance verification

### Parameter Adaptation

Key parameters are automatically adjusted based on performance:
- **Signal Thresholds**: Adjusted based on recent accuracy
- **Stop Loss Levels**: Adjusted based on volatility
- **Position Sizes**: Adjusted based on recent performance
- **Model Weights**: Adjusted based on individual model performance

Last updated: 2025-06-06 16:05:00
