"""
Master System Setup Script.
This script orchestrates the complete setup and optimization of the MT5 multi-terminal trading system.

Usage:
    python scripts/master_system_setup.py [options]

Options:
    --symbol BTCUSD.a        Symbol to process (default: BTCUSD.a)
    --years 5                Years of historical data (default: 5)
    --skip-optimization      Skip system optimization step
    --skip-data-collection   Skip data collection step
    --skip-model-training    Skip model training step
    --parallel-collection    Use parallel data collection
    --force-retrain          Force retraining of existing models
    --quick-setup            Perform quick setup (minimal data, faster)
"""
import argparse
import logging
import sys
import os
import subprocess
import time
from datetime import datetime
from typing import List, Dict, Any

# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/master_system_setup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MasterSystemSetup:
    """Master system setup orchestrator."""
    
    def __init__(self):
        """Initialize the master setup."""
        self.setup_stats = {}
        self.start_time = datetime.now()
        
    def run_script(self, script_path: str, args: List[str] = None) -> bool:
        """Run a Python script with optional arguments."""
        try:
            cmd = [sys.executable, script_path]
            if args:
                cmd.extend(args)
            
            logger.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                logger.info(f"✓ Script completed successfully: {script_path}")
                if result.stdout:
                    logger.info(f"Output: {result.stdout[-500:]}")  # Last 500 chars
                return True
            else:
                logger.error(f"✗ Script failed: {script_path}")
                logger.error(f"Return code: {result.returncode}")
                if result.stderr:
                    logger.error(f"Error: {result.stderr}")
                if result.stdout:
                    logger.error(f"Output: {result.stdout}")
                return False
                
        except Exception as e:
            logger.error(f"Exception running script {script_path}: {str(e)}")
            return False
    
    def step_1_system_optimization(self, symbol: str) -> bool:
        """Step 1: System optimization and cleanup."""
        logger.info("\n" + "="*80)
        logger.info("STEP 1: SYSTEM OPTIMIZATION AND CLEANUP")
        logger.info("="*80)
        
        script_path = "scripts/comprehensive_system_optimization.py"
        args = [
            "--symbol", symbol,
            "--full-optimization"
        ]
        
        success = self.run_script(script_path, args)
        self.setup_stats['optimization'] = success
        return success
    
    def step_2_data_collection(self, symbol: str, years: int, parallel: bool = False) -> bool:
        """Step 2: Optimized data collection."""
        logger.info("\n" + "="*80)
        logger.info("STEP 2: OPTIMIZED DATA COLLECTION")
        logger.info("="*80)
        
        script_path = "scripts/optimize_data_collection.py"
        args = [
            "--symbol", symbol,
            "--years", str(years),
            "--validate",
            "--clean-duplicates"
        ]
        
        if parallel:
            args.append("--parallel")
        
        success = self.run_script(script_path, args)
        self.setup_stats['data_collection'] = success
        return success
    
    def step_3_system_validation_and_training(
        self, 
        symbol: str, 
        years: int, 
        force_retrain: bool = False,
        skip_data_collection: bool = False
    ) -> bool:
        """Step 3: System validation and model training."""
        logger.info("\n" + "="*80)
        logger.info("STEP 3: SYSTEM VALIDATION AND MODEL TRAINING")
        logger.info("="*80)
        
        script_path = "scripts/validate_and_train_system.py"
        args = [
            "--symbol", symbol,
            "--years", str(years)
        ]
        
        if force_retrain:
            args.append("--force-retrain")
        
        if skip_data_collection:
            args.append("--skip-data-collection")
        
        success = self.run_script(script_path, args)
        self.setup_stats['validation_training'] = success
        return success
    
    def step_4_final_validation(self, symbol: str) -> bool:
        """Step 4: Final system validation."""
        logger.info("\n" + "="*80)
        logger.info("STEP 4: FINAL SYSTEM VALIDATION")
        logger.info("="*80)
        
        script_path = "scripts/validate_and_train_system.py"
        args = [
            "--symbol", symbol,
            "--validate-only",
            "--skip-data-collection"
        ]
        
        success = self.run_script(script_path, args)
        self.setup_stats['final_validation'] = success
        return success
    
    def generate_setup_report(self, symbol: str) -> None:
        """Generate a comprehensive setup report."""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        logger.info("\n" + "="*80)
        logger.info("MASTER SYSTEM SETUP COMPLETION REPORT")
        logger.info("="*80)
        
        logger.info(f"\n📊 SETUP SUMMARY:")
        logger.info(f"Symbol: {symbol}")
        logger.info(f"Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Total duration: {duration}")
        
        logger.info(f"\n🔧 STEP RESULTS:")
        step_names = {
            'optimization': 'System Optimization',
            'data_collection': 'Data Collection',
            'validation_training': 'Validation & Training',
            'final_validation': 'Final Validation'
        }
        
        successful_steps = 0
        total_steps = len(self.setup_stats)
        
        for step_key, step_name in step_names.items():
            if step_key in self.setup_stats:
                status = "✓ SUCCESS" if self.setup_stats[step_key] else "✗ FAILED"
                logger.info(f"  {step_name}: {status}")
                if self.setup_stats[step_key]:
                    successful_steps += 1
        
        success_rate = (successful_steps / total_steps) * 100 if total_steps > 0 else 0
        logger.info(f"\nOverall success rate: {successful_steps}/{total_steps} ({success_rate:.1f}%)")
        
        # System status and next steps
        logger.info("\n" + "="*80)
        if success_rate >= 75:
            logger.info("🎉 SYSTEM SETUP COMPLETED SUCCESSFULLY")
            logger.info("\n✅ SYSTEM IS READY FOR TRADING")
            logger.info("\nNext steps:")
            logger.info("1. Start the multi-terminal trading bot:")
            logger.info("   python run_trading_bot.py --multi-terminal")
            logger.info("2. Monitor system performance:")
            logger.info("   python scripts/run_dashboard.py")
            logger.info("3. Check logs for any issues:")
            logger.info("   tail -f logs/trading_bot_*.log")
        else:
            logger.info("⚠️  SYSTEM SETUP COMPLETED WITH ISSUES")
            logger.info("\n❌ SYSTEM REQUIRES ATTENTION")
            logger.info("\nRecommended actions:")
            logger.info("1. Review the logs for failed steps")
            logger.info("2. Fix any identified issues")
            logger.info("3. Re-run the failed steps individually")
            logger.info("4. Contact support if issues persist")
        
        logger.info("="*80)
    
    def quick_setup(self, symbol: str) -> bool:
        """Perform a quick setup with minimal data."""
        logger.info("\n[QUICK SETUP] PERFORMING QUICK SETUP")
        logger.info("This will set up the system with minimal data for faster initialization.")
        
        # Quick optimization
        logger.info("\n1. Quick system optimization...")
        if not self.step_1_system_optimization(symbol):
            logger.warning("Quick optimization had issues, but continuing...")
        
        # Minimal data collection (1 year)
        logger.info("\n2. Minimal data collection...")
        if not self.step_2_data_collection(symbol, years=1, parallel=True):
            logger.error("Data collection failed")
            return False
        
        # Quick training
        logger.info("\n3. Quick model training...")
        if not self.step_3_system_validation_and_training(
            symbol, years=1, force_retrain=False, skip_data_collection=True
        ):
            logger.error("Model training failed")
            return False
        
        logger.info("\n[SUCCESS] QUICK SETUP COMPLETED")
        return True

    def full_setup(
        self,
        symbol: str,
        years: int,
        skip_optimization: bool = False,
        skip_data_collection: bool = False,
        skip_model_training: bool = False,
        parallel_collection: bool = False,
        force_retrain: bool = False
    ) -> bool:
        """Perform a full system setup."""
        logger.info("\n[FULL SETUP] PERFORMING FULL SYSTEM SETUP")
        logger.info("This will set up the complete system with all optimizations.")
        
        # Step 1: System optimization
        if not skip_optimization:
            if not self.step_1_system_optimization(symbol):
                logger.warning("System optimization had issues, but continuing...")
        else:
            logger.info("Skipping system optimization as requested")
            self.setup_stats['optimization'] = True
        
        # Step 2: Data collection
        if not skip_data_collection:
            if not self.step_2_data_collection(symbol, years, parallel_collection):
                logger.error("Data collection failed")
                return False
        else:
            logger.info("Skipping data collection as requested")
            self.setup_stats['data_collection'] = True
        
        # Step 3: System validation and training
        if not skip_model_training:
            if not self.step_3_system_validation_and_training(
                symbol, years, force_retrain, skip_data_collection
            ):
                logger.error("System validation and training failed")
                return False
        else:
            logger.info("Skipping model training as requested")
            self.setup_stats['validation_training'] = True
        
        # Step 4: Final validation
        if not self.step_4_final_validation(symbol):
            logger.warning("Final validation had issues, but setup is complete")
        
        logger.info("\n[SUCCESS] FULL SETUP COMPLETED")
        return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Master system setup and optimization')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Symbol to process')
    parser.add_argument('--years', type=int, default=5, help='Years of historical data')
    parser.add_argument('--skip-optimization', action='store_true', help='Skip system optimization')
    parser.add_argument('--skip-data-collection', action='store_true', help='Skip data collection')
    parser.add_argument('--skip-model-training', action='store_true', help='Skip model training')
    parser.add_argument('--parallel-collection', action='store_true', help='Use parallel data collection')
    parser.add_argument('--force-retrain', action='store_true', help='Force retraining of models')
    parser.add_argument('--quick-setup', action='store_true', help='Perform quick setup')
    
    args = parser.parse_args()
    
    logger.info("="*80)
    logger.info("MT5 MULTI-TERMINAL TRADING SYSTEM - MASTER SETUP")
    logger.info("="*80)
    logger.info(f"Symbol: {args.symbol}")
    logger.info(f"Years of data: {args.years}")
    logger.info(f"Quick setup: {args.quick_setup}")
    logger.info(f"Parallel collection: {args.parallel_collection}")
    logger.info(f"Force retrain: {args.force_retrain}")
    
    # Initialize master setup
    master_setup = MasterSystemSetup()
    
    try:
        if args.quick_setup:
            success = master_setup.quick_setup(args.symbol)
        else:
            success = master_setup.full_setup(
                symbol=args.symbol,
                years=args.years,
                skip_optimization=args.skip_optimization,
                skip_data_collection=args.skip_data_collection,
                skip_model_training=args.skip_model_training,
                parallel_collection=args.parallel_collection,
                force_retrain=args.force_retrain
            )
        
        # Generate final report
        master_setup.generate_setup_report(args.symbol)
        
        if success:
            logger.info("Master system setup completed successfully!")
            return 0
        else:
            logger.error("Master system setup completed with errors!")
            return 1
            
    except KeyboardInterrupt:
        logger.info("\nSetup interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during setup: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == '__main__':
    sys.exit(main())
