{"1": {"model_type": "arima", "description": "ARIMA Models", "models": {"ETHUSD.a": {}, "BTCUSD.a": {"H4": {"model_path": "model\\saved_models\\arima\\BTCUSD.a_H4_arima.pkl", "metrics_path": "model\\saved_models\\arima\\BTCUSD.a_H4_arima_metrics.json", "is_best": false, "note": "Using arima model for BTCUSD.a (H4)"}}}}, "2": {"model_type": "lstm", "description": "LSTM Models", "models": {"ETHUSD.a": {}, "BTCUSD.a": {"H4": {"model_path": "model\\saved_models\\lstm\\BTCUSD.a_H4_lstm.pth", "metrics_path": "model\\saved_models\\lstm\\BTCUSD.a_H4_lstm_metrics.json", "is_best": false, "note": "Using lstm model for BTCUSD.a (H4)"}}}}, "3": {"model_type": "tft", "description": "TFT Models", "models": {"ETHUSD.a": {}, "BTCUSD.a": {"H4": {"model_path": "model\\saved_models\\tft\\BTCUSD.a_H4_tft.pth", "metrics_path": "model\\saved_models\\tft\\BTCUSD.a_H4_tft_metrics.json", "is_best": true, "note": "Best model for BTCUSD.a (H4)"}}}}, "4": {"model_type": "lstm_arima", "description": "LSTM + ARIMA Ensemble", "models": {"ETHUSD.a": {}, "BTCUSD.a": {"H4": {"model_path": "model\\saved_models\\ensemble\\BTCUSD.a_H4_lstm_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\BTCUSD.a_H4_lstm_arima_metrics.json", "is_best": false, "note": "Using lstm_arima model for BTCUSD.a (H4)"}}}}, "5": {"model_type": "tft_arima", "description": "TFT + ARIMA Ensemble", "models": {"ETHUSD.a": {}, "BTCUSD.a": {"H4": {"model_path": "model\\saved_models\\ensemble\\BTCUSD.a_H4_tft_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\BTCUSD.a_H4_tft_arima_metrics.json", "is_best": false, "note": "Using tft_arima model for BTCUSD.a (H4)"}}}}}