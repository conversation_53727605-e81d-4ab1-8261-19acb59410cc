# Advanced Trading System

A sophisticated trading system that supports multiple models, symbols, and timeframes with automatic optimization.

## Features

- **Multiple Model Types**:
  - LSTM (Long Short-Term Memory): Deep learning for sequence prediction
  - TFT (Temporal Fusion Transformer): State-of-the-art time series forecasting
  - ARIMA (AutoRegressive Integrated Moving Average): Statistical forecasting
  - Ensemble combinations for improved robustness

- **Terminal-Specific Models**:
  - Terminal 1: ARIMA
  - Terminal 2: LSTM
  - Terminal 3: TFT
  - Terminal 4: LSTM + ARIMA ensemble
  - Terminal 5: TFT + ARIMA ensemble with multi-timeframe

- **Multi-Symbol Support**:
  - Trade any symbol in format 'XXXYYY.a'
  - Independent configuration for each symbol
  - Automatic data collection and validation

- **Data-Driven Timeframe Selection**:
  - Automatically selects the best timeframe for each model and symbol
  - Supported timeframes: M5, M15, M30, H1, H4
  - Based on performance metrics (direction accuracy, profit factor, Sharpe ratio)

- **Advanced Trading Features**:
  - Dynamic Stop-Loss and Take-Profit based on market volatility
  - Continuous learning and adaptation to changing market conditions
  - Comprehensive performance metrics tracking
  - Model decay detection and monitoring
  - Walk-forward optimization for realistic performance assessment
  - Efficient data storage using Parquet format
  - Thorough data validation and quality assurance

## Project Structure

```
BTCUSD bot/
├── analysis/                # Market analysis and feature engineering
│   ├── feature_engineering.py  # Feature engineering and preprocessing
│   └── visualization/       # Advanced visualization tools
│       ├── time_series_visualizer.py  # Time series visualization
│       ├── feature_visualizer.py      # Feature visualization
│       ├── performance_visualizer.py  # Performance visualization
│       ├── model_decay_monitor.py     # Model decay monitoring
│       └── dashboard.py               # Comprehensive dashboard
├── config/                  # Configuration files
├── data/                    # Data collection and preprocessing
│   ├── data_collector.py    # Data collection from MT5
│   ├── data_manager.py      # Data management with Parquet storage
│   └── validation/          # Data validation tools
│       ├── data_validator.py     # Data quality validation
│       ├── feature_validator.py  # Feature validation
│       └── model_validator.py    # Model input validation
├── documentation/           # Detailed documentation
│   └── modules/             # Module-specific documentation
├── logs/                    # Log files
├── model/                   # LSTM model implementation
│   ├── model_manager.py     # Model management and training
│   ├── model_evaluator.py   # Model evaluation and testing
│   └── saved_models/        # Saved model files
├── monitoring/              # Performance monitoring and visualization
│   ├── performance_monitor.py  # Performance tracking
│   └── dashboard.py         # Real-time dashboard
├── trading/                 # Trading strategy and order execution
│   ├── mt5_connector.py     # MT5 connection and order execution
│   ├── order_manager.py     # Order management
│   └── trading_strategy.py  # Trading strategy implementation
├── utils/                   # Utility functions
├── README.md                # Project documentation
├── run_trading_bot.py       # Main execution script
├── run_btcusd_trading_bot.py # BTCUSD trading bot execution script
├── run_multi_timeframe_bot.py # Multi-timeframe trading bot execution script
├── run_mt5_initializer.py   # MT5 initializer script
├── train_m5_model.py        # M5 model training script
├── train_timeframe_models.py # Multi-timeframe model training script
├── generate_timeframe_dashboards.py # Dashboard generation script
└── trading_bot.py           # Main trading bot class
```

## Installation

1. Clone the repository:
```
git clone https://github.com/r70pro/BTCUSD-Trading-Bot.git
cd BTCUSD-Trading-Bot
```

2. Install the required packages:
```
pip install -r requirements.txt
```

3. Configure your MT5 terminals in `config/credentials.py`.

## Documentation

All documentation is organized in the `documentation` directory:

### Core Documentation
- [System Architecture](documentation/core/system_architecture.md): Overview of the system architecture
- [Introduction](documentation/core/introduction.md): Introduction to the trading system
- [Installation Guide](documentation/core/installation_guide.md): Installation instructions
- [Configuration Guide](documentation/core/configuration_guide.md): Configuration guide
- [Usage Guide](documentation/core/usage_guide.md): Usage guide
- [Troubleshooting](documentation/core/troubleshooting.md): Troubleshooting guide
- [Trading System Guide](documentation/core/trading_system_guide.md): Comprehensive guide to the trading system
- [Important Information](documentation/core/IMPORTANT_README.md): Important information about the trading system

### Model Documentation
- [LSTM Model](documentation/models/lstm_model.md): LSTM model documentation
- [TFT Model](documentation/models/tft_model.md): TFT model documentation
- [ARIMA Model](documentation/models/arima_model.md): ARIMA model documentation
- [Ensemble Model](documentation/models/ensemble_model.md): Ensemble model documentation
- [Model Storage](documentation/models/model_storage.md): Standardized model storage structure
- [Timeframe Optimizer](documentation/models/timeframe_optimizer.md): Timeframe optimizer documentation
- [Model Comparison](documentation/models/model_comparison.md): Comparison of different models
- [Model Security](documentation/models/model_security.md): Model security and conversion guide
- [Model Types](documentation/models/model_types.md): Overview of different model types

### Technical Documentation
- [MT5 Connection Guide](documentation/technical/mt5_connection_guide.md): Guide for connecting to MT5
- [MT5 Connection Best Practices](documentation/technical/mt5_connection_best_practices.md): Best practices for MT5 connection
- [Data Collection](documentation/technical/data_collection.md): Data collection documentation
- [Data Storage](documentation/technical/data_storage.md): Standardized data storage structure
- [Feature Engineering](documentation/technical/feature_engineering.md): Feature engineering documentation
- [MT5 Integration](documentation/technical/mt5_integration.md): MT5 integration documentation
- [M5 Data Collection Guide](documentation/technical/m5_data_collection_guide.md): Guide for collecting M5 data
- [Data Validation](documentation/technical/data_validation.md): Data validation documentation

### Trading Documentation
- [Trading Strategy](documentation/trading/trading_strategy.md): Trading strategy documentation
- [Order Management](documentation/trading/order_management.md): Order management documentation
- [Symbol Manager](documentation/trading/symbol_manager.md): Symbol manager documentation
- [Multi-Timeframe Guide](documentation/trading/multi_timeframe_guide.md): Guide for multi-timeframe trading
- [Dashboard](documentation/trading/dashboard.md): Dashboard documentation
- [Performance Monitoring](documentation/trading/performance_monitoring.md): Performance monitoring documentation

### Reference Documentation
- [API Reference](documentation/reference/api_reference.md): API reference documentation
- [Code Examples](documentation/reference/code_examples.md): Code examples
- [Future Enhancements](documentation/reference/future_enhancements.md): Future enhancements
- [GitHub Workflow](documentation/reference/github_workflow.md): GitHub workflow guide
- [Performance Metrics](documentation/reference/performance_metrics.md): Performance metrics documentation
- [Terminal Configuration](documentation/reference/terminal_configuration.md): Terminal configuration guide
- [Utilities](documentation/reference/utilities.md): Utilities documentation
- [Index](documentation/reference/index.md): Documentation index
- [Summary](documentation/reference/SUMMARY.md): Documentation summary

## Usage

### Adding a Trading Symbol

To add a new symbol to the system:

```bash
python scripts/add_symbol.py XXXYYY.a
```

Options:
- `--years N`: Number of years of historical data to collect (default: 5)
- `--no-validate`: Skip data validation
- `--terminals N [N ...]`: Specific terminal IDs to collect data from

Example:
```bash

```

### Initializing Models with Optimal Timeframes

To initialize models for all terminals with the best timeframe for each model type:

```bash
python scripts/initialize_terminal_models.py
```

Options:
- `--terminals 1 3 5`: Initialize specific terminals
- `--symbol ETHUSD.a`: Initialize for a specific symbol
- `--force-retrain`: Force retraining of existing models
python scripts/add_symbol.py ETHUSD.a --years 3 --terminals 1 2 3
### Timeframe Optimization

To run timeframe optimization once:

```bash
python scripts/optimize_timeframes.py
```

To schedule periodic optimization:

```bash
python scripts/optimize_timeframes.py --schedule --interval 168
```

### Training Models

To train models for specific timeframes:

```bash
python train_models.py --start-date 2023-01-01 --end-date 2024-04-01 --timeframes M5 M15 M30 H1 H4
```

You can customize the training parameters:

```bash
python train_m5_model.py --start-date 2023-01-01 --end-date 2024-04-01 --epochs 200 --batch-size 32 --sequence-length 20 --hidden-size 128 --num-layers 3 --patience 15
```

### Running a Backtest

```bash
python run_trading_bot.py --backtest --start-date 2020-01-01 --end-date 2023-01-01
```

### Running Walk-forward Optimization

```bash
python run_trading_bot.py --optimize --start-date 2020-01-01 --end-date 2023-01-01
```

### Generating a Performance Report

```bash
python run_trading_bot.py --report
```

### Visualization and Dashboard

```bash
python run_trading_bot.py --dashboard
```

The dashboard provides a comprehensive view of the trading system's performance, including:

- Interactive price charts with technical indicators
- Equity curve and drawdown analysis
- Trade distribution and performance metrics
- Feature importance and correlation analysis
- Model performance and decay monitoring
- Data validation results

### Data Validation

The system includes comprehensive data validation tools with performance optimizations:

```python
from data.validation.data_validator import DataValidator

# Initialize validator
data_validator = DataValidator()

# Add validation rules
data_validator.add_rule(
    rule_name="missing_values_check",
    rule_type="missing_values",
    parameters={
        "columns": ["open", "high", "low", "close", "volume"],
        "threshold": 0.05
    }
)

# Validate data with parallel execution for large datasets
validation_results = data_validator.validate_data(
    df=historical_data,
    parallel=True,
    max_workers=4  # Use 4 CPU cores
)

# Generate comprehensive validation report
report = data_validator.generate_validation_report()
```

Key features:
- Parallel execution for large datasets
- Statistical caching for improved performance
- Comprehensive validation reports
- Robust error handling
- Detailed logging for debugging

### Starting the Trading Bot

#### Step 1: Ensure MT5 Terminals Are Running

Before starting the trading bot, make sure all MT5 terminals are running and Algo Trading is enabled in each terminal:

1. Open all 5 MT5 terminals
2. In each terminal, click the "Algo Trading" button in the toolbar to enable automated trading
3. Verify that the "Algo Trading" button is highlighted, indicating that it's enabled

You can check the status of all terminals using:

```
python check_all_terminals_safely.py
```

#### Step 2: Download Recent Data (Recommended)

Before training the models, it's recommended to download the most recent 2 years of BTCUSD data:

```
python download_recent_data.py
```

You can customize the date range and timeframes:

```
python download_recent_data.py --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

Alternatively, you can use the underlying script directly:

```
python scripts/collect_historical_data.py --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

#### Step 3: Train the Models (Optional)

After downloading the data, you can train or update the models with the most recent 2 years of data:

```
python train_models.py --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

Or train just the M5 model with more customization:

```
python scripts/retrain_m5_model.py --start-date 2023-04-25 --end-date 2025-04-25 --epochs 100 --batch-size 64 --sequence-length 10 --hidden-size 64 --num-layers 2
```

For improved models with advanced parameters:

```
python train_improved_models.py --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

#### Step 4: Start the Trading Bot

To start the trading bot in normal trading mode:

```
python run_trading_bot.py
```

To start in monitor mode (generates signals but doesn't place trades):

```
python run_trading_bot.py --monitor
```

To start with the dashboard for real-time monitoring:

```
python run_trading_bot.py --dashboard
```

To combine options:

```
python run_trading_bot.py --monitor --dashboard
```

To train models with the most recent 2 years of data and then start the bot:

```
python run_trading_bot.py --train --start-date 2023-04-25 --end-date 2025-04-25 --timeframes M5 M15 M30 H1
```

For running multiple trading bots simultaneously:

```
python scripts/run_trading_bots.py
```

#### Step 5: Monitor Performance

Once the bot is running, you can monitor its performance through:

1. The console output showing signals and trades
2. The dashboard (if enabled) showing real-time performance metrics
3. Log files in the `logs` directory

#### Step 6: Stopping the Bot

To stop the bot, press `Ctrl+C` in the console where it's running. The bot will gracefully shut down, closing any necessary connections.

## MT5 Terminal Configuration

The system uses a specialized 5-terminal setup, with each terminal assigned a specific model type and trading strategy:

1. **Terminal ID 1 - ARIMA Model**:
   - Model Type: ARIMA (AutoRegressive Integrated Moving Average)
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Conservative
   - Trading Style: Statistical-based, mean-reversion
   - Position Size: 0.5-1% risk per trade
   - Stop Loss: Tighter (1.0x ATR multiplier)
   - Take Profit: 2:1 reward-to-risk ratio

2. **Terminal ID 2 - LSTM Model**:
   - Model Type: LSTM (Long Short-Term Memory)
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Moderate
   - Trading Style: Deep learning-based, pattern recognition
   - Position Size: 1-2% risk per trade
   - Stop Loss: Balanced (1.5x ATR multiplier)
   - Take Profit: 2.5:1 reward-to-risk ratio

3. **Terminal ID 3 - TFT Model**:
   - Model Type: TFT (Temporal Fusion Transformer)
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Moderate-Aggressive
   - Trading Style: Advanced pattern recognition with attention mechanisms
   - Position Size: 1.5-2.5% risk per trade
   - Stop Loss: Wider (2.0x ATR multiplier)
   - Take Profit: 3:1 reward-to-risk ratio

4. **Terminal ID 4 - LSTM+ARIMA Ensemble**:
   - Model Type: Ensemble combining LSTM and ARIMA
   - Timeframe: Optimized based on performance metrics
   - Risk Profile: Aggressive
   - Trading Style: Hybrid approach combining statistical and deep learning
   - Position Size: 2-3% risk per trade
   - Stop Loss: Strategic (2.5x ATR multiplier)
   - Take Profit: 3.5:1 reward-to-risk ratio

5. **Terminal ID 5 - TFT+ARIMA Ensemble with Multi-Timeframe**:
   - Model Type: Ensemble combining TFT and ARIMA with multi-timeframe analysis
   - Timeframe: Multiple timeframes with weighted signals
   - Risk Profile: Dynamic
   - Trading Style: Confirmation-based, high-probability setups
   - Position Size: Dynamic (0.5-3% based on signal strength)
   - Stop Loss: Adaptive based on volatility
   - Take Profit: Determined by confluence points
   - Signal Fusion Weights:
     - M5: 25%
     - M15: 20%
     - M30: 20%
     - H1: 20%
     - H4: 15%

This multi-model, multi-timeframe approach allows the system to:
1. Capture different market dynamics and trading opportunities
2. Leverage the strengths of each model type
3. Automatically select the optimal timeframe for each model and symbol
4. Provide high-probability trade setups based on model consensus

## Performance Metrics

The bot tracks the following performance metrics:

- Total Return
- Sharpe Ratio
- Sortino Ratio
- Maximum Drawdown
- Win Rate
- Profit Factor
- Expectancy
- Average Trade Duration

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

Trading cryptocurrencies involves significant risk and can result in the loss of your invested capital. The BTCUSD Trading Bot is provided for educational and informational purposes only. Always consult with a qualified professional before making any investment decisions.
