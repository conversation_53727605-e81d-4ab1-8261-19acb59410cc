"""
Model Manager Module.
This module manages different types of models (LSTM, TFT, ARIMA, Ensemble).
It provides a unified interface for creating, training, updating, and using models.
"""
import logging
import os
import json
import pickle
import traceback
from datetime import datetime
from typing import Dict, List, Optional
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader

# Import model implementations
from model.model_trainer import ModelTrainer
from model.tft_model import TFTModel
from model.arima_model import ARIMAModelTrainer
from model.ensemble_model import EnsembleModel

# Import configuration and path utilities
from config.central_config import CentralConfig
from utils.path_utils import (
    get_model_path,
    get_metrics_path,
    get_visualization_path,
    get_historical_data_path
)
from data.data_manager import DataManager

# Configure logger
logger = logging.getLogger('model.manager')

class ModelManager:
    """
    Model Manager class for managing different types of models.
    """
    def __init__(self):
        """
        Initialize ModelManager.
        """
        self.models = {}  # Dictionary to store models
        self.model_paths = {}  # Dictionary to store model file paths

        # Create model directories using the standardized paths
        os.makedirs(str(CentralConfig.SAVED_MODELS_DIR), exist_ok=True)
        os.makedirs(str(CentralConfig.SAVED_MODELS_DIR / 'lstm'), exist_ok=True)
        os.makedirs(str(CentralConfig.SAVED_MODELS_DIR / 'tft'), exist_ok=True)
        os.makedirs(str(CentralConfig.SAVED_MODELS_DIR / 'arima'), exist_ok=True)
        os.makedirs(str(CentralConfig.SAVED_MODELS_DIR / 'ensemble'), exist_ok=True)
        os.makedirs(str(CentralConfig.VISUALIZATIONS_DIR), exist_ok=True)

    def _save_metrics(self, metrics_path: str, metrics: Dict[str, float]) -> bool:
        """
        Save model metrics to a JSON file.

        Args:
            metrics_path: Path to save the metrics
            metrics: Dictionary of metrics to save

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert numpy types to Python types for JSON serialization
            serializable_metrics = {}
            for key, value in metrics.items():
                # Handle numpy integer types (including NumPy 2.0 compatibility)
                if isinstance(value, (np.integer, np.int8, np.int16, np.int32, np.int64,
                                     np.uint8, np.uint16, np.uint32, np.uint64)):
                    serializable_metrics[key] = int(value)
                # Handle numpy floating point types (including NumPy 2.0 compatibility)
                elif isinstance(value, (np.floating, np.float16, np.float32, np.float64)):
                    serializable_metrics[key] = float(value)
                else:
                    serializable_metrics[key] = value

            # Add timestamp
            serializable_metrics['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Save to JSON
            with open(metrics_path, 'w') as f:
                json.dump(serializable_metrics, f, indent=4)

            logger.info(f"Metrics saved to {metrics_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving metrics: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def create_model(
        self,
        model_name: str,
        model_type: str,
        input_size: int = None,
        hidden_size: int = 64,
        num_layers: int = 2,
        output_size: int = 1
    ) -> bool:
        """
        Create a new model.

        Args:
            model_name: Name of the model
            model_type: Type of model ('lstm', 'tft', 'arima', 'ensemble')
            input_size: Number of input features (required for LSTM and TFT)
            hidden_size: Number of hidden units (for LSTM and TFT)
            num_layers: Number of layers (for LSTM and TFT)
            output_size: Number of output units (for LSTM and TFT)

        Returns:
            bool: True if model was created successfully, False otherwise
        """
        try:
            if model_type == 'lstm':
                if input_size is None:
                    logger.error("input_size is required for LSTM models")
                    return False

                model = ModelTrainer(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    output_size=output_size
                )
                self.models[model_name] = model
                self.models[f"{model_name}_type"] = 'lstm'
                logger.info(f"Created LSTM model: {model_name}")
                return True

            elif model_type == 'tft':
                if input_size is None:
                    logger.error("input_size is required for TFT models")
                    return False

                # Use the new TFTModel implementation
                model = TFTModel(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_attention_heads=CentralConfig.TFT_PARAMS.get('attention_heads', 4),
                    dropout_rate=CentralConfig.TFT_PARAMS.get('dropout', 0.1),
                    learning_rate=CentralConfig.LEARNING_RATE,
                    num_lstm_layers=num_layers
                )
                self.models[model_name] = model
                self.models[f"{model_name}_type"] = 'tft'
                logger.info(f"Created TFT model: {model_name}")
                return True

            elif model_type == 'arima':
                model = ARIMAModelTrainer()
                self.models[model_name] = model
                self.models[f"{model_name}_type"] = 'arima'
                logger.info(f"Created ARIMA model: {model_name}")
                return True

            elif model_type == 'ensemble':
                # Ensemble models are created differently, through the EnsembleModel class
                logger.warning("Ensemble models should be created using the EnsembleModel class")
                return False

            else:
                logger.error(f"Unknown model type: {model_type}")
                return False

        except Exception as e:
            logger.error(f"Error creating model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def train_model(
        self,
        model_name: str,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: np.ndarray = None,
        y_val: np.ndarray = None,
        feature_names: List[str] = None,
        **kwargs
    ) -> bool:
        """
        Train a model.

        Args:
            model_name: Name of the model
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            feature_names: Feature names (for TFT models)
            **kwargs: Additional training parameters

        Returns:
            bool: True if model was trained successfully, False otherwise
        """
        try:
            if model_name not in self.models:
                logger.error(f"Model {model_name} not found")
                return False

            model_type = self.models.get(f"{model_name}_type")
            model = self.models[model_name]

            if model_type == 'lstm':
                # Train LSTM model
                batch_size = kwargs.get('batch_size', 32)
                epochs = kwargs.get('epochs', 100)
                patience = kwargs.get('patience', 10)

                model.train(
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    batch_size=batch_size,
                    epochs=epochs,
                    patience=patience
                )

                # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe_...)
                parts = model_name.split('_')
                if len(parts) >= 2:
                    symbol = parts[0]
                    timeframe = parts[1]
                else:
                    symbol = "BTCUSD.a"  # Default symbol
                    timeframe = "M5"     # Default timeframe

                # Get standardized model path following the required format
                # Terminal 2: model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
                model_path = get_model_path(symbol, timeframe, 'lstm')
                model.save_model(model_path)
                self.model_paths[model_name] = model_path

                # Save metrics separately following the required format
                # Terminal 2: model/saved_models/lstm/{symbol}_{timeframe}_lstm_metrics.json
                metrics = model.evaluate(X_val, y_val, feature_names)
                metrics_path = get_metrics_path(symbol, timeframe, 'lstm')
                self._save_metrics(metrics_path, metrics)

                logger.info(f"Model saved to {model_path}")
                logger.info(f"Metrics saved to {metrics_path}")

                logger.info(f"Trained and saved LSTM model: {model_name}")
                return True

            elif model_type == 'tft':
                # Train TFT model
                batch_size = kwargs.get('batch_size', 32)
                epochs = kwargs.get('epochs', 100)
                patience = kwargs.get('patience', 10)

                # Use the fit method of the TFTModel
                model.fit(
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    epochs=epochs,
                    batch_size=batch_size,
                    patience=patience,
                    verbose=True
                )

                # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe_...)
                parts = model_name.split('_')
                if len(parts) >= 2:
                    symbol = parts[0]
                    timeframe = parts[1]
                else:
                    symbol = "BTCUSD.a"  # Default symbol
                    timeframe = "M5"     # Default timeframe

                # Get standardized model path following the required format
                # Terminal 3: model/saved_models/tft/{symbol}_{timeframe}_tft.pth
                model_path = get_model_path(symbol, timeframe, 'tft')
                model.save(model_path)
                self.model_paths[model_name] = model_path

                logger.info(f"TFT model saved to {model_path}")

                # Calculate metrics
                if X_val is not None and y_val is not None:
                    y_pred = model.predict(X_val)

                    # Calculate basic metrics
                    mse = np.mean((y_pred - y_val) ** 2)
                    rmse = np.sqrt(mse)
                    mae = np.mean(np.abs(y_pred - y_val))

                    # Calculate directional accuracy
                    direction_correct = np.sum((y_pred[1:] > y_pred[:-1]) == (y_val[1:] > y_val[:-1]))
                    direction_accuracy = direction_correct / (len(y_val) - 1) if len(y_val) > 1 else 0

                    metrics = {
                        'mse': float(mse),
                        'rmse': float(rmse),
                        'mae': float(mae),
                        'direction_accuracy': float(direction_accuracy)
                    }

                    # Save metrics following the required format
                    # Terminal 3: model/saved_models/tft/{symbol}_{timeframe}_tft_metrics.json
                    metrics_path = get_metrics_path(symbol, timeframe, 'tft')
                    self._save_metrics(metrics_path, metrics)

                    logger.info(f"TFT metrics saved to {metrics_path}")

                    # Create visualization
                    try:
                        import matplotlib.pyplot as plt
                        plt.figure(figsize=(12, 6))
                        plt.plot(y_val, label='Actual')
                        plt.plot(y_pred, label='Predicted')
                        plt.title(f'TFT Model Predictions - {symbol} {timeframe}')
                        plt.legend()
                        plt.grid(True)

                        # Save visualization
                        viz_path = get_visualization_path(symbol, timeframe, 'tft', 'predictions')
                        os.makedirs(os.path.dirname(viz_path), exist_ok=True)
                        plt.savefig(viz_path)
                        plt.close()
                    except Exception as viz_error:
                        logger.warning(f"Failed to create visualization: {str(viz_error)}")

                logger.info(f"Trained and saved TFT model: {model_name}")
                return True

            elif model_type == 'arima':
                # Train ARIMA model
                # For ARIMA, we use the time series directly
                model.train(y_train)

                # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe_...)
                parts = model_name.split('_')
                if len(parts) >= 2:
                    symbol = parts[0]
                    timeframe = parts[1]
                else:
                    symbol = "BTCUSD.a"  # Default symbol
                    timeframe = "M5"     # Default timeframe

                # Get standardized model path following the required format
                # Terminal 1: model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
                model_path = get_model_path(symbol, timeframe, 'arima')
                model.save_model(model_path)
                self.model_paths[model_name] = model_path

                logger.info(f"ARIMA model saved to {model_path}")

                # Save metrics separately
                # For ARIMA, we need to calculate metrics on the training data
                predictions = model.forecast(1, y_train)

                # Calculate basic metrics
                mse = np.mean((predictions - y_train) ** 2)
                rmse = np.sqrt(mse)
                mae = np.mean(np.abs(predictions - y_train))

                # Calculate directional accuracy
                direction_correct = np.sum((predictions > 0) == (y_train > 0))
                direction_accuracy = direction_correct / len(y_train)

                metrics = {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'direction_accuracy': float(direction_accuracy)
                }

                # Save metrics following the required format
                # Terminal 1: model/saved_models/arima/{symbol}_{timeframe}_arima_metrics.json
                metrics_path = get_metrics_path(symbol, timeframe, 'arima')
                self._save_metrics(metrics_path, metrics)

                logger.info(f"ARIMA metrics saved to {metrics_path}")

                logger.info(f"Trained and saved ARIMA model: {model_name}")
                return True

            elif model_type == 'ensemble':
                # Ensemble models don't need training as they use trained component models
                logger.info(f"Ensemble model {model_name} doesn't require direct training")
                return True

            else:
                logger.error(f"Unknown model type: {model_type}")
                return False

        except Exception as e:
            logger.error(f"Error training model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def update_model(
        self,
        model_name: str,
        X: np.ndarray,
        y: np.ndarray,
        epochs: int = 10,
        use_amp: bool = True,
        **kwargs
    ) -> bool:
        """
        Update a model with new data using optimized performance settings.

        Args:
            model_name: Name of the model
            X: Input features (can be None for ARIMA models)
            y: Target values
            epochs: Number of epochs for the update (default: 10)
            use_amp: Whether to use Automatic Mixed Precision for faster training
            **kwargs: Additional update parameters

        Returns:
            bool: True if model was updated successfully, False otherwise
        """
        try:
            if model_name not in self.models:
                logger.error(f"Model {model_name} not found")
                return False

            model_type = self.models.get(f"{model_name}_type")
            model = self.models[model_name]

            if model_type == 'lstm' or model_type == 'tft':
                # For sequence models, we can use the same training method with fewer epochs
                batch_size = kwargs.get('batch_size', 32)

                # Determine batch size based on data size
                if len(X) < 1000:
                    # Use smaller batch size for small datasets
                    batch_size = min(batch_size, max(16, len(X) // 10))

                # Determine number of workers based on data size
                num_workers = 4 if len(X) > 10000 else (2 if len(X) > 1000 else 0)

                # Split data for validation (simple 80/20 split)
                split_idx = int(len(X) * 0.8)
                X_train, X_val = X[:split_idx], X[split_idx:]
                y_train, y_val = y[:split_idx], y[split_idx:]

                # Update model with optimized settings
                model.train(
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    batch_size=batch_size,
                    epochs=epochs,
                    patience=5,  # Shorter patience for updates
                    use_amp=use_amp,
                    num_workers=num_workers,
                    pin_memory=num_workers > 0
                )

                # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe_...)
                parts = model_name.split('_')
                if len(parts) >= 2:
                    symbol = parts[0]
                    timeframe = parts[1]
                else:
                    symbol = "BTCUSD.a"  # Default symbol
                    timeframe = "M5"     # Default timeframe

                # Get standardized model path following the required format
                # Terminal 2: model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
                # Terminal 3: model/saved_models/tft/{symbol}_{timeframe}_tft.pth
                model_path = get_model_path(symbol, timeframe, model_type)
                model.save_model(model_path)

                logger.info(f"Updated and saved {model_type.upper()} model: {model_name} to {model_path}")
                return True

            elif model_type == 'arima':
                # For ARIMA models, we update with new data
                model.update(y)

                # Extract symbol and timeframe from model_name
                parts = model_name.split('_')
                if len(parts) >= 2:
                    symbol = parts[0]
                    timeframe = parts[1]
                else:
                    symbol = "BTCUSD.a"  # Default symbol
                    timeframe = "M5"     # Default timeframe

                # Get standardized model path following the required format
                # Terminal 1: model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
                model_path = get_model_path(symbol, timeframe, 'arima')
                model.save_model(model_path)

                logger.info(f"Updated and saved ARIMA model: {model_name}")
                return True

            elif model_type in ['ensemble', 'lstm_arima', 'tft_arima']:
                # Extract symbol and timeframe from model_name
                parts = model_name.split('_')
                if len(parts) >= 2:
                    symbol = parts[0]
                    timeframe = parts[1]
                else:
                    symbol = "BTCUSD.a"  # Default symbol
                    timeframe = "M5"     # Default timeframe

                # Determine ensemble type
                ensemble_type = model_type
                if 'lstm_arima' in model_name:
                    ensemble_type = 'lstm_arima'
                elif 'tft_arima' in model_name:
                    ensemble_type = 'tft_arima'

                # Get standardized model path following the required format
                # Terminal 4: model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima.pkl
                # Terminal 5: model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima.pkl
                model_path = get_model_path(symbol, timeframe, ensemble_type)

                # For ensemble models, we may need to update component models first
                logger.info(f"Ensemble model {model_name} updated. Path: {model_path}")
                return True

            else:
                logger.error(f"Unknown model type: {model_type}")
                return False

        except Exception as e:
            logger.error(f"Error updating model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def predict(
        self,
        model_name: str,
        X: np.ndarray,
        feature_names: List[str] = None,
        steps: int = 1,
        time_series: Optional[np.ndarray] = None
    ) -> np.ndarray:
        """
        Make predictions with a model.

        Args:
            model_name: Name of the model
            X: Input features (sequence data for LSTM/TFT models, time series for ARIMA models)
            feature_names: Feature names (for TFT models)
            steps: Number of steps to forecast (for ARIMA models)
            time_series: Optional time series data for ARIMA models (if X is structured differently)

        Returns:
            np.ndarray: Predictions
        """
        try:
            if model_name not in self.models:
                logger.error(f"Model {model_name} not found")
                return np.array([])

            model_type = self.models.get(f"{model_name}_type")
            model = self.models[model_name]

            if model_type == 'lstm':
                # For LSTM models, we use the predict method with sequence data
                predictions = model.predict(X)
                return predictions

            elif model_type == 'tft':
                # For TFT models, we use the predict method with sequence data and feature names
                if feature_names is None:
                    # Create dummy feature names if not provided
                    if isinstance(X, np.ndarray) and len(X.shape) == 3:
                        feature_names = [f"feature_{i}" for i in range(X.shape[2])]
                    else:
                        logger.warning(f"Cannot create feature names for TFT model: unexpected input shape {X.shape}")

                predictions = model.predict(X, feature_names)
                return predictions

            elif model_type == 'arima':
                # For ARIMA models, we forecast future values using time_series if provided, otherwise X
                data = time_series if time_series is not None else X
                predictions = model.forecast(steps, data)
                return predictions

            elif model_type == 'ensemble':
                # For ensemble models, we use the predict method of the ensemble with all available data
                predictions = model.predict(
                    X=X,
                    feature_names=feature_names,
                    steps=steps,
                    time_series=time_series
                )
                return predictions

            else:
                logger.error(f"Unknown model type: {model_type}")
                return np.array([])

        except Exception as e:
            logger.error(f"Error making predictions with model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return np.array([])

    def evaluate_model(
        self,
        model_name: str,
        X: np.ndarray,
        y: np.ndarray,
        feature_names: List[str] = None,
        time_series: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """
        Evaluate a model.

        Args:
            model_name: Name of the model
            X: Input features (sequence data for LSTM/TFT models)
            y: Target values
            feature_names: Feature names (for TFT models)
            time_series: Optional time series data for ARIMA models

        Returns:
            Dict[str, float]: Evaluation metrics
        """
        try:
            if model_name not in self.models:
                logger.error(f"Model {model_name} not found")
                return {}

            model_type = self.models.get(f"{model_name}_type")
            model = self.models[model_name]

            if model_type == 'lstm':
                # For LSTM models, we use the evaluate method with sequence data
                metrics = model.evaluate(X, y)
                return metrics

            elif model_type == 'tft':
                # For TFT models, we use the evaluate method with sequence data and feature names
                metrics = model.evaluate(X, y, feature_names)
                return metrics

            elif model_type == 'arima':
                # For ARIMA models, we calculate metrics manually
                data = time_series if time_series is not None else X
                predictions = model.forecast(1, data)

                # Calculate basic metrics
                mse = np.mean((predictions - y) ** 2)
                rmse = np.sqrt(mse)
                mae = np.mean(np.abs(predictions - y))

                # Calculate R-squared (coefficient of determination)
                ss_res = np.sum((y - predictions) ** 2)
                ss_tot = np.sum((y - np.mean(y)) ** 2)
                r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0

                # Calculate directional accuracy
                direction_correct = np.sum((predictions > 0) == (y > 0))
                direction_accuracy = direction_correct / len(y)

                return {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2),
                    'direction_accuracy': float(direction_accuracy)
                }

            elif model_type == 'ensemble':
                # For ensemble models, we use the evaluate method of the ensemble with all available data
                metrics = model.evaluate(
                    X=X,
                    y=y,
                    feature_names=feature_names,
                    time_series=time_series
                )
                return metrics

            else:
                logger.error(f"Unknown model type: {model_type}")
                return {}

        except Exception as e:
            logger.error(f"Error evaluating model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return {}

    def load_model(self, model_name: str, model_path: str = None, model_type: str = None) -> bool:
        """
        Load a model from disk.

        Args:
            model_name: Name of the model
            model_path: Path to the model file (optional)
            model_type: Type of model (optional)

        Returns:
            bool: True if model was loaded successfully, False otherwise
        """
        try:
            # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe_...)
            parts = model_name.split('_')
            if len(parts) < 3:
                logger.error(f"Invalid model name format: {model_name}. Expected format: symbol_timeframe_type")
                return False

            # Handle AUDNZD.a format correctly
            if '.' in model_name:
                # For names like "AUDNZD.a_M5_lstm"
                symbol = parts[0] + '.' + parts[1]  # "AUDNZD.a"
                timeframe = parts[2]  # "M5"
            else:
                # For names like "EURUSD_M5_lstm"
                symbol = parts[0]  # "EURUSD"
                timeframe = parts[1]  # "M5"

            # If model_path is not provided, determine it based on model_type or infer from model_name
            if model_path is None:
                # Determine model type from model name if not provided
                if model_type is None:
                    if '_lstm_arima' in model_name:
                        model_type = 'lstm_arima'
                    elif '_tft_arima' in model_name:
                        model_type = 'tft_arima'
                    elif '_lstm' in model_name:
                        model_type = 'lstm'
                    elif '_tft' in model_name:
                        model_type = 'tft'
                    elif '_arima' in model_name:
                        model_type = 'arima'
                    elif '_ensemble' in model_name:
                        model_type = 'ensemble'
                    else:
                        # Try to infer model type from directory structure
                        for mtype in ['lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima', 'ensemble']:
                            path = get_model_path(symbol, timeframe, mtype)
                            if os.path.exists(path):
                                model_type = mtype
                                break
                        else:
                            logger.error(f"Could not determine model type for {model_name}")
                            return False

                # Get model path based on model type
                model_path = get_model_path(symbol, timeframe, model_type)

            # Check if model file exists
            if not os.path.exists(model_path):
                logger.error(f"Model file not found: {model_path}")
                return False

            # If model_type is not provided, try to determine it from the model_path
            if model_type is None:
                if 'lstm_arima' in model_path:
                    model_type = 'lstm_arima'
                elif 'tft_arima' in model_path:
                    model_type = 'tft_arima'
                elif '/lstm/' in model_path or '_lstm' in model_path:
                    model_type = 'lstm'
                elif '/tft/' in model_path or '_tft' in model_path:
                    model_type = 'tft'
                elif '/arima/' in model_path or '_arima' in model_path:
                    model_type = 'arima'
                elif '/ensemble/' in model_path or '_ensemble' in model_path:
                    model_type = 'ensemble'
                else:
                    logger.error(f"Could not determine model type from path: {model_path}")
                    return False

            # Load model based on type
            if model_type == 'lstm':
                # Create a new model trainer
                model = ModelTrainer(input_size=1)  # Placeholder input_size

                # Load model from file with safe loading
                try:
                    if model.load_model(model_path):
                        self.models[model_name] = model
                        self.models[f"{model_name}_type"] = 'lstm'
                        self.model_paths[model_name] = model_path
                        logger.info(f"Loaded LSTM model: {model_name} from {model_path}")
                        return True
                    else:
                        logger.error(f"Failed to load LSTM model: {model_name} from {model_path}")
                        return False
                except Exception as e:
                    logger.error(f"Error loading LSTM model {model_name}: {str(e)}")
                    return False

            elif model_type == 'tft':
                try:
                    # Load TFT model from file using the class method
                    model = TFTModel.load(model_path)
                    self.models[model_name] = model
                    self.models[f"{model_name}_type"] = 'tft'
                    self.model_paths[model_name] = model_path
                    logger.info(f"Loaded TFT model: {model_name} from {model_path}")
                    return True
                except Exception as e:
                    logger.error(f"Failed to load TFT model: {model_name} from {model_path}: {str(e)}")
                    return False

            elif model_type == 'arima':
                # Create a new ARIMA trainer
                model = ARIMAModelTrainer()

                # Load model from file
                if model.load_model(model_path):
                    self.models[model_name] = model
                    self.models[f"{model_name}_type"] = 'arima'
                    self.model_paths[model_name] = model_path
                    logger.info(f"Loaded ARIMA model: {model_name} from {model_path}")
                    return True
                else:
                    logger.error(f"Failed to load ARIMA model: {model_name} from {model_path}")
                    return False

            elif model_type in ['ensemble', 'lstm_arima', 'tft_arima']:
                # For ensemble models, we need to load the component models first
                # Load the ensemble model state first to get component model names
                try:
                    with open(model_path, 'rb') as f:
                        model_state = pickle.load(f)

                    # Get component model weights and paths
                    weights = model_state.get('weights', [])
                    model_paths = model_state.get('model_paths', [])
                    model_types = model_state.get('model_types', [])

                    # Create ensemble model using the loaded state
                    if model_paths and model_types:
                        # Use model paths and types
                        ensemble = EnsembleModel(
                            model_paths=model_paths,
                            model_types=model_types,
                            weights=weights
                        )
                    else:
                        # Fallback: create empty ensemble and load later
                        ensemble = EnsembleModel(
                            model_paths=[],
                            model_types=[],
                            weights=[]
                        )

                    # Load the ensemble model state
                    success = True
                except Exception as e:
                    logger.error(f"Error loading ensemble model state: {str(e)}")
                    return False

                # Load model from file
                if success:
                    # Now we need to load all component models
                    for component_model in ensemble.model_names:
                        if component_model not in self.models:
                            # Try to load the component model
                            if not self.load_model(component_model):
                                logger.warning(f"Failed to load component model {component_model} for ensemble {model_name}")

                    self.models[model_name] = ensemble
                    self.models[f"{model_name}_type"] = 'ensemble'
                    self.model_paths[model_name] = model_path
                    logger.info(f"Loaded ensemble model: {model_name} from {model_path}")
                    return True
                else:
                    logger.error(f"Failed to load ensemble model: {model_name} from {model_path}")
                    return False

            else:
                logger.error(f"Unknown model type: {model_type}")
                return False

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def create_ensemble(
        self,
        ensemble_name: str,
        model_names: List[str],
        weights: Optional[List[float]] = None
    ) -> bool:
        """
        Create an ensemble model.

        Args:
            ensemble_name: Name of the ensemble
            model_names: List of model names to include in the ensemble
            weights: Optional weights for each model (default: equal weights)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if all component models exist
            for model_name in model_names:
                if model_name not in self.models:
                    logger.error(f"Component model {model_name} not found")
                    return False

            # Get the actual model objects for the ensemble
            component_models = []
            for model_name in model_names:
                model = self.models.get(model_name)
                if model is not None:
                    component_models.append(model)
                else:
                    # Try to load the component model from disk
                    logger.info(f"Component model {model_name} not in memory, attempting to load from disk")

                    # Extract model type from model name
                    if 'lstm' in model_name.lower():
                        model_type = 'lstm'
                    elif 'tft' in model_name.lower():
                        model_type = 'tft'
                    elif 'arima' in model_name.lower():
                        model_type = 'arima'
                    else:
                        logger.error(f"Cannot determine model type for {model_name}")
                        return False

                    # Get the model path for the component model
                    from utils.path_utils import get_model_path

                    # Extract symbol and timeframe from model name (format: SYMBOL_TIMEFRAME_TYPE)
                    parts = model_name.split('_')
                    if len(parts) >= 3:
                        symbol = parts[0] + '.' + parts[1] if '.' in model_name else parts[0]  # Handle AUDNZD.a format
                        timeframe = parts[1] if '.' not in model_name else parts[2]

                        # Get the model path
                        model_path = get_model_path(symbol, timeframe, model_type)

                        # Load the component model
                        success = self.load_model(model_name, model_path, model_type)
                    else:
                        logger.error(f"Invalid model name format: {model_name}")
                        return False

                    if success:
                        model = self.models.get(model_name)
                        if model is not None:
                            component_models.append(model)
                        else:
                            logger.error(f"Failed to load component model {model_name} into memory")
                            return False
                    else:
                        logger.error(f"Failed to load component model {model_name} from disk")
                        return False

            # Extract symbol and timeframe from ensemble_name (assuming format: symbol_timeframe_...)
            parts = ensemble_name.split('_')
            if len(parts) >= 2:
                symbol = parts[0]
                timeframe = parts[1]

                # Determine ensemble type (lstm_arima or tft_arima)
                if any('lstm' in name.lower() for name in model_names) and any('arima' in name.lower() for name in model_names):
                    ensemble_type = 'lstm_arima'
                elif any('tft' in name.lower() for name in model_names) and any('arima' in name.lower() for name in model_names):
                    ensemble_type = 'tft_arima'
                else:
                    ensemble_type = 'ensemble'

                # Get standardized model path following the required format
                # Terminal 4: model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima.pkl
                # Terminal 5: model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima.pkl
                model_path = get_model_path(symbol, timeframe, ensemble_type)
                logger.info(f"Using standardized path for ensemble model: {model_path}")
            else:
                # Fallback to old path if ensemble_name doesn't follow the expected format
                model_path = f"model/saved_models/ensemble/{ensemble_name}.pkl"
                logger.warning(f"Using fallback path for ensemble model: {model_path}")
                ensemble_type = 'ensemble'

            # Get model paths for the component models to create proper ensemble
            component_model_paths = []
            component_model_types = []

            for model_name in model_names:
                # Get the model path for each component
                model_parts = model_name.split('_')
                if len(model_parts) >= 3:
                    comp_symbol = model_parts[0] + '.' + model_parts[1] if '.' in model_name else model_parts[0]
                    comp_timeframe = model_parts[1] if '.' not in model_name else model_parts[2]
                    comp_type = model_parts[-1]  # Last part is the model type

                    comp_model_path = get_model_path(comp_symbol, comp_timeframe, comp_type)
                    component_model_paths.append(comp_model_path)
                    component_model_types.append(comp_type)
                else:
                    logger.error(f"Invalid component model name format: {model_name}")
                    return False

            # Create ensemble model with model paths (not objects) for proper serialization
            ensemble = EnsembleModel(
                model_paths=component_model_paths,
                model_types=component_model_types,
                weights=weights
            )

            # Store ensemble in memory
            self.models[ensemble_name] = ensemble
            self.models[f"{ensemble_name}_type"] = ensemble_type

            if ensemble.save_model(model_path):
                self.model_paths[ensemble_name] = model_path

                # Evaluate ensemble if possible
                # Find a common test set
                for model_name in model_names:
                    if hasattr(self.models[model_name], 'X_test') and hasattr(self.models[model_name], 'y_test'):
                        X_test = self.models[model_name].X_test
                        y_test = self.models[model_name].y_test

                        # Evaluate ensemble
                        metrics = ensemble.evaluate(X_test, y_test)

                        # Save metrics
                        if len(parts) >= 2:
                            # Save metrics following the required format
                            # Terminal 4: model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima_metrics.json
                            # Terminal 5: model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima_metrics.json
                            from utils.path_utils import get_metrics_path
                            metrics_path = get_metrics_path(symbol, timeframe, ensemble_type)
                            logger.info(f"Using standardized path for ensemble metrics: {metrics_path}")
                        else:
                            metrics_path = f"model/saved_models/ensemble/{ensemble_name}_metrics.json"
                            logger.warning(f"Using fallback path for ensemble metrics: {metrics_path}")

                        ensemble.save_metrics(metrics_path, metrics)
                        logger.info(f"Ensemble metrics saved to {metrics_path}")
                        break

                logger.info(f"Created and saved ensemble model: {ensemble_name}")
                return True
            else:
                logger.error(f"Failed to save ensemble model: {ensemble_name}")
                return False

        except Exception as e:
            logger.error(f"Error creating ensemble model {ensemble_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def list_models(self) -> List[str]:
        """
        List all available models.

        Returns:
            List[str]: List of model names
        """
        return [name for name in self.models.keys() if not name.endswith('_type')]

    def get_model_type(self, model_name: str) -> str:
        """
        Get the type of a model.

        Args:
            model_name: Name of the model

        Returns:
            str: Model type or empty string if not found
        """
        return self.models.get(f"{model_name}_type", "")

    def save_model(self, model_name: str, model_path: str = None, terminal_id: Optional[int] = None):
        """
        Save a model to disk using the standardized path structure.

        Args:
            model_name: Name of the model
            model_path: Path to save the model (optional)
            terminal_id: Terminal ID (optional)
        """
        # Get model
        model = self.models.get(model_name)
        if model is None:
            logger.error(f"Model {model_name} not found")
            return

        # If model_path is not provided, determine it based on model_type
        if model_path is None:
            # Get model type
            model_type = self.models.get(f"{model_name}_type")
            if model_type is None:
                logger.error(f"Model type not found for {model_name}")
                return

            # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe_...)
            parts = model_name.split('_')
            if len(parts) < 3:
                logger.error(f"Invalid model name format: {model_name}. Expected format: symbol_timeframe_type")
                # Fallback to old path
                model_path = os.path.join('model/saved_models', f"{model_name}.pth")
            else:
                # Handle AUDNZD.a format correctly
                if '.' in model_name:
                    # For names like "AUDNZD.a_M5_lstm"
                    symbol = parts[0] + '.' + parts[1]  # "AUDNZD.a"
                    timeframe = parts[2]  # "M5"
                else:
                    # For names like "EURUSD_M5_lstm"
                    symbol = parts[0]  # "EURUSD"
                    timeframe = parts[1]  # "M5"

                # Get standardized model path
                model_path = get_model_path(symbol, timeframe, model_type, terminal_id)

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(model_path)), exist_ok=True)

        # Save model
        model.save_model(model_path)
        self.model_paths[model_name] = model_path
        logger.info(f"Model {model_name} saved to {model_path}")

    def train_model_with_loaders(self, model_name: str, train_loader, val_loader, feature_names=None, epochs=100, patience=10) -> bool:
        """
        Train a model using data loaders for memory-efficient training.

        Args:
            model_name: Name of the model
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            feature_names: List of feature names (optional, for TFT models)
            epochs: Maximum number of epochs to train
            patience: Number of epochs to wait for improvement before early stopping

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get model
            model = self.models.get(model_name)
            if model is None:
                logger.error(f"Model {model_name} not found")
                return False

            # Check if the model is a ModelTrainer instance
            if hasattr(model, 'train_with_loaders'):
                # Use the model's train_with_loaders method
                history = model.train_with_loaders(
                    train_loader=train_loader,
                    val_loader=val_loader,
                    epochs=epochs,
                    patience=patience,
                    verbose=True
                )

                # Save metrics
                metrics = {
                    'train_loss': history['train_loss'][-1] if history['train_loss'] else None,
                    'val_loss': history['val_loss'][-1] if history['val_loss'] else None,
                    'epochs_trained': len(history['train_loss']),
                    'best_val_loss': min(history['val_loss']) if history['val_loss'] else None,
                    'feature_names': feature_names
                }
                self._save_metrics(model_name, metrics)

                # Save model
                model_path = self._get_model_path(model_name)
                model.save_model(model_path)
                logger.info(f"Model saved to {model_path}")

                return True
            else:
                # For models that don't have train_with_loaders method
                logger.error(f"Model {model_name} does not support training with data loaders")
                return False

        except Exception as e:
            logger.error(f"Error training model {model_name} with loaders: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _get_model_path(self, model_name: str) -> str:
        """
        Get the path to save the model.

        Args:
            model_name: Name of the model

        Returns:
            str: Path to save the model
        """
        # Extract model type from model name
        model_type = model_name.split('_')[-1]  # e.g., 'arima', 'lstm', 'tft'

        # Get the correct file extension based on model type
        from utils.path_utils import get_model_file_extension
        extension = get_model_file_extension(model_type)

        # Create directory if it doesn't exist
        model_dir = os.path.join('model', 'saved_models', model_type)
        os.makedirs(model_dir, exist_ok=True)

        # Return full path
        return os.path.join(model_dir, f"{model_name}.{extension}")

    def train_arima_model(self, model_name: str, symbol: str, timeframe: str) -> bool:
        """
        Train an ARIMA model for the given symbol and timeframe.
        Uses memory-efficient approach by processing data in chunks.

        Args:
            model_name: Name of the model
            symbol: Trading symbol
            timeframe: Timeframe to train on

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the path to the data file
            data_path = get_historical_data_path(symbol, timeframe, terminal_id=1, use_latest=True)

            if not os.path.exists(data_path):
                logger.error(f"No data file found at {data_path}")
                return False

            # Create ARIMA model
            if not self.create_model(model_name, 'arima'):
                logger.error(f"Failed to create ARIMA model: {model_name}")
                return False

            # Get model
            model = self.models.get(model_name)
            if model is None:
                logger.error(f"Model {model_name} not found")
                return False

            # Read data in chunks to reduce memory usage
            chunk_size = 10000  # Adjust based on available memory

            # Use pandas read_parquet with chunksize
            # Since read_parquet doesn't support chunksize directly, we'll use a workaround
            # First, get the total number of rows
            df_info = pd.read_parquet(data_path, columns=['close'])
            total_rows = len(df_info)
            del df_info  # Free memory

            # Calculate number of chunks
            num_chunks = (total_rows + chunk_size - 1) // chunk_size

            logger.info(f"Processing {total_rows} rows in {num_chunks} chunks of size {chunk_size}")

            # Process data in chunks
            for i in range(num_chunks):
                start_row = i * chunk_size
                end_row = min((i + 1) * chunk_size, total_rows)

                logger.info(f"Processing chunk {i+1}/{num_chunks} (rows {start_row} to {end_row})")

                # Read chunk
                df_chunk = pd.read_parquet(data_path, columns=['close'])
                df_chunk = df_chunk.iloc[start_row:end_row]

                # Extract close prices
                y_chunk = df_chunk['close'].values

                # Train or update model with chunk
                if i == 0:
                    # Initial training
                    success = model.fit(y_chunk)
                    if not success:
                        logger.error(f"Failed to train ARIMA model on first chunk: {model_name}")
                        return False
                else:
                    # Update model with new data
                    if not model.update(y_chunk):
                        logger.warning(f"Failed to update ARIMA model with chunk {i+1}: {model_name}")
                        # Continue anyway, as we've already trained on some data

                # Free memory
                del df_chunk

            # Save model
            model_path = self._get_model_path(model_name)
            model.save_model(model_path)

            # Save metrics
            metrics = {
                'model_type': 'arima',
                'symbol': symbol,
                'timeframe': timeframe,
                'total_rows': total_rows,
                'chunks_processed': num_chunks,
                'arima_order': model.get_params().get('order', None),
                'aic': model.get_aic() if hasattr(model, 'get_aic') else None
            }

            # Get standardized metrics path
            from utils.path_utils import get_metrics_path
            metrics_path = get_metrics_path(symbol, timeframe, 'arima')
            self._save_metrics(metrics_path, metrics)

            logger.info(f"Successfully trained ARIMA model: {model_name}")
            return True

        except Exception as e:
            logger.error(f"Error training ARIMA model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def train_lstm_model(self, model_name: str, symbol: str, timeframe: str) -> bool:
        """
        Train an LSTM model for the given symbol and timeframe.
        Uses batch processing to reduce memory usage.

        Args:
            model_name: Name of the model
            symbol: Trading symbol
            timeframe: Timeframe to train on

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create data manager
            data_manager = DataManager()

            # Load data for the symbol and timeframe from Terminal 2
            df = data_manager.load_historical_data(symbol, timeframe, terminal_id=2)

            if df is None or len(df) == 0:
                logger.error(f"No data found for {symbol} {timeframe} from Terminal 2")
                return False

            # Prepare data for LSTM model
            # Use technical indicators as features
            feature_cols = [
                'close', 'sma_5', 'sma_20', 'ema_5', 'ema_20',
                'rsi_14', 'macd_line', 'macd_signal', 'bb_upper', 'bb_lower'
            ]

            # Check if all feature columns exist
            missing_cols = [col for col in feature_cols if col not in df.columns]
            if missing_cols:
                logger.warning(f"Missing columns: {missing_cols}. Using available columns.")
                feature_cols = [col for col in feature_cols if col in df.columns]

            if not feature_cols:
                logger.error(f"No valid feature columns found in data")
                return False

            # Define sequence length
            sequence_length = 20  # Use 20 time steps for prediction

            # Create a custom dataset class for memory-efficient data loading
            class TimeSeriesDataset(Dataset):
                def __init__(self, dataframe, feature_columns, sequence_length, target_column='close', start_idx=0, end_idx=None):
                    self.df = dataframe
                    self.feature_cols = feature_columns
                    self.sequence_length = sequence_length
                    self.target_column = target_column
                    self.start_idx = start_idx
                    self.end_idx = end_idx if end_idx is not None else len(dataframe) - sequence_length

                def __len__(self):
                    return self.end_idx - self.start_idx

                def __getitem__(self, idx):
                    # Adjust index with start_idx offset
                    idx = idx + self.start_idx

                    # Get sequence
                    sequence = self.df[self.feature_cols].iloc[idx:idx+self.sequence_length].values

                    # Get target
                    target = self.df[self.target_column].iloc[idx+self.sequence_length]

                    return torch.tensor(sequence, dtype=torch.float32), torch.tensor(target, dtype=torch.float32)

            # Calculate split index for train/validation
            total_samples = len(df) - sequence_length
            split_idx = int(total_samples * 0.8) + sequence_length  # Add sequence_length to get the actual index in the dataframe

            # Create train and validation datasets
            train_dataset = TimeSeriesDataset(
                df,
                feature_cols,
                sequence_length,
                start_idx=0,
                end_idx=split_idx-sequence_length
            )

            val_dataset = TimeSeriesDataset(
                df,
                feature_cols,
                sequence_length,
                start_idx=split_idx-sequence_length,
                end_idx=None
            )

            # Create data loaders with appropriate batch sizes
            # Use smaller batch sizes to reduce memory usage
            train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

            logger.info(f"Created data loaders: {len(train_loader)} training batches, {len(val_loader)} validation batches")

            # Create LSTM model
            if not self.create_model(model_name, 'lstm', input_size=len(feature_cols), hidden_size=64, num_layers=2):
                logger.error(f"Failed to create LSTM model: {model_name}")
                return False

            # Train model with data loaders
            if not self.train_model_with_loaders(model_name, train_loader, val_loader, epochs=100, patience=10):
                logger.error(f"Failed to train LSTM model: {model_name}")
                return False

            logger.info(f"Successfully trained LSTM model: {model_name}")
            return True

        except Exception as e:
            logger.error(f"Error training LSTM model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def train_tft_model(self, model_name: str, symbol: str, timeframe: str) -> bool:
        """
        Train a TFT model for the given symbol and timeframe.
        Uses batch processing to reduce memory usage.

        Args:
            model_name: Name of the model
            symbol: Trading symbol
            timeframe: Timeframe to train on

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from data.data_manager import DataManager
            import torch
            from torch.utils.data import Dataset, DataLoader

            # Create data manager
            data_manager = DataManager()

            # Load data for the symbol and timeframe from Terminal 3
            df = data_manager.load_historical_data(symbol, timeframe, terminal_id=3)

            if df is None or len(df) == 0:
                logger.error(f"No data found for {symbol} {timeframe} from Terminal 3")
                return False

            # Prepare data for TFT model
            # Use technical indicators as features
            feature_cols = [
                'close', 'open', 'high', 'low', 'sma_5', 'sma_20', 'ema_5', 'ema_20',
                'rsi_14', 'macd_line', 'macd_signal', 'bb_upper', 'bb_lower', 'atr'
            ]

            # Check if all feature columns exist
            missing_cols = [col for col in feature_cols if col not in df.columns]
            if missing_cols:
                logger.warning(f"Missing columns: {missing_cols}. Using available columns.")
                feature_cols = [col for col in feature_cols if col in df.columns]

            if not feature_cols:
                logger.error(f"No valid feature columns found in data")
                return False

            # Define sequence length
            sequence_length = 30  # Use 30 time steps for prediction

            # Create a custom dataset class for memory-efficient data loading
            class TimeSeriesDataset(Dataset):
                def __init__(self, dataframe, feature_columns, sequence_length, target_column='close', start_idx=0, end_idx=None):
                    self.df = dataframe
                    self.feature_cols = feature_columns
                    self.sequence_length = sequence_length
                    self.target_column = target_column
                    self.start_idx = start_idx
                    self.end_idx = end_idx if end_idx is not None else len(dataframe) - sequence_length

                def __len__(self):
                    return self.end_idx - self.start_idx

                def __getitem__(self, idx):
                    # Adjust index with start_idx offset
                    idx = idx + self.start_idx

                    # Get sequence
                    sequence = self.df[self.feature_cols].iloc[idx:idx+self.sequence_length].values

                    # Get target
                    target = self.df[self.target_column].iloc[idx+self.sequence_length]

                    return torch.tensor(sequence, dtype=torch.float32), torch.tensor(target, dtype=torch.float32)

            # Calculate split index for train/validation
            total_samples = len(df) - sequence_length
            split_idx = int(total_samples * 0.8) + sequence_length  # Add sequence_length to get the actual index in the dataframe

            # Create train and validation datasets
            train_dataset = TimeSeriesDataset(
                df,
                feature_cols,
                sequence_length,
                start_idx=0,
                end_idx=split_idx-sequence_length
            )

            val_dataset = TimeSeriesDataset(
                df,
                feature_cols,
                sequence_length,
                start_idx=split_idx-sequence_length,
                end_idx=None
            )

            # Create data loaders with appropriate batch sizes
            # Use smaller batch sizes to reduce memory usage
            train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

            logger.info(f"Created data loaders: {len(train_loader)} training batches, {len(val_loader)} validation batches")

            # Convert data loaders to numpy arrays for TFT training
            # Collect all training data
            X_train_list = []
            y_train_list = []
            for batch_X, batch_y in train_loader:
                X_train_list.append(batch_X.numpy())
                y_train_list.append(batch_y.numpy())

            X_train = np.concatenate(X_train_list, axis=0)
            y_train = np.concatenate(y_train_list, axis=0)

            # Collect all validation data
            X_val_list = []
            y_val_list = []
            for batch_X, batch_y in val_loader:
                X_val_list.append(batch_X.numpy())
                y_val_list.append(batch_y.numpy())

            X_val = np.concatenate(X_val_list, axis=0)
            y_val = np.concatenate(y_val_list, axis=0)

            logger.info(f"Converted data loaders to arrays: X_train shape: {X_train.shape}, y_train shape: {y_train.shape}")
            logger.info(f"X_val shape: {X_val.shape}, y_val shape: {y_val.shape}")

            # Create TFT model
            if not self.create_model(model_name, 'tft', input_size=len(feature_cols), hidden_size=64, num_layers=2):
                logger.error(f"Failed to create TFT model: {model_name}")
                return False

            # Train model with numpy arrays
            if not self.train_model(model_name, X_train, y_train, X_val, y_val, feature_names=feature_cols, epochs=100, patience=10):
                logger.error(f"Failed to train TFT model: {model_name}")
                return False

            logger.info(f"Successfully trained TFT model: {model_name}")
            return True

        except Exception as e:
            logger.error(f"Error training TFT model {model_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def initialize_terminal_models(self, symbol: str, timeframes: List[str], terminals: Optional[List[int]] = None, force_retrain: bool = False) -> Dict[int, Dict[str, bool]]:
        """
        Initialize models for all terminals following the standardized terminal-model mapping:
        - Terminal 1: ARIMA
        - Terminal 2: LSTM
        - Terminal 3: TFT
        - Terminal 4: LSTM + ARIMA ensemble
        - Terminal 5: TFT + ARIMA ensemble

        Args:
            symbol: Trading symbol
            timeframes: List of timeframes to initialize
            terminals: List of terminal IDs to initialize (default: all terminals)
            force_retrain: Whether to force retraining of existing models

        Returns:
            Dict[int, Dict[str, bool]]: Dictionary mapping terminal IDs to dictionaries mapping timeframes to success status
        """
        if terminals is None:
            terminals = CentralConfig.get_all_terminal_ids()

        # Validate terminal IDs
        for terminal_id in terminals:
            if terminal_id not in CentralConfig.VALID_TERMINAL_IDS:
                logger.error(f"Invalid terminal ID: {terminal_id}. Valid terminal IDs are: {CentralConfig.VALID_TERMINAL_IDS}")
                return {}

        # Validate timeframes
        for timeframe in timeframes:
            if timeframe not in CentralConfig.VALID_TIMEFRAMES:
                logger.error(f"Invalid timeframe: {timeframe}. Valid timeframes are: {CentralConfig.VALID_TIMEFRAMES}")
                return {}

        results = {}

        # Process terminals in order to ensure dependencies are handled correctly
        # Process ARIMA first (Terminal 1), then LSTM (Terminal 2) and TFT (Terminal 3),
        # and finally the ensemble models (Terminals 4 and 5)
        ordered_terminals = sorted(terminals, key=lambda t: 10 if t > 3 else t)

        for terminal_id in ordered_terminals:
            model_type = CentralConfig.get_terminal_model_type(terminal_id)
            terminal_results = {}

            # Verify the terminal-model mapping is correct
            expected_model_type = CentralConfig.TERMINAL_MODEL_MAP.get(terminal_id)
            if model_type != expected_model_type:
                logger.error(f"Model type mismatch for Terminal {terminal_id}. Expected: {expected_model_type}, Got: {model_type}")
                logger.error("This indicates a configuration issue. Please check the TERMINAL_MODEL_TYPES mapping.")
                continue

            for timeframe in timeframes:
                logger.info(f"Initializing {model_type.upper()} model for Terminal {terminal_id}, Symbol {symbol}, Timeframe {timeframe}")

                # Construct model name
                model_name = f"{symbol}_{timeframe}_{model_type}"

                # Check if model already exists
                model_path = get_model_path(symbol, timeframe, model_type, terminal_id)
                metrics_path = get_metrics_path(symbol, timeframe, model_type, terminal_id)

                if os.path.exists(model_path) and os.path.exists(metrics_path) and not force_retrain:
                    logger.info(f"Model already exists at {model_path}. Skipping training.")
                    terminal_results[timeframe] = True
                    continue

                # Train model based on type
                if model_type == 'arima':
                    # Terminal 1: ARIMA
                    logger.info(f"Training ARIMA model for Terminal 1, Symbol {symbol}, Timeframe {timeframe}")
                    success = self.train_arima_model(model_name, symbol, timeframe)

                    # Verify the model was saved to the correct path
                    if success and not os.path.exists(model_path):
                        logger.error(f"Model was trained but not saved to the expected path: {model_path}")
                        success = False

                elif model_type == 'lstm':
                    # Terminal 2: LSTM
                    logger.info(f"Training LSTM model for Terminal 2, Symbol {symbol}, Timeframe {timeframe}")
                    success = self.train_lstm_model(model_name, symbol, timeframe)

                    # Verify the model was saved to the correct path
                    if success and not os.path.exists(model_path):
                        logger.error(f"Model was trained but not saved to the expected path: {model_path}")
                        success = False

                elif model_type == 'tft':
                    # Terminal 3: TFT
                    logger.info(f"Training TFT model for Terminal 3, Symbol {symbol}, Timeframe {timeframe}")
                    success = self.train_tft_model(model_name, symbol, timeframe)

                    # Verify the model was saved to the correct path
                    if success and not os.path.exists(model_path):
                        logger.error(f"Model was trained but not saved to the expected path: {model_path}")
                        success = False

                elif model_type == 'lstm_arima':
                    # Terminal 4: LSTM + ARIMA ensemble
                    logger.info(f"Training LSTM+ARIMA ensemble for Terminal 4, Symbol {symbol}, Timeframe {timeframe}")

                    # Check if component models exist, if not train them
                    lstm_model_path = get_model_path(symbol, timeframe, 'lstm')
                    arima_model_path = get_model_path(symbol, timeframe, 'arima')

                    lstm_exists = os.path.exists(lstm_model_path)
                    arima_exists = os.path.exists(arima_model_path)

                    # Train component models if they don't exist or force_retrain is True
                    if not lstm_exists or force_retrain:
                        logger.info(f"Training LSTM component for ensemble, Symbol {symbol}, Timeframe {timeframe}")
                        lstm_success = self.train_lstm_model(f"{symbol}_{timeframe}_lstm", symbol, timeframe)
                    else:
                        logger.info(f"LSTM component already exists at {lstm_model_path}")
                        # Load the existing LSTM model into memory
                        lstm_success = self.load_model(f"{symbol}_{timeframe}_lstm", lstm_model_path, 'lstm')

                    if not arima_exists or force_retrain:
                        logger.info(f"Training ARIMA component for ensemble, Symbol {symbol}, Timeframe {timeframe}")
                        arima_success = self.train_arima_model(f"{symbol}_{timeframe}_arima", symbol, timeframe)
                    else:
                        logger.info(f"ARIMA component already exists at {arima_model_path}")
                        # Load the existing ARIMA model into memory
                        arima_success = self.load_model(f"{symbol}_{timeframe}_arima", arima_model_path, 'arima')

                    if lstm_success and arima_success:
                        # Create ensemble
                        logger.info(f"Creating LSTM+ARIMA ensemble, Symbol {symbol}, Timeframe {timeframe}")
                        success = self.create_ensemble(
                            ensemble_name=model_name,
                            model_names=[f"{symbol}_{timeframe}_lstm", f"{symbol}_{timeframe}_arima"]
                        )

                        # Verify the ensemble model was saved to the correct path
                        if success and not os.path.exists(model_path):
                            logger.error(f"Ensemble model was created but not saved to the expected path: {model_path}")
                            success = False
                    else:
                        logger.error(f"Failed to train component models for ensemble. LSTM: {lstm_success}, ARIMA: {arima_success}")
                        success = False

                elif model_type == 'tft_arima':
                    # Terminal 5: TFT + ARIMA ensemble
                    logger.info(f"Training TFT+ARIMA ensemble for Terminal 5, Symbol {symbol}, Timeframe {timeframe}")

                    # Check if component models exist, if not train them
                    tft_model_path = get_model_path(symbol, timeframe, 'tft')
                    arima_model_path = get_model_path(symbol, timeframe, 'arima')

                    tft_exists = os.path.exists(tft_model_path)
                    arima_exists = os.path.exists(arima_model_path)

                    # Train component models if they don't exist or force_retrain is True
                    if not tft_exists or force_retrain:
                        logger.info(f"Training TFT component for ensemble, Symbol {symbol}, Timeframe {timeframe}")
                        tft_success = self.train_tft_model(f"{symbol}_{timeframe}_tft", symbol, timeframe)
                    else:
                        logger.info(f"TFT component already exists at {tft_model_path}")
                        # Load the existing TFT model into memory
                        tft_success = self.load_model(f"{symbol}_{timeframe}_tft", tft_model_path, 'tft')

                    if not arima_exists or force_retrain:
                        logger.info(f"Training ARIMA component for ensemble, Symbol {symbol}, Timeframe {timeframe}")
                        arima_success = self.train_arima_model(f"{symbol}_{timeframe}_arima", symbol, timeframe)
                    else:
                        logger.info(f"ARIMA component already exists at {arima_model_path}")
                        # Load the existing ARIMA model into memory
                        arima_success = self.load_model(f"{symbol}_{timeframe}_arima", arima_model_path, 'arima')

                    if tft_success and arima_success:
                        # Create ensemble
                        logger.info(f"Creating TFT+ARIMA ensemble, Symbol {symbol}, Timeframe {timeframe}")
                        success = self.create_ensemble(
                            ensemble_name=model_name,
                            model_names=[f"{symbol}_{timeframe}_tft", f"{symbol}_{timeframe}_arima"]
                        )

                        # Verify the ensemble model was saved to the correct path
                        if success and not os.path.exists(model_path):
                            logger.error(f"Ensemble model was created but not saved to the expected path: {model_path}")
                            success = False
                    else:
                        logger.error(f"Failed to train component models for ensemble. TFT: {tft_success}, ARIMA: {arima_success}")
                        success = False
                else:
                    logger.error(f"Unknown model type: {model_type}")
                    success = False

                terminal_results[timeframe] = success

                # Verify metrics file exists
                if success and not os.path.exists(metrics_path):
                    logger.warning(f"Model was trained but metrics file not found at expected path: {metrics_path}")

            results[terminal_id] = terminal_results

        return results
