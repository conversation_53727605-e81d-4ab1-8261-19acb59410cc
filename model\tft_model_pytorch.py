"""
Temporal Fusion Transformer (TFT) Model Module - PyTorch Implementation.
This module defines the TFT model architecture using PyTorch.

Reference: https://arxiv.org/abs/1912.09363
"""
import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from typing import Dict, List, Optional
from datetime import datetime

# Configure logger
logger = logging.getLogger('model.tft_model_pytorch')

class GatedResidualNetwork(nn.Module):
    """
    Gated Residual Network as described in the TFT paper.
    """
    def __init__(self, input_size, hidden_size, output_size, dropout_rate, use_layer_norm=True, use_residual=True):
        """
        Initialize the GRN.

        Args:
            input_size: Size of input
            hidden_size: Size of hidden layer
            output_size: Size of output
            dropout_rate: Dropout rate
            use_layer_norm: Whether to use layer normalization
            use_residual: Whether to use residual connections
        """
        super().__init__()
        self.input_size = input_size
        self.output_size = output_size
        self.hidden_size = hidden_size
        self.dropout_rate = dropout_rate
        self.use_layer_norm = use_layer_norm
        self.use_residual = use_residual

        # Layer normalization
        self.norm = nn.LayerNorm(input_size) if use_layer_norm else nn.Identity()

        # FC layers
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(dropout_rate)

        # Skip connection if input and output dimensions differ
        if use_residual:
            self.skip_connection = nn.Linear(input_size, output_size) if input_size != output_size else nn.Identity()
        else:
            self.skip_connection = None

        # Gating layer
        self.gate = nn.Linear(input_size, output_size)

    def forward(self, x):
        """Forward pass through the GRN."""
        # Normalize input
        normalized_x = self.norm(x)

        # Main branch
        hidden = F.elu(self.fc1(normalized_x))
        hidden = self.dropout(hidden)
        hidden = F.elu(self.fc2(hidden))
        hidden = self.dropout(hidden)

        # Gating mechanism
        gate = torch.sigmoid(self.gate(normalized_x))

        # Apply gating and optional residual connection
        if self.use_residual and self.skip_connection is not None:
            skip = self.skip_connection(x)
            output = gate * hidden + skip
        else:
            output = gate * hidden

        return output

class TemporalFusionTransformer(nn.Module):
    """
    Temporal Fusion Transformer model for time series forecasting.
    """
    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_attention_heads: int = 4,
        dropout_rate: float = 0.2,
        num_lstm_layers: int = 2,
        use_layer_norm: bool = True,
        use_residual_connections: bool = True
    ):
        """
        Initialize the Temporal Fusion Transformer model with enhanced architecture.

        Args:
            input_size: Number of input features
            hidden_size: Size of hidden layers
            num_attention_heads: Number of attention heads
            dropout_rate: Dropout rate
            num_lstm_layers: Number of LSTM layers
            use_layer_norm: Whether to use layer normalization
            use_residual_connections: Whether to use residual connections
        """
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.dropout_rate = dropout_rate
        self.num_lstm_layers = num_lstm_layers
        self.use_layer_norm = use_layer_norm
        self.use_residual_connections = use_residual_connections

        # Variable selection network
        self.variable_selection = GatedResidualNetwork(
            input_size=input_size,
            hidden_size=hidden_size,
            output_size=hidden_size,
            dropout_rate=dropout_rate,
            use_layer_norm=use_layer_norm,
            use_residual=use_residual_connections
        )

        # LSTM encoder with bidirectional option for better feature extraction
        self.lstm = nn.LSTM(
            input_size=hidden_size,
            hidden_size=hidden_size // 2 if use_residual_connections else hidden_size,  # Half size if bidirectional
            num_layers=num_lstm_layers,
            batch_first=True,
            bidirectional=use_residual_connections,  # Use bidirectional LSTM if residual connections are enabled
            dropout=dropout_rate if num_lstm_layers > 1 else 0
        )

        # Layer normalization before attention
        self.pre_attention_norm = nn.LayerNorm(hidden_size) if use_layer_norm else nn.Identity()

        # Multi-head attention with improved parameters
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_attention_heads,
            dropout=dropout_rate,
            batch_first=False  # Keep False for compatibility with older PyTorch versions
        )

        # Dropout after attention
        self.attention_dropout = nn.Dropout(dropout_rate)

        # Final gated residual network
        self.final_grn = GatedResidualNetwork(
            input_size=hidden_size,
            hidden_size=hidden_size,
            output_size=hidden_size,
            dropout_rate=dropout_rate,
            use_layer_norm=use_layer_norm,
            use_residual=use_residual_connections
        )

        # Output layer with additional hidden layer for better expressivity
        self.pre_output = nn.Linear(hidden_size, hidden_size // 2)
        self.output_activation = nn.ELU()
        self.output_layer = nn.Linear(hidden_size // 2, 1)

    def forward(self, x):
        """
        Forward pass through the enhanced TFT model.

        Args:
            x: Input tensor of shape [batch_size, sequence_length, input_size]

        Returns:
            Tensor: Output tensor of shape [batch_size, 1]
        """
        batch_size, seq_length, _ = x.shape

        # Variable selection with improved feature extraction
        var_selected = torch.zeros(batch_size, seq_length, self.hidden_size, device=x.device)
        for i in range(seq_length):
            var_selected[:, i, :] = self.variable_selection(x[:, i, :])

        # LSTM encoder (potentially bidirectional if residual connections are enabled)
        lstm_out, _ = self.lstm(var_selected)

        # Self-attention with layer normalization if enabled
        # First, normalize the LSTM output
        norm_lstm_out = self.pre_attention_norm(lstm_out)

        # Transpose for attention: [batch_size, seq_length, hidden] -> [seq_length, batch_size, hidden]
        norm_lstm_out_t = norm_lstm_out.transpose(0, 1)

        # Apply self-attention
        attn_out, _ = self.attention(
            query=norm_lstm_out_t,
            key=norm_lstm_out_t,
            value=norm_lstm_out_t
        )

        # Transpose back: [seq_length, batch_size, hidden] -> [batch_size, seq_length, hidden]
        attn_out = attn_out.transpose(0, 1)

        # Residual connection and dropout
        if self.use_residual_connections:
            attn_out = lstm_out + self.attention_dropout(attn_out)
        else:
            attn_out = self.attention_dropout(attn_out)

        # Final GRN on the last time step
        final_hidden = self.final_grn(attn_out[:, -1, :])

        # Enhanced output layer with additional hidden layer
        pre_output = self.pre_output(final_hidden)
        pre_output = self.output_activation(pre_output)
        output = self.output_layer(pre_output)

        return output

class TFTModel:
    """
    TFT model wrapper class with training and evaluation methods.
    This class provides an enhanced API with improved hyperparameters.
    """
    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_attention_heads: int = 4,
        dropout_rate: float = 0.2,
        learning_rate: float = 0.001,
        num_lstm_layers: int = 2,
        weight_decay: float = 1e-5,
        use_layer_norm: bool = True,
        use_residual_connections: bool = True,
        use_huber_loss: bool = True
    ):
        """
        Initialize the TFT model with optimized hyperparameters.

        Args:
            input_size: Number of input features
            hidden_size: Size of hidden layers
            num_attention_heads: Number of attention heads
            dropout_rate: Dropout rate
            learning_rate: Learning rate for optimizer
            num_lstm_layers: Number of LSTM layers
            weight_decay: L2 regularization factor
            use_layer_norm: Whether to use layer normalization
            use_residual_connections: Whether to use residual connections
            use_huber_loss: Whether to use Huber loss instead of MSE
        """
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.num_lstm_layers = num_lstm_layers
        self.weight_decay = weight_decay
        self.use_layer_norm = use_layer_norm
        self.use_residual_connections = use_residual_connections
        self.use_huber_loss = use_huber_loss

        # Set device with MPS support for Apple Silicon
        self.device = torch.device("mps" if torch.backends.mps.is_available() else
                                  "cuda" if torch.cuda.is_available() else
                                  "cpu")
        logger.info(f"Using device: {self.device}")

        # Create enhanced model
        self.model = TemporalFusionTransformer(
            input_size=input_size,
            hidden_size=hidden_size,
            num_attention_heads=num_attention_heads,
            dropout_rate=dropout_rate,
            num_lstm_layers=num_lstm_layers,
            use_layer_norm=use_layer_norm,
            use_residual_connections=use_residual_connections
        ).to(self.device)

        # Create optimizer with improved parameters
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        # Create loss function
        if use_huber_loss:
            self.criterion = nn.HuberLoss(delta=1.0)
        else:
            self.criterion = nn.MSELoss()

        # Create learning rate scheduler
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True,
            min_lr=1e-6
        )

        # Initialize history
        self.history = None

    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: np.ndarray,
        y_val: np.ndarray,
        epochs: int = 100,
        batch_size: int = 64,
        patience: int = 10,
        model_path: Optional[str] = None,
        use_amp: bool = True,
        num_workers: int = 4,
        pin_memory: bool = True,
        verbose: bool = True
    ) -> Dict[str, List[float]]:
        """
        Train the TFT model with optimized performance.

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            epochs: Number of epochs
            batch_size: Batch size
            patience: Patience for early stopping
            model_path: Path to save the best model
            use_amp: Whether to use Automatic Mixed Precision (faster training on compatible GPUs)
            num_workers: Number of workers for data loading (0 for single-process)
            pin_memory: Whether to pin memory for faster data transfer to GPU
            verbose: Whether to print progress

        Returns:
            Dict: Training history
        """
        # Determine if we should use AMP based on device compatibility
        use_amp = use_amp and (self.device.type == 'cuda')
        if use_amp:
            logger.info("Using Automatic Mixed Precision for faster training")
            # Use the updated API for PyTorch 2.0+
            try:
                scaler = torch.amp.GradScaler(device_type=self.device.type)
            except (TypeError, ValueError):
                # Fallback for older PyTorch versions
                try:
                    # Modern PyTorch API (2.0+)
                    scaler = torch.amp.GradScaler(device_type='cuda')
                except (TypeError, ValueError):
                    try:
                        # Newer PyTorch API (1.10+)
                        scaler = torch.amp.GradScaler('cuda')
                    except (TypeError, ValueError):
                        # Last resort fallback for older PyTorch versions
                        scaler = torch.amp.GradScaler()

        # Adjust num_workers based on system
        if self.device.type == 'cpu':
            # Reduce workers on CPU to avoid overhead
            num_workers = min(2, num_workers)
            pin_memory = False

        # Convert data to PyTorch tensors (keep training data on CPU until needed)
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
        y_train_tensor = torch.tensor(y_train, dtype=torch.float32)
        X_val_tensor = torch.tensor(X_val, dtype=torch.float32).to(self.device)
        y_val_tensor = torch.tensor(y_val, dtype=torch.float32).to(self.device)

        # Create data loaders with optimized settings
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory,
            prefetch_factor=2 if num_workers > 0 else None,
            persistent_workers=True if num_workers > 0 else False
        )

        # Initialize history
        history = {
            'loss': [],
            'val_loss': [],
            'mae': [],
            'val_mae': []
        }

        # Initialize early stopping variables
        best_val_loss = float('inf')
        early_stopping_counter = 0
        best_model_state = None

        # Determine validation frequency (less frequent for large datasets)
        val_freq = max(1, min(5, epochs // 20))  # Validate at most every 5 epochs for large datasets

        # Training loop
        logger.info(f"Training TFT model with {len(X_train)} samples for {epochs} epochs")
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0
            train_mae = 0.0

            # Use tqdm for progress tracking if verbose
            iterator = train_loader
            if verbose and epoch % 10 == 0:
                try:
                    from tqdm import tqdm
                    iterator = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
                except ImportError:
                    pass

            for batch_X, batch_y in iterator:
                # Move data to device
                batch_X = batch_X.to(self.device, non_blocking=True)
                batch_y = batch_y.to(self.device, non_blocking=True)

                # Forward pass with AMP if enabled
                if use_amp:
                    try:
                        # Use the updated API for PyTorch 2.0+
                        with torch.amp.autocast(device_type=self.device.type):
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))
                            mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))
                    except (TypeError, ValueError):
                        # Fallback for older PyTorch versions
                        try:
                            with torch.amp.autocast('cuda'):
                                outputs = self.model(batch_X)
                                loss = self.criterion(outputs, batch_y.unsqueeze(1))
                                mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))
                        except (TypeError, ValueError):
                            # Last resort fallback
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))
                            mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))

                    # Backward pass with gradient scaling
                    self.optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.step(self.optimizer)
                    scaler.update()
                else:
                    # Standard forward pass
                    self.optimizer.zero_grad()
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y.unsqueeze(1))
                    mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))

                    # Standard backward pass
                    loss.backward()
                    self.optimizer.step()

                # Calculate metrics
                train_loss += loss.item()
                train_mae += mae.item()

            # Calculate average metrics
            train_loss /= len(train_loader)
            train_mae /= len(train_loader)

            # Only validate periodically to save time
            if epoch % val_freq == 0 or epoch == epochs - 1:
                # Validation
                self.model.eval()
                with torch.no_grad():
                    val_outputs = self.model(X_val_tensor)
                    val_loss = self.criterion(val_outputs, y_val_tensor.unsqueeze(1)).item()
                    val_mae = torch.mean(torch.abs(val_outputs - y_val_tensor.unsqueeze(1))).item()

                # Save history
                history['loss'].append(train_loss)
                history['val_loss'].append(val_loss)
                history['mae'].append(train_mae)
                history['val_mae'].append(val_mae)

                # Print progress
                if verbose and (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}, "
                               f"MAE: {train_mae:.6f}, Val MAE: {val_mae:.6f}")

                # Check for early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    early_stopping_counter = 0
                    best_model_state = self.model.state_dict().copy()

                    # Save best model if path is provided
                    if model_path:
                        os.makedirs(os.path.dirname(model_path), exist_ok=True)
                        self.save_model(model_path)
                        logger.info(f"Saved best model to {model_path}")
                else:
                    early_stopping_counter += 1
                    if early_stopping_counter >= patience:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break
            else:
                # Just log training metrics when skipping validation
                history['loss'].append(train_loss)
                history['mae'].append(train_mae)
                history['val_loss'].append(None)  # Will be interpolated later
                history['val_mae'].append(None)  # Will be interpolated later

                if verbose and (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {train_loss:.6f}, MAE: {train_mae:.6f}")

        # Fill in missing validation values with interpolation
        for metric in ['val_loss', 'val_mae']:
            if None in history[metric]:
                # Get indices of non-None values
                valid_indices = [i for i, x in enumerate(history[metric]) if x is not None]

                # Interpolate missing values
                for i in range(len(history[metric])):
                    if history[metric][i] is None:
                        # Find nearest valid indices
                        left_idx = max([idx for idx in valid_indices if idx < i], default=valid_indices[0])
                        right_idx = min([idx for idx in valid_indices if idx > i], default=valid_indices[-1])

                        # Linear interpolation
                        left_val = history[metric][left_idx]
                        right_val = history[metric][right_idx]

                        if left_idx == right_idx:
                            history[metric][i] = left_val
                        else:
                            weight = (i - left_idx) / (right_idx - left_idx)
                            history[metric][i] = left_val + weight * (right_val - left_val)

        # Load best model
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        # Save history
        self.history = history

        logger.info(f"Finished training with final loss: {history['loss'][-1]:.6f}, "
                   f"val_loss: {history['val_loss'][-1]:.6f}")

        return history

    def update_model(
        self,
        X: np.ndarray,
        y: np.ndarray,
        batch_size: int = 32,
        epochs: int = 10,
        use_amp: bool = True,
        num_workers: int = 2,
        pin_memory: bool = True
    ):
        """
        Update the model with new data (online learning) with optimized performance.

        Args:
            X: Input features
            y: Target values
            batch_size: Batch size
            epochs: Number of epochs
            use_amp: Whether to use Automatic Mixed Precision (faster training on compatible GPUs)
            num_workers: Number of workers for data loading (0 for single-process)
            pin_memory: Whether to pin memory for faster data transfer to GPU
        """
        # Determine if we should use AMP based on device compatibility
        use_amp = use_amp and (self.device.type == 'cuda')
        if use_amp:
            # Use the updated API for PyTorch 2.0+
            try:
                scaler = torch.amp.GradScaler(device_type=self.device.type)
            except (TypeError, ValueError):
                # Fallback for older PyTorch versions
                try:
                    # Modern PyTorch API (2.0+)
                    scaler = torch.amp.GradScaler(device_type='cuda')
                except (TypeError, ValueError):
                    try:
                        # Newer PyTorch API (1.10+)
                        scaler = torch.amp.GradScaler('cuda')
                    except (TypeError, ValueError):
                        # Last resort fallback for older PyTorch versions
                        scaler = torch.amp.GradScaler()

        # Adjust num_workers based on system
        if self.device.type == 'cpu':
            # Reduce workers on CPU to avoid overhead
            num_workers = min(1, num_workers)
            pin_memory = False

        # For small updates, don't use multiple workers
        if len(X) < 1000:
            num_workers = 0
            pin_memory = False

        # Convert data to PyTorch tensors (keep on CPU until needed)
        X_tensor = torch.tensor(X, dtype=torch.float32)
        y_tensor = torch.tensor(y, dtype=torch.float32)

        # Create data loader with optimized settings
        dataset = TensorDataset(X_tensor, y_tensor)
        loader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory,
            prefetch_factor=2 if num_workers > 0 else None,
            persistent_workers=True if num_workers > 0 and len(X) > 5000 else False
        )

        # Training loop
        self.model.train()
        for epoch in range(epochs):
            train_loss = 0.0
            train_mae = 0.0

            for batch_X, batch_y in loader:
                # Move data to device
                batch_X = batch_X.to(self.device, non_blocking=True)
                batch_y = batch_y.to(self.device, non_blocking=True)

                # Forward pass with AMP if enabled
                if use_amp:
                    try:
                        # Use the updated API for PyTorch 2.0+
                        with torch.amp.autocast(device_type=self.device.type):
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))
                            mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))
                    except (TypeError, ValueError):
                        # Fallback for older PyTorch versions
                        try:
                            with torch.amp.autocast('cuda'):
                                outputs = self.model(batch_X)
                                loss = self.criterion(outputs, batch_y.unsqueeze(1))
                                mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))
                        except (TypeError, ValueError):
                            # Last resort fallback
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))
                            mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))

                    # Backward pass with gradient scaling
                    self.optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.step(self.optimizer)
                    scaler.update()
                else:
                    # Standard forward pass
                    self.optimizer.zero_grad()
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y.unsqueeze(1))
                    mae = torch.mean(torch.abs(outputs - batch_y.unsqueeze(1)))

                    # Standard backward pass
                    loss.backward()
                    self.optimizer.step()

                # Calculate metrics
                train_loss += loss.item()
                train_mae += mae.item()

            # Calculate average metrics
            train_loss /= len(loader)
            train_mae /= len(loader)

            # Update history if it exists
            if self.history is not None:
                if 'loss' in self.history:
                    self.history['loss'].append(train_loss)
                if 'mae' in self.history:
                    self.history['mae'].append(train_mae)

        logger.info(f"Model updated with {len(X)} samples, Final Loss: {train_loss:.6f}, MAE: {train_mae:.6f}")

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the model.

        Args:
            X: Input features

        Returns:
            np.ndarray: Predictions
        """
        # Convert to PyTorch tensor
        X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)

        # Set model to evaluation mode
        self.model.eval()

        # Make predictions
        with torch.no_grad():
            predictions = self.model(X_tensor)

        # Convert to numpy array
        predictions_np = predictions.cpu().numpy()

        return predictions_np

    def evaluate(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray,
        feature_names: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Evaluate the model on test data.

        Args:
            X_test: Test features
            y_test: Test targets
            feature_names: Names of features for feature importance

        Returns:
            Dict: Evaluation metrics
        """
        # Make predictions
        y_pred = self.predict(X_test).flatten()

        # Calculate metrics
        mse = np.mean((y_pred - y_test) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_pred - y_test))

        # Calculate R-squared
        ss_total = np.sum((y_test - np.mean(y_test)) ** 2)
        ss_residual = np.sum((y_test - y_pred) ** 2)
        r2 = 1 - (ss_residual / ss_total) if ss_total > 0 else 0

        # Calculate direction accuracy
        direction_accuracy = np.mean((np.sign(y_pred) == np.sign(y_test)).astype(int))

        # Calculate profit factor
        gains = np.sum(y_pred[y_pred > 0])
        losses = np.abs(np.sum(y_pred[y_pred < 0]))
        profit_factor = gains / losses if losses > 0 else float('inf')

        # Calculate Sharpe ratio (simplified)
        returns = y_pred
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0

        metrics = {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'direction_accuracy': direction_accuracy,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio
        }

        logger.info(f"Evaluation metrics: MSE={mse:.6f}, RMSE={rmse:.6f}, MAE={mae:.6f}, "
                   f"R²={r2:.6f}, Direction Accuracy={direction_accuracy:.6f}")

        return metrics

    def save_model(self, path: str) -> bool:
        """
        Save the model to disk.

        Args:
            path: Path to save the model

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)

            # Prepare model state
            model_state = {
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'input_size': self.input_size,
                'hidden_size': self.hidden_size,
                'num_attention_heads': self.num_attention_heads,
                'dropout_rate': self.dropout_rate,
                'num_lstm_layers': self.num_lstm_layers,
                'history': self.history,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Save model
            torch.save(model_state, path)
            logger.info(f"Model saved to {path}")
            return True
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def load_model(self, path: str) -> bool:
        """
        Load the model from disk.

        Args:
            path: Path to load the model from

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(path):
                logger.error(f"Model file {path} not found")
                return False

            # Load model state
            checkpoint = torch.load(path, map_location=self.device)

            # Update model parameters
            self.input_size = checkpoint.get('input_size', self.input_size)
            self.hidden_size = checkpoint.get('hidden_size', self.hidden_size)
            self.num_attention_heads = checkpoint.get('num_attention_heads', self.num_attention_heads)
            self.dropout_rate = checkpoint.get('dropout_rate', self.dropout_rate)
            self.num_lstm_layers = checkpoint.get('num_lstm_layers', self.num_lstm_layers)

            # Recreate model with loaded parameters
            self.model = TemporalFusionTransformer(
                input_size=self.input_size,
                hidden_size=self.hidden_size,
                num_attention_heads=self.num_attention_heads,
                dropout_rate=self.dropout_rate,
                num_lstm_layers=self.num_lstm_layers
            ).to(self.device)

            # Load model state dict
            self.model.load_state_dict(checkpoint['model_state_dict'])

            # Load optimizer state dict if available
            if 'optimizer_state_dict' in checkpoint:
                self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

            # Load history if available
            if 'history' in checkpoint:
                self.history = checkpoint['history']

            logger.info(f"Model loaded from {path}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def plot_training_history(self, save_path: Optional[str] = None):
        """
        Plot the training history.

        Args:
            save_path: Path to save the plot
        """
        if self.history is None:
            logger.error("No training history to plot")
            return

        plt.figure(figsize=(12, 5))

        # Plot loss
        plt.subplot(1, 2, 1)
        plt.plot(self.history['loss'], label='Training Loss')
        plt.plot(self.history['val_loss'], label='Validation Loss')
        plt.title('Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)

        # Plot MAE
        plt.subplot(1, 2, 2)
        plt.plot(self.history['mae'], label='Training MAE')
        plt.plot(self.history['val_mae'], label='Validation MAE')
        plt.title('Model MAE')
        plt.xlabel('Epoch')
        plt.ylabel('MAE')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()

        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path)
            logger.info(f"Training history plot saved to {save_path}")

        plt.close()
