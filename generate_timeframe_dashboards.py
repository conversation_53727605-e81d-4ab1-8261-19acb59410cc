"""
Script to generate timeframe-specific dashboards.
This script generates dashboards for each timeframe.
"""
import logging
import time
import os
import sys
import argparse
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap

from trading.mt5_connector import MT5Connector
from trading.terminal_manager import TerminalManager
from model.model_manager import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator
from config.trading_config import TRADING_CONFIG
from config.credentials import MT5_TERMINALS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/generate_dashboards.log')
    ]
)
logger = logging.getLogger('generate_dashboards')

def generate_model_visualizations(model_name, output_dir):
    """
    Generate visualizations for a model.
    
    Args:
        model_name: Name of the model
        output_dir: Directory to save visualizations
    
    Returns:
        dict: Dictionary of visualization paths
    """
    logger.info(f"Generating visualizations for model {model_name}...")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize model manager
    model_manager = ModelManager()
    
    # Load model
    if not model_manager.load_model(model_name):
        logger.error(f"Failed to load model {model_name}")
        return {}
    
    # Get model
    model = model_manager.get_model(model_name)
    if model is None:
        logger.error(f"Failed to get model {model_name}")
        return {}
    
    # Generate visualizations
    visualizations = {}
    
    # 1. Training history
    if model.history.get('train_loss') and model.history.get('val_loss'):
        try:
            # Create figure
            plt.figure(figsize=(12, 6))
            
            # Plot training and validation loss
            plt.plot(model.history['train_loss'], label='Training Loss')
            plt.plot(model.history['val_loss'], label='Validation Loss')
            
            plt.title(f"{model_name} - Training History")
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.grid(True)
            
            # Save figure
            history_path = os.path.join(output_dir, f"{model_name}_training_history.png")
            plt.savefig(history_path)
            plt.close()
            
            visualizations['training_history'] = history_path
            logger.info(f"Generated training history visualization: {history_path}")
        except Exception as e:
            logger.error(f"Error generating training history visualization: {str(e)}")
    
    # 2. Model architecture
    try:
        # Create figure
        plt.figure(figsize=(10, 6))
        
        # Create a table of hyperparameters
        hyperparams = model.hyperparams
        cell_text = []
        for key, value in hyperparams.items():
            cell_text.append([key, str(value)])
        
        plt.axis('off')
        plt.table(cellText=cell_text, colLabels=['Parameter', 'Value'], 
                 loc='center', cellLoc='center')
        
        plt.title(f"{model_name} - Model Architecture")
        
        # Save figure
        architecture_path = os.path.join(output_dir, f"{model_name}_architecture.png")
        plt.savefig(architecture_path)
        plt.close()
        
        visualizations['architecture'] = architecture_path
        logger.info(f"Generated model architecture visualization: {architecture_path}")
    except Exception as e:
        logger.error(f"Error generating model architecture visualization: {str(e)}")
    
    return visualizations

def generate_timeframe_dashboard(timeframe, output_dir):
    """
    Generate a dashboard for a specific timeframe.
    
    Args:
        timeframe: Timeframe to generate dashboard for
        output_dir: Directory to save dashboard
    
    Returns:
        str: Path to the dashboard HTML file
    """
    logger.info(f"Generating dashboard for timeframe {timeframe}...")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get model name
    symbol = TRADING_CONFIG['symbol']
    model_name = f"{symbol}_{timeframe}"
    
    # Generate model visualizations
    model_dir = os.path.join(output_dir, 'model')
    os.makedirs(model_dir, exist_ok=True)
    visualizations = generate_model_visualizations(model_name, model_dir)
    
    # Find terminal ID for this timeframe
    terminal_id = None
    terminal_name = None
    risk_profile = None
    trading_style = None
    
    for t_id, terminal in MT5_TERMINALS.items():
        if terminal.get('timeframe') == timeframe:
            terminal_id = t_id
            terminal_name = terminal.get('name')
            risk_profile = terminal.get('risk_profile')
            trading_style = terminal.get('trading_style')
            break
    
    if terminal_id is None:
        logger.warning(f"No terminal found for timeframe {timeframe}")
        terminal_id = "N/A"
        terminal_name = "N/A"
        risk_profile = "N/A"
        trading_style = "N/A"
    
    # Create HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{timeframe} Timeframe Dashboard</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            .header {{
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                margin-bottom: 20px;
            }}
            .section {{
                margin-bottom: 30px;
                padding: 20px;
                background-color: #f9f9f9;
                border-radius: 5px;
            }}
            .info-box {{
                display: inline-block;
                width: 45%;
                margin: 10px;
                padding: 15px;
                background-color: #e9f7ef;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }}
            th, td {{
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            th {{
                background-color: #4CAF50;
                color: white;
            }}
            tr:hover {{
                background-color: #f5f5f5;
            }}
            .visualization {{
                margin: 20px 0;
                text-align: center;
            }}
            img {{
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                text-align: center;
                color: #777;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{timeframe} Timeframe Dashboard</h1>
                <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Terminal Information</h2>
                <div class="info-box">
                    <h3>Terminal Details</h3>
                    <table>
                        <tr>
                            <th>Parameter</th>
                            <th>Value</th>
                        </tr>
                        <tr>
                            <td>Terminal ID</td>
                            <td>{terminal_id}</td>
                        </tr>
                        <tr>
                            <td>Terminal Name</td>
                            <td>{terminal_name}</td>
                        </tr>
                        <tr>
                            <td>Timeframe</td>
                            <td>{timeframe}</td>
                        </tr>
                        <tr>
                            <td>Risk Profile</td>
                            <td>{risk_profile}</td>
                        </tr>
                        <tr>
                            <td>Trading Style</td>
                            <td>{trading_style}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="info-box">
                    <h3>Risk Parameters</h3>
                    <table>
                        <tr>
                            <th>Parameter</th>
                            <th>Value</th>
                        </tr>
    """
    
    # Add risk parameters if available
    if terminal_id != "N/A":
        terminal = MT5_TERMINALS[terminal_id]
        for param, value in terminal.items():
            if param in ['position_size_factor', 'sl_multiplier', 'tp_multiplier', 'trade_duration']:
                html_content += f"""
                        <tr>
                            <td>{param}</td>
                            <td>{value}</td>
                        </tr>
                """
    
    html_content += f"""
                    </table>
                </div>
            </div>
            
            <div class="section">
                <h2>Model Information</h2>
                <p>Model Name: {model_name}</p>
    """
    
    # Add model visualizations if available
    if 'training_history' in visualizations:
        html_content += f"""
                <div class="visualization">
                    <h3>Training History</h3>
                    <img src="{os.path.relpath(visualizations['training_history'], output_dir)}" alt="Training History">
                </div>
        """
    
    if 'architecture' in visualizations:
        html_content += f"""
                <div class="visualization">
                    <h3>Model Architecture</h3>
                    <img src="{os.path.relpath(visualizations['architecture'], output_dir)}" alt="Model Architecture">
                </div>
        """
    
    html_content += f"""
            </div>
            
            <div class="section">
                <h2>Trading Strategy</h2>
                <p>The trading strategy for the {timeframe} timeframe is designed to capture market movements appropriate for this time scale.</p>
                
                <h3>Signal Generation</h3>
                <p>Signals are generated based on the LSTM model predictions for the {timeframe} timeframe.</p>
                
                <h3>Entry Criteria</h3>
                <ul>
                    <li>Long Entry: Signal > {TRADING_CONFIG['terminal_config'].get(terminal_id, {}).get('signal_threshold', 0.3)}</li>
                    <li>Short Entry: Signal < -{TRADING_CONFIG['terminal_config'].get(terminal_id, {}).get('signal_threshold', 0.3)}</li>
                </ul>
                
                <h3>Exit Criteria</h3>
                <ul>
                    <li>Take Profit: {terminal.get('tp_multiplier', 2.0)}x ATR</li>
                    <li>Stop Loss: {terminal.get('sl_multiplier', 1.0)}x ATR</li>
                    <li>Reversal Exit: Signal crosses {TRADING_CONFIG['terminal_config'].get(terminal_id, {}).get('signal_reversal', 0.7)} threshold in opposite direction</li>
                </ul>
            </div>
            
            <div class="footer">
                <p>BTCUSD Trading Bot - {timeframe} Timeframe Dashboard</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Save HTML file
    dashboard_path = os.path.join(output_dir, f"{timeframe}_dashboard.html")
    with open(dashboard_path, 'w') as f:
        f.write(html_content)
    
    logger.info(f"Generated dashboard for timeframe {timeframe}: {dashboard_path}")
    return dashboard_path

def generate_main_dashboard(timeframes, output_dir):
    """
    Generate a main dashboard that links to all timeframe dashboards.
    
    Args:
        timeframes: List of timeframes
        output_dir: Directory to save dashboard
    
    Returns:
        str: Path to the main dashboard HTML file
    """
    logger.info("Generating main dashboard...")
    
    # Create HTML content
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Multi-Timeframe Trading Dashboard</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }
            h1, h2 {
                color: #333;
            }
            .header {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                margin-bottom: 20px;
                text-align: center;
            }
            .timeframe-cards {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-around;
            }
            .card {
                width: 30%;
                margin: 10px;
                padding: 20px;
                background-color: #f9f9f9;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
            }
            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }
            .card h2 {
                color: #4CAF50;
                margin-top: 0;
            }
            .card p {
                color: #666;
            }
            .card a {
                display: inline-block;
                margin-top: 10px;
                padding: 8px 16px;
                background-color: #4CAF50;
                color: white;
                text-decoration: none;
                border-radius: 4px;
            }
            .card a:hover {
                background-color: #45a049;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                text-align: center;
                color: #777;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Multi-Timeframe Trading Dashboard</h1>
                <p>Generated on """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
            </div>
            
            <div class="timeframe-cards">
    """
    
    # Add cards for each timeframe
    for timeframe in timeframes:
        # Find terminal ID for this timeframe
        terminal_id = None
        terminal_name = None
        risk_profile = None
        trading_style = None
        
        for t_id, terminal in MT5_TERMINALS.items():
            if terminal.get('timeframe') == timeframe:
                terminal_id = t_id
                terminal_name = terminal.get('name')
                risk_profile = terminal.get('risk_profile')
                trading_style = terminal.get('trading_style')
                break
        
        if terminal_id is None:
            continue
        
        html_content += f"""
                <div class="card">
                    <h2>{timeframe} Timeframe</h2>
                    <p><strong>Terminal:</strong> {terminal_name}</p>
                    <p><strong>Risk Profile:</strong> {risk_profile}</p>
                    <p><strong>Trading Style:</strong> {trading_style}</p>
                    <a href="{timeframe}/{timeframe}_dashboard.html">View Dashboard</a>
                </div>
        """
    
    # Add card for multi-timeframe
    html_content += """
                <div class="card">
                    <h2>Multi-Timeframe</h2>
                    <p><strong>Terminal:</strong> IC Markets Demo 3</p>
                    <p><strong>Risk Profile:</strong> Dynamic</p>
                    <p><strong>Trading Style:</strong> Confirmation-based, high-probability setups</p>
                    <a href="MULTI/MULTI_dashboard.html">View Dashboard</a>
                </div>
            </div>
            
            <div class="footer">
                <p>BTCUSD Trading Bot - Multi-Timeframe Trading Dashboard</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Save HTML file
    dashboard_path = os.path.join(output_dir, "index.html")
    with open(dashboard_path, 'w') as f:
        f.write(html_content)
    
    logger.info(f"Generated main dashboard: {dashboard_path}")
    return dashboard_path

def generate_dashboards(output_dir):
    """
    Generate dashboards for all timeframes.
    
    Args:
        output_dir: Directory to save dashboards
    """
    logger.info(f"Generating dashboards in {output_dir}...")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get timeframes
    timeframes = list(TRADING_CONFIG['timeframes'].keys())
    
    # Generate dashboards for each timeframe
    for timeframe in timeframes:
        timeframe_dir = os.path.join(output_dir, timeframe)
        generate_timeframe_dashboard(timeframe, timeframe_dir)
    
    # Generate dashboard for multi-timeframe
    multi_dir = os.path.join(output_dir, "MULTI")
    generate_timeframe_dashboard("MULTI", multi_dir)
    
    # Generate main dashboard
    generate_main_dashboard(timeframes, output_dir)
    
    logger.info("Dashboard generation completed")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Generate timeframe-specific dashboards')
    parser.add_argument('--output-dir', type=str, default='dashboards', help='Directory to save dashboards')
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Generate dashboards
    generate_dashboards(args.output_dir)

if __name__ == "__main__":
    main()
