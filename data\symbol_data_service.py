"""
Symbol Data Service Module.
This module handles data collection and validation for symbols.
"""
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple
import pandas as pd
import numpy as np
from config.trading_config import TRADING_CONFIG
from trading.mt5_connector import MT5Connector
from data.data_collector import DataCollector

# Configure logger
logger = logging.getLogger('data.symbol_data_service')

class SymbolDataService:
    """
    Symbol Data Service class for handling data collection and validation for symbols.
    """
    def __init__(self, mt5_connector: MT5Connector, data_collector: DataCollector):
        """
        Initialize SymbolDataService.

        Args:
            mt5_connector: MT5Connector instance
            data_collector: DataCollector instance
        """
        self.mt5 = mt5_connector
        self.data_collector = data_collector
        self.data_dir = 'data/historical'
        self.timeframes = TRADING_CONFIG.get('timeframes', ['M5', 'M15', 'M30'])

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        # Create subdirectories for each timeframe
        for tf in self.timeframes:
            os.makedirs(os.path.join(self.data_dir, tf), exist_ok=True)

    def collect_historical_data(
        self,
        symbol: str,
        years: int = 5,
        terminal_ids: Optional[List[int]] = None
    ) -> Dict[str, Dict[int, pd.DataFrame]]:
        """
        Collect historical data for a symbol from all terminals.

        Args:
            symbol: Symbol to collect data for
            years: Number of years of historical data to collect
            terminal_ids: List of terminal IDs to collect from (None for all)

        Returns:
            Dict[str, Dict[int, pd.DataFrame]]: Dictionary of timeframe -> terminal_id -> data
        """
        try:
            # Calculate start date
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365 * years)

            # Format dates
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            logger.info(f"Collecting {years} years of historical data for {symbol} from {start_date_str} to {end_date_str}")

            # Get terminal IDs
            if terminal_ids is None:
                terminal_ids = list(self.mt5.connected_terminals.keys())

            # Collect data for each timeframe and terminal
            results = {}

            for tf in self.timeframes:
                results[tf] = {}

                for terminal_id in terminal_ids:
                    logger.info(f"Collecting {symbol} {tf} data from Terminal {terminal_id}...")

                    # Connect to terminal
                    if not self.mt5.connect_terminal(terminal_id):
                        logger.warning(f"Failed to connect to Terminal {terminal_id}, skipping...")
                        continue

                    # Get historical data directly from MT5 (bypass storage)
                    df = self.mt5.get_historical_data(
                        symbol=symbol,
                        timeframe=tf,
                        start_date=start_date_str,
                        end_date=end_date_str
                    )

                    if df is not None and len(df) > 0:
                        logger.info(f"Collected {len(df)} bars of {symbol} {tf} data from Terminal {terminal_id}")
                        results[tf][terminal_id] = df
                    else:
                        logger.warning(f"Failed to collect {symbol} {tf} data from Terminal {terminal_id}")

            # Save data to Parquet files
            self._save_data_to_parquet(symbol, results)

            return results

        except Exception as e:
            logger.error(f"Error collecting historical data for {symbol}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def _save_data_to_parquet(
        self,
        symbol: str,
        data: Dict[str, Dict[int, pd.DataFrame]]
    ):
        """
        Save data to Parquet files.

        Args:
            symbol: Symbol
            data: Dictionary of timeframe -> terminal_id -> data
        """
        try:
            for tf, terminal_data in data.items():
                for terminal_id, df in terminal_data.items():
                    # Create directory for symbol if it doesn't exist
                    symbol_dir = os.path.join(self.data_dir, tf, symbol)
                    os.makedirs(symbol_dir, exist_ok=True)

                    # Save to Parquet file
                    file_path = os.path.join(symbol_dir, f"terminal_{terminal_id}.parquet")
                    df.to_parquet(file_path)
                    logger.info(f"Saved {symbol} {tf} data from Terminal {terminal_id} to {file_path}")

        except Exception as e:
            logger.error(f"Error saving data to Parquet: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def validate_data(
        self,
        symbol: str,
        timeframes: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, float]]:
        """
        Validate data quality for a symbol.

        Args:
            symbol: Symbol to validate
            timeframes: List of timeframes to validate (None for all)

        Returns:
            Dict[str, Dict[str, float]]: Dictionary of timeframe -> metric -> value
        """
        try:
            # Use all timeframes if not specified
            if timeframes is None:
                timeframes = self.timeframes

            results = {}

            for tf in timeframes:
                # Get all terminal data for this timeframe
                symbol_dir = os.path.join(self.data_dir, tf, symbol)
                if not os.path.exists(symbol_dir):
                    logger.warning(f"No data directory found for {symbol} {tf}")
                    continue

                # Load data from all terminals
                terminal_data = {}
                for file in os.listdir(symbol_dir):
                    if file.endswith('.parquet'):
                        file_path = os.path.join(symbol_dir, file)
                        terminal_id = int(file.split('_')[1].split('.')[0])
                        terminal_data[terminal_id] = pd.read_parquet(file_path)

                if not terminal_data:
                    logger.warning(f"No data found for {symbol} {tf}")
                    continue

                # Validate data
                validation_results = self._validate_terminal_data(terminal_data)
                results[tf] = validation_results

                # Log validation results
                logger.info(f"Validation results for {symbol} {tf}:")
                for metric, value in validation_results.items():
                    logger.info(f"  {metric}: {value}")

            return results

        except Exception as e:
            logger.error(f"Error validating data for {symbol}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def _validate_terminal_data(
        self,
        terminal_data: Dict[int, pd.DataFrame]
    ) -> Dict[str, float]:
        """
        Validate data from multiple terminals.

        Args:
            terminal_data: Dictionary of terminal_id -> data

        Returns:
            Dict[str, float]: Dictionary of metric -> value
        """
        # Initialize results
        results = {
            'completeness': 0.0,
            'consistency': 0.0,
            'terminal_coverage': 0.0,
            'gaps_percentage': 0.0,
            'outliers_percentage': 0.0
        }

        # Check terminal coverage
        results['terminal_coverage'] = len(terminal_data) / 5  # Assuming 5 terminals

        # If no data, return empty results
        if not terminal_data:
            return results

        # Combine data from all terminals
        all_data = []
        for terminal_id, df in terminal_data.items():
            df = df.copy()
            df['terminal_id'] = terminal_id
            all_data.append(df)

        combined_df = pd.concat(all_data)
        combined_df = combined_df.sort_index()

        # Check completeness (percentage of expected data points that are present)
        # For daily data, we expect about 252 trading days per year
        # For hourly data, we expect about 24*5*52 = 6240 hours per year
        # For minute data, we need to be more sophisticated

        # For now, let's use a simple approach based on the timeframe
        timeframe = combined_df.index.freq
        if timeframe is None:
            # Try to infer timeframe from the data
            if len(combined_df) > 1:
                median_diff = np.median(np.diff(combined_df.index.astype(np.int64)) / 1e9 / 60)
                if 4 <= median_diff <= 6:
                    timeframe = 'M5'
                elif 14 <= median_diff <= 16:
                    timeframe = 'M15'
                elif 29 <= median_diff <= 31:
                    timeframe = 'M30'
                else:
                    timeframe = 'unknown'
            else:
                timeframe = 'unknown'

        # Calculate expected number of data points
        start_date = combined_df.index.min()
        end_date = combined_df.index.max()
        trading_days = (end_date - start_date).days * 5/7  # Approximate trading days

        if timeframe == 'M5':
            expected_points = trading_days * 24 * 60 / 5
        elif timeframe == 'M15':
            expected_points = trading_days * 24 * 60 / 15
        elif timeframe == 'M30':
            expected_points = trading_days * 24 * 60 / 30
        else:
            expected_points = len(combined_df)  # Fallback

        results['completeness'] = min(1.0, len(combined_df) / expected_points)

        # Check consistency (agreement between terminals)
        if len(terminal_data) > 1:
            # Group by time and check variance
            grouped = combined_df.groupby(combined_df.index)
            close_variance = grouped['close'].std().mean()
            normalized_variance = close_variance / combined_df['close'].mean()
            results['consistency'] = max(0.0, 1.0 - normalized_variance)
        else:
            results['consistency'] = 1.0  # Only one terminal, so perfect consistency

        # Check for gaps
        if len(combined_df) > 1:
            # Calculate time differences
            time_diffs = np.diff(combined_df.index.astype(np.int64)) / 1e9 / 60  # Convert to minutes

            # Determine expected time difference based on timeframe
            if timeframe == 'M5':
                expected_diff = 5
            elif timeframe == 'M15':
                expected_diff = 15
            elif timeframe == 'M30':
                expected_diff = 30
            else:
                expected_diff = np.median(time_diffs)  # Use median as fallback

            # Count gaps (time differences significantly larger than expected)
            gaps = np.sum(time_diffs > expected_diff * 2)
            results['gaps_percentage'] = gaps / len(time_diffs)
        else:
            results['gaps_percentage'] = 0.0

        # Check for outliers in price data
        for col in ['open', 'high', 'low', 'close']:
            if col in combined_df.columns:
                # Calculate z-scores
                z_scores = np.abs((combined_df[col] - combined_df[col].mean()) / combined_df[col].std())
                outliers = np.sum(z_scores > 3)  # Count values more than 3 standard deviations from mean
                results[f'outliers_percentage_{col}'] = outliers / len(combined_df)

        # Average the outlier percentages
        outlier_cols = [col for col in results if col.startswith('outliers_percentage_')]
        if outlier_cols:
            results['outliers_percentage'] = sum(results[col] for col in outlier_cols) / len(outlier_cols)

        return results

    def merge_terminal_data(
        self,
        symbol: str,
        timeframe: str
    ) -> Optional[pd.DataFrame]:
        """
        Merge data from all terminals for a symbol and timeframe.

        Args:
            symbol: Symbol
            timeframe: Timeframe

        Returns:
            Optional[pd.DataFrame]: Merged data or None if no data
        """
        try:
            # Get all terminal data for this timeframe
            symbol_dir = os.path.join(self.data_dir, timeframe, symbol)
            if not os.path.exists(symbol_dir):
                logger.warning(f"No data directory found for {symbol} {timeframe}")
                return None

            # Load data from all terminals
            terminal_data = {}
            for file in os.listdir(symbol_dir):
                if file.endswith('.parquet') and file.startswith('terminal_'):
                    file_path = os.path.join(symbol_dir, file)
                    terminal_id = int(file.split('_')[1].split('.')[0])
                    terminal_data[terminal_id] = pd.read_parquet(file_path)

            if not terminal_data:
                logger.warning(f"No data found for {symbol} {timeframe}")
                return None

            # Merge data from all terminals
            merged_df = self._merge_data(terminal_data)

            # Save merged data
            merged_file = os.path.join(symbol_dir, 'merged.parquet')
            merged_df.to_parquet(merged_file)
            logger.info(f"Saved merged data for {symbol} {timeframe} to {merged_file}")

            return merged_df

        except Exception as e:
            logger.error(f"Error merging terminal data for {symbol} {timeframe}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _merge_data(self, terminal_data: Dict[int, pd.DataFrame]) -> pd.DataFrame:
        """
        Merge data from multiple terminals.

        Args:
            terminal_data: Dictionary of terminal_id -> data

        Returns:
            pd.DataFrame: Merged data
        """
        # If only one terminal, return its data
        if len(terminal_data) == 1:
            return list(terminal_data.values())[0]

        # Combine data from all terminals
        all_data = []
        for terminal_id, df in terminal_data.items():
            df = df.copy()
            df['terminal_id'] = terminal_id
            all_data.append(df)

        combined_df = pd.concat(all_data)

        # Group by time and aggregate
        grouped = combined_df.groupby(combined_df.index)

        # For OHLC data, use appropriate aggregation
        merged_data = {
            'open': grouped['open'].first(),
            'high': grouped['high'].max(),
            'low': grouped['low'].min(),
            'close': grouped['close'].last(),
            'terminal_count': grouped['terminal_id'].count()
        }

        # Add volume if it exists
        if 'volume' in combined_df.columns:
            merged_data['volume'] = grouped['volume'].sum()

        # Add tick_volume if it exists
        if 'tick_volume' in combined_df.columns:
            merged_data['tick_volume'] = grouped['tick_volume'].sum()

        merged_df = pd.DataFrame(merged_data)

        return merged_df
