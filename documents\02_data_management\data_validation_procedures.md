# Data Validation Procedures

This document describes the comprehensive data validation procedures implemented in the MT5 Trading System.

## Overview

The MT5 Trading System implements a multi-layered data validation framework to ensure data quality, integrity, and consistency throughout the entire data pipeline. The validation system includes automated checks for raw market data, feature engineering outputs, model inputs, and prediction results.

## Validation Architecture

### Validation Components

```
Data Validation Framework
├── DataValidator (data/validation/data_validator.py)
│   ├── OHLCV data validation
│   ├── Missing values detection
│   ├── Price integrity checks
│   └── Timestamp validation
├── FeatureValidator (data/validation/feature_validator.py)
│   ├── Feature distribution analysis
│   ├── Data drift detection
│   └── Model input validation
├── ModelValidator (data/validation/model_validator.py)
│   ├── Input shape validation
│   ├── Value range checks
│   └── Sequence consistency validation
└── DataQualityManager (data/validation/data_quality_manager.py)
    ├── Comprehensive data quality checks
    ├── Statistical analysis
    └── Visual inspection plots
```

### Validation Layers

1. **Raw Data Validation**: Validates OHLCV data from MT5 terminals
2. **Feature Validation**: Validates engineered features and technical indicators
3. **Model Input Validation**: Validates data prepared for model training/prediction
4. **Output Validation**: Validates model predictions and trading signals

## Raw Data Validation

### DataValidator Class

The `DataValidator` class provides comprehensive validation for raw OHLCV data:

```python
from data.validation.data_validator import DataValidator

# Initialize validator with default rules
validator = DataValidator()

# Validate OHLCV data
results = validator.validate_data(df, parallel=True)
```

### Default Validation Rules

#### 1. Missing Values Check
```python
# Allow up to 1% missing values
{
    'missing_values_check': {
        'type': 'missing_values',
        'parameters': {
            'columns': ['open', 'high', 'low', 'close', 'volume'],
            'threshold': 0.01
        }
    }
}
```

#### 2. Price Integrity Check
```python
# Ensure price relationships are valid
{
    'price_integrity_check': {
        'type': 'range_check',
        'parameters': {
            'relation': 'high >= low'
        }
    }
}
```

**Validation Logic:**
- `high >= low` (always true)
- `high >= open` (high must be >= opening price)
- `high >= close` (high must be >= closing price)
- `low <= open` (low must be <= opening price)
- `low <= close` (low must be <= closing price)

#### 3. Price Range Check
```python
# Ensure prices are positive
{
    'price_outliers_check': {
        'type': 'range_check',
        'parameters': {
            'columns': {
                'open': {'min_value': 0.0001},
                'high': {'min_value': 0.0001},
                'low': {'min_value': 0.0001},
                'close': {'min_value': 0.0001}
            }
        }
    }
}
```

#### 4. Timestamp Validation
```python
# Ensure timestamps are in ascending order
{
    'timestamp_check': {
        'type': 'timestamp_check',
        'parameters': {
            'timestamp_column': 'time',
            'ascending': True
        }
    }
}
```

#### 5. Data Type Validation
```python
# Ensure correct data types
{
    'data_type_check': {
        'type': 'data_type_check',
        'parameters': {
            'columns': {
                'open': 'float64',
                'high': 'float64',
                'low': 'float64',
                'close': 'float64',
                'volume': 'float64'
            }
        }
    }
}
```

### Outlier Detection

#### Z-Score Method
```python
def detect_price_outliers(df, symbol, timeframe, threshold=3.0):
    """Detect price outliers using Z-score method."""

    # Calculate price changes
    df['price_change'] = df['close'].pct_change()

    # Calculate Z-scores
    mean_change = df['price_change'].mean()
    std_change = df['price_change'].std()
    df['z_score'] = (df['price_change'] - mean_change) / std_change

    # Identify outliers
    outliers = df[abs(df['z_score']) > threshold]

    if len(outliers) > len(df) * 0.01:  # More than 1% outliers
        logger.error(f"Too many outliers: {len(outliers)/len(df)*100:.2f}%")
        return False

    return True
```

#### Volume Consistency Check
```python
def check_volume_consistency(df, symbol, timeframe):
    """Check volume data consistency."""

    # Check for zero volume periods
    zero_volume = df[df['volume'] == 0]
    if len(zero_volume) > len(df) * 0.05:  # More than 5% zero volume
        logger.warning(f"High zero volume periods: {len(zero_volume)/len(df)*100:.2f}%")

    # Check for volume outliers
    volume_mean = df['volume'].mean()
    volume_std = df['volume'].std()
    volume_outliers = df[df['volume'] > volume_mean + 5 * volume_std]

    if len(volume_outliers) > len(df) * 0.01:
        logger.warning(f"Volume outliers detected: {len(volume_outliers)}")

    return True
```

## Feature Validation

### FeatureValidator Class

The `FeatureValidator` class validates engineered features and detects data drift:

```python
from data.validation.feature_validator import FeatureValidator

# Initialize feature validator
feature_validator = FeatureValidator(output_dir="data/validation/reports")

# Validate feature distributions
results = feature_validator.validate_feature_distributions(
    reference_df=historical_features,
    new_df=current_features,
    features=['sma_20', 'rsi', 'macd']
)
```

### Distribution Validation

#### Kolmogorov-Smirnov Test
```python
def validate_feature_distributions(self, reference_df, new_df, features):
    """Validate feature distributions using statistical tests."""

    results = {}

    for feature in features:
        ref_values = reference_df[feature].dropna()
        new_values = new_df[feature].dropna()

        # Kolmogorov-Smirnov test
        ks_stat, ks_pvalue = ks_2samp(ref_values, new_values)

        # Mann-Whitney U test
        mw_stat, mw_pvalue = mannwhitneyu(ref_values, new_values)

        # Check for significant distribution change
        distribution_changed = (ks_pvalue < 0.05) or (mw_pvalue < 0.05)

        results[feature] = {
            'ks_statistic': ks_stat,
            'ks_pvalue': ks_pvalue,
            'mw_statistic': mw_stat,
            'mw_pvalue': mw_pvalue,
            'distribution_changed': distribution_changed
        }

    return results
```

### Data Drift Detection

#### Rolling Window Analysis
```python
def detect_data_drift(self, df, features, window_size=100):
    """Detect data drift using rolling window analysis."""

    results = {}

    for feature in features:
        # Split into reference and current windows
        reference_window = df[feature].iloc[-2*window_size:-window_size]
        current_window = df[feature].iloc[-window_size:]

        # Statistical tests
        ks_stat, ks_pvalue = ks_2samp(reference_window, current_window)

        # Calculate normalized mean difference
        mean_diff = abs(current_window.mean() - reference_window.mean())
        mean_diff_normalized = mean_diff / reference_window.std()

        # Detect drift
        drift_detected = (ks_pvalue < 0.05) or (mean_diff_normalized > 2.0)

        results[feature] = {
            'drift_detected': drift_detected,
            'ks_pvalue': ks_pvalue,
            'mean_diff_normalized': mean_diff_normalized
        }

    return results
```

## Model Input Validation

### ModelValidator Class

The `ModelValidator` class validates data prepared for model training and prediction:

```python
from data.validation.model_validator import ModelValidator

# Initialize model validator
model_validator = ModelValidator(
    expected_input_shape=(None, 10, 32),  # (batch_size, sequence_length, features)
    expected_output_shape=(None, 1)
)

# Validate model inputs
results = model_validator.validate_input_shape(X)
```

### Input Shape Validation

```python
def validate_input_shape(self, X):
    """Validate the shape of model inputs."""

    if len(X.shape) != len(self.expected_input_shape):
        return {
            'status': 'fail',
            'message': f"Input has {len(X.shape)} dimensions, expected {len(self.expected_input_shape)}",
            'details': {
                'expected_shape': self.expected_input_shape,
                'actual_shape': X.shape
            }
        }

    # Check each dimension (except batch size)
    for i, (actual, expected) in enumerate(zip(X.shape[1:], self.expected_input_shape[1:])):
        if expected is not None and actual != expected:
            return {
                'status': 'fail',
                'message': f"Dimension {i+1} mismatch: got {actual}, expected {expected}",
                'details': {
                    'dimension': i+1,
                    'expected': expected,
                    'actual': actual
                }
            }

    return {'status': 'pass', 'message': "Input shape validation passed"}
```

### Value Range Validation

```python
def validate_input_values(self, X, expected_range=None):
    """Validate the values of model inputs."""

    # Check for NaN values
    nan_count = np.isnan(X).sum()
    if nan_count > 0:
        return {
            'status': 'fail',
            'message': f"Input contains {nan_count} NaN values",
            'details': {
                'nan_count': int(nan_count),
                'nan_percentage': float(nan_count / X.size)
            }
        }

    # Check for infinite values
    inf_count = np.isinf(X).sum()
    if inf_count > 0:
        return {
            'status': 'fail',
            'message': f"Input contains {inf_count} infinite values",
            'details': {
                'inf_count': int(inf_count),
                'inf_percentage': float(inf_count / X.size)
            }
        }

    # Check value range if specified
    if expected_range is not None:
        min_val, max_val = expected_range
        if X.min() < min_val or X.max() > max_val:
            return {
                'status': 'fail',
                'message': f"Values outside expected range [{min_val}, {max_val}]",
                'details': {
                    'expected_range': expected_range,
                    'actual_range': (float(X.min()), float(X.max()))
                }
            }

    return {'status': 'pass', 'message': "Input value validation passed"}
```

Last updated: 2025-06-06 16:25:00
