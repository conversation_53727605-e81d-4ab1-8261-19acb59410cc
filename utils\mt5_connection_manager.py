"""
MT5 Connection Manager.
This module ensures proper initialization and management of MT5 terminal connections.
It ensures Algo Trading remains enabled while initializing or checking connections.
It uses the standardized MT5 initializer to maintain consistent initialization across the codebase.
"""
import logging
import MetaTrader5 as mt5
from config.central_config import CentralConfig
from utils.mt5_initializer import initialize_mt5_once
from utils.path_utils import normalize_path

# Configure logging
logger = logging.getLogger(__name__)

class MT5ConnectionManager:
    """
    MT5 Connection Manager class for managing MT5 terminal connections.
    Ensures Algo Trading remains enabled while initializing or checking connections.
    """
    def __init__(self):
        """Initialize MT5ConnectionManager."""
        self.connected_terminals = {}
        self._initialize_mt5()

    def _initialize_mt5(self) -> bool:
        """
        Initialize MT5 with proper parameters using the global initializer.

        Returns:
            bool: True if MT5 was initialized successfully, False otherwise
        """
        # Use the global initializer to ensure MT5 is only initialized once
        return initialize_mt5_once()

    def check_terminal(self, terminal_id: int, config: dict) -> bool:
        """
        Check if terminal is accessible without reinitializing MT5.
        Following the MT5 connection guide best practices:
        1. Never calls mt5.shutdown() which can disable Algo Trading
        2. Uses the existing MT5 connection without reinitializing
        3. Provides detailed information about Algo Trading status

        Args:
            terminal_id: Terminal ID
            config: Terminal configuration

        Returns:
            bool: True if terminal is accessible, False otherwise
        """
        # Normalize the path to ensure consistent format
        normalized_path = normalize_path(config['path'])

        logger.info(f"\nChecking Terminal {terminal_id}...")
        logger.info(f"Path: {normalized_path}")
        logger.info(f"Server: {config['server']}")
        logger.info(f"Login: {config['login']}")

        try:
            # Get terminal info to verify connection
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                logger.error(f"Failed to get terminal info for Terminal {terminal_id}")
                logger.error(f"Error: {mt5.last_error()}")
                return False

            # Check if terminal is connected
            if not terminal_info.connected:
                logger.error(f"Terminal {terminal_id} is not connected to the server")
                logger.info("Please check your internet connection and make sure the terminal is connected")
                return False

            # Get account info to verify connection
            account_info = mt5.account_info()
            if account_info is None:
                logger.error(f"Failed to get account info for Terminal {terminal_id}")
                logger.error(f"Error: {mt5.last_error()}")
                logger.info("This might indicate that the terminal is not logged in or the connection to the server is lost")
                return False

            logger.info(f"Terminal {terminal_id} is accessible")
            logger.info(f"Balance: {account_info.balance}")
            logger.info(f"Equity: {account_info.equity}")
            logger.info(f"Margin: {account_info.margin}")

            # Check if Algo Trading is enabled in terminal info
            terminal_trade_allowed = False
            if hasattr(terminal_info, 'trade_allowed'):
                terminal_trade_allowed = terminal_info.trade_allowed
                logger.info(f"  Terminal trade_allowed: {terminal_trade_allowed}")
                if not terminal_trade_allowed:
                    logger.warning("  Algo Trading button is not enabled in the MT5 terminal")
                    logger.info("  Please enable it by clicking the 'Algo Trading' button in the toolbar")

            # Check if Algo Trading is enabled in account info
            account_trade_expert = False
            if hasattr(account_info, 'trade_expert'):
                account_trade_expert = account_info.trade_expert
                logger.info(f"  Account trade_expert: {account_trade_expert}")
                if not account_trade_expert:
                    logger.warning("  Expert Advisors are not allowed to trade for this account")
                    logger.info("  This might be due to an account change or server restrictions")

            # Determine if Algo Trading is enabled
            algo_trading_enabled = terminal_trade_allowed and account_trade_expert

            if algo_trading_enabled:
                logger.info(f"[SUCCESS] Algo Trading is ENABLED for Terminal {terminal_id}")
            else:
                logger.warning(f"[FAILED] Algo Trading is DISABLED for Terminal {terminal_id}")
                logger.info("Please refer to mt5_connection_guide.md for instructions on enabling Algo Trading")

            self.connected_terminals[terminal_id] = {
                'path': config['path'],
                'server': config['server'],
                'login': config['login'],
                'name': config.get('name', f'Terminal {terminal_id}'),
                'algo_trading_enabled': algo_trading_enabled,
                'terminal_trade_allowed': terminal_trade_allowed,
                'account_trade_expert': account_trade_expert
            }
            return True

        except Exception as e:
            logger.error(f"Error checking Terminal {terminal_id}: {str(e)}")
            return False

    def initialize_all_terminals(self) -> dict:
        """
        Initialize MT5 once and add all terminals to the connected_terminals dictionary.
        Following the MT5 connection guide best practices:
        1. Initializes MT5 only once using the global initializer
        2. Never calls mt5.shutdown() which can disable Algo Trading
        3. Adds all terminals to the connected_terminals dictionary without reinitializing MT5
        4. Verifies the connection to Terminal 3 (IC Markets) to ensure everything is working

        Returns:
            dict: Dictionary of connected terminals
        """
        logger.info("Initializing MT5 once and adding all terminals to connected_terminals...")

        try:
            # STEP 1: Initialize MT5 once using the global initializer with Terminal 1
            # Always use Terminal 1 to keep Algo Trading enabled in Terminal 1
            logger.info("STEP 1: Initializing MT5 once with Terminal 1...")
            if not initialize_mt5_once(terminal_id=1):
                logger.error("Failed to initialize MT5 with Terminal 1")
                logger.info("Please check that Terminal 1 is running and accessible")
                logger.info("Refer to mt5_connection_guide.md for detailed instructions")
                return {}

            logger.info("MT5 initialized successfully with Terminal 1")

            # STEP 2: Add all terminals to the connected_terminals dictionary without reinitializing MT5
            logger.info("\nSTEP 2: Adding all terminals to connected_terminals dictionary...")
            for terminal_id in range(1, 6):
                # Check if the terminal exists in the configuration
                if not CentralConfig.get_terminal_path(terminal_id):
                    logger.info(f"Terminal {terminal_id} not found in configuration - skipping")
                    continue

                terminal_path = CentralConfig.get_terminal_path(terminal_id)
                terminal_server = CentralConfig.get_terminal_server(terminal_id)
                terminal_login = CentralConfig.get_terminal_login(terminal_id)
                terminal_name = CentralConfig.get_terminal_name(terminal_id)

                if not terminal_path or not terminal_server or not terminal_login:
                    logger.warning(f"Terminal {terminal_id} has incomplete configuration - skipping")
                    continue

                self.connected_terminals[terminal_id] = {
                    'path': terminal_path,
                    'server': terminal_server,
                    'login': terminal_login,
                    'name': terminal_name or f'Terminal {terminal_id}',
                    'algo_trading_enabled': None,  # Will be updated when checked
                    'terminal_trade_allowed': None,
                    'account_trade_expert': None
                }
                logger.info(f"Added Terminal {terminal_id} ({terminal_name}) to connected terminals (without initializing)")

            # STEP 3: Check all terminals to verify connections and Algo Trading status
            logger.info("\nSTEP 3: Verifying connection to all terminals...")

            # Check each terminal from 1 to 5
            for check_terminal_id in range(1, 6):
                terminal_path = CentralConfig.get_terminal_path(check_terminal_id)
                terminal_server = CentralConfig.get_terminal_server(check_terminal_id)
                terminal_login = CentralConfig.get_terminal_login(check_terminal_id)
                terminal_name = CentralConfig.get_terminal_name(check_terminal_id)

                if not terminal_path or not terminal_server or not terminal_login:
                    logger.warning(f"Terminal {check_terminal_id} has incomplete configuration - skipping verification")
                    continue

                logger.info(f"\nChecking Terminal {check_terminal_id} ({terminal_name})...")
                if self.check_terminal(check_terminal_id, {
                    'path': terminal_path,
                    'server': terminal_server,
                    'login': terminal_login,
                    'name': terminal_name
                }):
                    logger.info(f"Successfully verified connection to Terminal {check_terminal_id} ({terminal_name})")

                    # Check if Algo Trading is enabled
                    if self.connected_terminals[check_terminal_id].get('algo_trading_enabled', False):
                        logger.info(f"[SUCCESS] Algo Trading is ENABLED in Terminal {check_terminal_id}")
                    else:
                        logger.warning(f"[FAILED] Algo Trading is DISABLED in Terminal {check_terminal_id}")
                        logger.info(f"Please enable Algo Trading in Terminal {check_terminal_id} to ensure it remains enabled")
                else:
                    logger.error(f"Terminal {check_terminal_id} is not accessible after initialization")
                    logger.info("This might indicate a problem with the MT5 connection")

            # All terminals have been checked in the previous step
            # No need for additional checks

        except Exception as e:
            logger.error(f"Error initializing terminals: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

        logger.info("\nInitialization complete. Connected terminals:")
        for terminal_id, info in self.connected_terminals.items():
            logger.info(f"Terminal {terminal_id}: {info['name']} ({info['server']})")

        return self.connected_terminals

    def get_terminal_info(self, terminal_id: int) -> dict:
        """
        Get information about a terminal.

        Args:
            terminal_id: Terminal ID

        Returns:
            dict: Terminal information
        """
        if terminal_id not in self.connected_terminals:
            logger.warning(f"Terminal {terminal_id} is not connected")
            return {}

        return self.connected_terminals[terminal_id]

    def get_all_terminal_info(self) -> dict:
        """
        Get information about all connected terminals.

        Returns:
            dict: Dictionary of connected terminals
        """
        return self.connected_terminals

def check_mt5_connections():
    """
    Check accessibility of all configured terminals.
    Following the MT5 connection guide best practices:
    1. Initializes MT5 only once using the global initializer
    2. Never calls mt5.shutdown() which can disable Algo Trading
    3. Checks all terminals without reinitializing MT5
    4. Provides detailed information about Algo Trading status
    """
    logger.info("Starting MT5 terminal connection check...")
    logger.info("Following MT5 connection guide best practices to maintain Algo Trading enabled")

    manager = MT5ConnectionManager()
    accessible_terminals = 0
    total_terminals = 0
    algo_trading_enabled_terminals = 0
    algo_trading_disabled_terminals = []

    try:
        for terminal_id in range(1, 6):
            # Check if the terminal exists in the configuration
            terminal_path = CentralConfig.get_terminal_path(terminal_id)
            if not terminal_path:
                logger.info(f"Terminal {terminal_id} not found in configuration - skipping")
                continue

            total_terminals += 1
            terminal_server = CentralConfig.get_terminal_server(terminal_id)
            terminal_login = CentralConfig.get_terminal_login(terminal_id)
            terminal_name = CentralConfig.get_terminal_name(terminal_id)

            # Create config dictionary for the terminal
            config = {
                'path': terminal_path,
                'server': terminal_server,
                'login': terminal_login,
                'name': terminal_name or f'Terminal {terminal_id}'
            }

            logger.info(f"\nChecking Terminal {terminal_id} ({terminal_name})...")
            if manager.check_terminal(terminal_id, config):
                accessible_terminals += 1

                # Check if Algo Trading is enabled
                if manager.connected_terminals[terminal_id].get('algo_trading_enabled', False):
                    algo_trading_enabled_terminals += 1
                    logger.info(f"✅ Algo Trading is ENABLED for Terminal {terminal_id}")
                else:
                    algo_trading_disabled_terminals.append(terminal_id)
                    logger.warning(f"❌ Algo Trading is DISABLED for Terminal {terminal_id}")

                    # Provide specific guidance based on what's disabled
                    terminal_trade_allowed = manager.connected_terminals[terminal_id].get('terminal_trade_allowed', False)
                    account_trade_expert = manager.connected_terminals[terminal_id].get('account_trade_expert', False)

                    if not terminal_trade_allowed:
                        logger.info("  Please enable Algo Trading by clicking the 'Algo Trading' button in the toolbar")
                    if not account_trade_expert:
                        logger.info("  Expert Advisors are not allowed to trade for this account")
                        logger.info("  This might be due to an account change or server restrictions")
            else:
                logger.error(f"Terminal {terminal_id} is not accessible")

        # Print summary
        logger.info(f"\nConnection check summary:")
        logger.info(f"Total terminals: {total_terminals}")
        logger.info(f"Accessible terminals: {accessible_terminals}")
        logger.info(f"Not accessible: {total_terminals - accessible_terminals}")
        logger.info(f"Terminals with Algo Trading enabled: {algo_trading_enabled_terminals}")
        logger.info(f"Terminals with Algo Trading disabled: {len(algo_trading_disabled_terminals)}")

        if accessible_terminals > 0:
            logger.info("\nAccessible terminals:")
            for terminal_id, info in manager.connected_terminals.items():
                algo_status = "✅ Algo Trading ENABLED" if info.get('algo_trading_enabled', False) else "❌ Algo Trading DISABLED"
                logger.info(f"Terminal {terminal_id}: {info['name']} ({info['server']}) - {algo_status}")

        if algo_trading_disabled_terminals:
            logger.warning("\nTerminals with Algo Trading disabled:")
            for terminal_id in algo_trading_disabled_terminals:
                logger.warning(f"Terminal {terminal_id}: {manager.connected_terminals[terminal_id]['name']}")
            logger.info("Please refer to mt5_connection_guide.md for instructions on enabling Algo Trading")

        return manager.connected_terminals

    except Exception as e:
        logger.error(f"Error during connection check: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

    # IMPORTANT: DO NOT call mt5.shutdown() as it will disable Algo Trading
    # The script will end without shutting down MT5, which is the correct behavior
    # to maintain Algo Trading enabled

class MT5ConnectionManagerExtension(MT5ConnectionManager):
    """
    Extension of MT5ConnectionManager with additional methods for checking terminal and account info.
    """
    def check_terminal_info(self, terminal_id: int) -> object:
        """
        Check terminal info for a specific terminal without connecting to it.
        This method is safer than initializing MT5 with a different terminal,
        which can sometimes disable Algo Trading in other terminals.

        Args:
            terminal_id: Terminal ID to check

        Returns:
            Terminal info object or None if check fails
        """
        from config.credentials import MT5_TERMINALS

        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal ID {terminal_id} not found in configuration")
            return None

        # First, make sure MT5 is initialized
        if not self._initialize_mt5():
            logger.error("Failed to initialize MT5")
            return None

        # Get terminal info without changing the active terminal
        terminal_info = mt5.terminal_info()
        if terminal_info is None:
            logger.error(f"Failed to get terminal info for Terminal {terminal_id}")
            logger.error(f"MT5 error: {mt5.last_error()}")
            return None

        return terminal_info

    def check_account_info(self, terminal_id: int) -> object:
        """
        Check account info for a specific terminal without connecting to it.
        This method is safer than initializing MT5 with a different terminal,
        which can sometimes disable Algo Trading in other terminals.

        Args:
            terminal_id: Terminal ID to check

        Returns:
            Account info object or None if check fails
        """
        from config.credentials import MT5_TERMINALS

        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal ID {terminal_id} not found in configuration")
            return None

        # First, make sure MT5 is initialized
        if not self._initialize_mt5():
            logger.error("Failed to initialize MT5")
            return None

        # Get account info without changing the active terminal
        account_info = mt5.account_info()
        if account_info is None:
            logger.error(f"Failed to get account info for Terminal {terminal_id}")
            logger.error(f"MT5 error: {mt5.last_error()}")
            return None

        return account_info

if __name__ == "__main__":
    check_mt5_connections()
