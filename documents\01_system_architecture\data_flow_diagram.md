# Data Flow Diagram

This document describes the detailed data flow architecture of the MT5 Trading System.

## Overview

The MT5 Trading System implements a sophisticated data flow architecture that processes market data through multiple stages: collection, feature engineering, model training, prediction, signal generation, and order execution. Each stage is handled by specialized components with clear interfaces and standardized data formats.

## Complete Data Flow Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           MT5 TRADING SYSTEM DATA FLOW                         │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Terminal 1    │    │   Terminal 2    │    │   Terminal 3    │    │   Terminal 4    │    │   Terminal 5    │
│   (ARIMA)       │    │   (LSTM)        │    │   (TFT)         │    │ (LSTM+ARIMA)    │    │ (TFT+ARIMA)     │
│ Pepperstone     │    │ Pepperstone     │    │ IC Markets      │    │ IC Markets      │    │ IC Markets      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │                      │                      │
          └──────────────────────┼──────────────────────┼──────────────────────┼──────────────────────┘
                                 │                      │                      │
                                 ▼                      ▼                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │                MT5Connector                                │
                    │  - check_connection()                                      │
                    │  - get_historical_data()                                   │
                    │  - get_symbol_info()                                       │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │                DataCollector                               │
                    │  - collect_historical_data()                              │
                    │  - get_latest_data()                                       │
                    │  - process_data_with_indicators()                          │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              Raw OHLCV Data                                │
                    │  Format: pandas.DataFrame                                  │
                    │  Columns: [time, open, high, low, close, volume]          │
                    │  Storage: data/storage/historical/terminal_{id}/           │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              FeatureEngineer                               │
                    │  - create_features()                                       │
                    │  - create_sequences()                                      │
                    │  - calculate_technical_indicators()                        │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │            Feature Data                                    │
                    │  Format: pandas.DataFrame with technical indicators       │
                    │  Features: SMA, EMA, RSI, MACD, Bollinger Bands, etc.    │
                    │  Storage: data/storage/features/terminal_{id}/             │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              ModelManager                                  │
                    │  - train_model()                                           │
                    │  - load_model()                                            │
                    │  - predict()                                               │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                    ┌─────────────────┼─────────────────┬─────────────────┬─────────────────┐
                    ▼                 ▼                 ▼                 ▼                 ▼
        ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
        │  ARIMA Model    │ │  LSTM Model     │ │  TFT Model      │ │ LSTM+ARIMA      │ │ TFT+ARIMA       │
        │  Terminal 1     │ │  Terminal 2     │ │  Terminal 3     │ │ Ensemble        │ │ Ensemble        │
        │                 │ │                 │ │                 │ │ Terminal 4      │ │ Terminal 5      │
        └─────────┬───────┘ └─────────┬───────┘ └─────────┬───────┘ └─────────┬───────┘ └─────────┬───────┘
                  │                   │                   │                   │                   │
                  └───────────────────┼───────────────────┼───────────────────┼───────────────────┘
                                      │                   │                   │
                                      ▼                   ▼                   ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              Model Predictions                             │
                    │  Format: numpy.ndarray                                     │
                    │  Content: Price predictions for next timeframe            │
                    │  Confidence: Model confidence scores                       │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              TradingStrategy                               │
                    │  - generate_signals()                                      │
                    │  - aggregate_timeframe_signals()                           │
                    │  - apply_risk_management()                                 │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              Trading Signals                               │
                    │  Format: Dict                                              │
                    │  Content: {direction, strength, confidence, timeframe}    │
                    │  Values: BUY/SELL, 0.0-1.0, 0.0-1.0, M5/M15/M30/H1/H4   │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              OrderManager                                  │
                    │  - execute_order()                                         │
                    │  - manage_positions()                                      │
                    │  - apply_risk_controls()                                   │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              MT5 Order Execution                          │
                    │  - Market orders                                           │
                    │  - Stop loss orders                                        │
                    │  - Take profit orders                                      │
                    └─────────────────┬───────────────────────────────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────────────────────────────┐
                    │              PerformanceMonitor                            │
                    │  - track_trades()                                          │
                    │  - calculate_metrics()                                     │
                    │  - update_dashboard()                                      │
                    └─────────────────────────────────────────────────────────────┘
```

## Detailed Component Flow

### 1. Data Collection Layer

**MT5Connector → DataCollector**

```python
# MT5Connector provides raw market data
df = mt5_connector.get_historical_data(
    symbol="BTCUSD.a",
    timeframe="M5",
    num_bars=1000
)

# DataCollector processes and stores data
data_collector = DataCollector(mt5_connector)
processed_data = data_collector.collect_historical_data(
    symbol="BTCUSD.a",
    timeframe="M5",
    start_date="2023-01-01",
    terminal_id=1
)
```

**Data Format:**
```python
# Raw OHLCV DataFrame structure
{
    'time': pd.Timestamp,
    'open': float,
    'high': float,
    'low': float,
    'close': float,
    'volume': int
}
```

### 2. Feature Engineering Layer

**DataCollector → FeatureEngineer**

```python
# FeatureEngineer creates technical indicators
feature_eng = FeatureEngineer()
features = feature_eng.create_features(raw_data)

# Create sequences for model training
X, y, dates = feature_eng.create_sequences(
    df=features,
    target_column='close',
    sequence_length=10,
    return_dates=True
)
```

**Feature Data Format:**
```python
# Enhanced DataFrame with technical indicators
{
    'time': pd.Timestamp,
    'open': float, 'high': float, 'low': float, 'close': float, 'volume': int,
    'sma_20': float, 'ema_12': float, 'rsi': float,
    'macd': float, 'macd_signal': float, 'macd_histogram': float,
    'bb_upper': float, 'bb_middle': float, 'bb_lower': float,
    'atr': float, 'cci': float, 'stoch_k': float, 'stoch_d': float
}
```

### 3. Model Training and Prediction Layer

**FeatureEngineer → ModelManager → Individual Models**

```python
# ModelManager coordinates model training
model_manager = ModelManager()

# Train model based on terminal type
if terminal_id == 1:  # ARIMA
    model_manager.train_model("BTCUSD.a_M5_arima", y_train)
elif terminal_id == 2:  # LSTM
    model_manager.train_model("BTCUSD.a_M5_lstm", X_train, y_train, X_val, y_val)
elif terminal_id == 3:  # TFT
    model_manager.train_model("BTCUSD.a_M5_tft", X_train, y_train, X_val, y_val)

# Generate predictions
prediction = model_manager.predict("BTCUSD.a_M5_lstm", X_latest)
```

### 4. Signal Generation Layer

**ModelManager → TradingStrategy**

```python
# TradingStrategy aggregates predictions into trading signals
strategy = TradingStrategy(terminal_id=1, model_type="arima")

# Generate signals for multiple timeframes
signals = {}
for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
    signal = strategy.generate_signal(
        symbol="BTCUSD.a",
        timeframe=timeframe
    )
    signals[timeframe] = signal

# Aggregate signals
final_signal = strategy.aggregate_timeframe_signals(signals)
```

**Signal Format:**
```python
{
    'direction': 'BUY' | 'SELL',
    'strength': float,  # 0.0-1.0
    'confidence': float,  # 0.0-1.0
    'timeframe': str,  # 'M5', 'M15', etc.
    'prediction': float,  # Raw model prediction
    'current_price': float
}
```

### 5. Order Execution Layer

**TradingStrategy → OrderManager → MT5**

```python
# OrderManager executes trades based on signals
order_manager = OrderManager(mt5_connector)

if final_signal['strength'] > 0.6 and final_signal['confidence'] > 0.7:
    # Calculate position size
    position_size = order_manager.calculate_position_size(
        account_balance=25000.0,
        risk_per_trade=0.01,  # 1%
        stop_loss_pips=200
    )

    # Execute order
    order_result = order_manager.execute_order(
        symbol="BTCUSD.a",
        order_type=final_signal['direction'],
        volume=position_size,
        stop_loss=final_signal['current_price'] * 0.98,
        take_profit=final_signal['current_price'] * 1.03
    )
```

## Data Storage Paths

### Historical Data Storage
```
data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/
└── {start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
```

### Feature Data Storage
```
data/storage/features/terminal_{terminal_id}/{symbol}_{timeframe}/
└── features_terminal_{terminal_id}_{timestamp}.parquet
```

### Model Storage
```
model/saved_models/{model_type}/
├── {symbol}_{timeframe}_{model_type}.pkl/.pth
└── {symbol}_{timeframe}_{model_type}_metrics.json
```

## Error Handling and Recovery

### Connection Failures
```python
# MT5 connection check
if not mt5_connector.check_connection():
    logger.error("MT5 connection lost, attempting reconnection...")
    mt5_connector.reconnect()
```

### Data Validation
```python
# Data quality checks
if df is None or len(df) == 0:
    logger.error("No data received, skipping processing")
    return None

# Feature validation
if features.isnull().sum().sum() > len(features) * 0.1:
    logger.warning("High number of missing values detected")
```

### Model Fallbacks
```python
# Model loading with fallback
try:
    model = model_manager.load_model(model_name)
except Exception as e:
    logger.error(f"Failed to load model {model_name}, using fallback")
    model = model_manager.load_fallback_model()
```

Last updated: 2025-06-06 16:15:00
