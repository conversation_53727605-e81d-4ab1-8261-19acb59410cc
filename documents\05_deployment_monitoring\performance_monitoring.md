# Performance Monitoring

This document describes the comprehensive performance monitoring system implemented in the MT5 Trading System.

## Overview

The MT5 Trading System includes a sophisticated performance monitoring framework that tracks trading performance, model accuracy, system health, and risk metrics in real-time. The monitoring system provides both automated alerts and visual dashboards for comprehensive system oversight.

## Monitoring Components

### Real-Time Dashboard

The system provides a web-based dashboard accessible at `http://localhost:8050` that displays:

- **Live Trading Performance**: Real-time P&L, win rate, and trade statistics
- **Model Performance**: Individual model accuracy and prediction quality
- **Risk Metrics**: Current exposure, drawdown, and risk utilization
- **System Health**: Terminal status, connection health, and error rates

### Performance Metrics

#### Trading Performance Metrics

1. **Profitability Metrics**
   - Total P&L
   - Daily/Monthly returns
   - Return on investment (ROI)
   - Profit factor
   - Average trade profit/loss

2. **Risk Metrics**
   - Maximum drawdown
   - Current drawdown
   - Value at Risk (VaR)
   - Sharpe ratio
   - Sortino ratio

3. **Trade Statistics**
   - Total number of trades
   - Win rate percentage
   - Average holding time
   - Trade frequency
   - Position sizing accuracy

#### Model Performance Metrics

1. **Prediction Accuracy**
   - Mean Absolute Error (MAE)
   - Root Mean Square Error (RMSE)
   - Directional accuracy
   - Confidence intervals

2. **Model Health**
   - Training loss trends
   - Validation performance
   - Overfitting indicators
   - Feature importance stability

### Data Collection

#### Trade Data

```python
# Trade data structure
trade_data = {
    'timestamp': datetime.now(),
    'symbol': 'BTCUSD.a',
    'direction': 'BUY',
    'entry_price': 45000.0,
    'exit_price': 45500.0,
    'quantity': 0.1,
    'profit_loss': 50.0,
    'terminal_id': 1,
    'model_type': 'arima',
    'signal_strength': 0.75,
    'holding_time': 3600  # seconds
}
```

#### Performance Data Storage

Performance data is stored in CSV format for easy analysis:

```
monitoring/data/
├── trade_history.csv          # Individual trade records
├── equity_curve.csv           # Account equity over time
├── daily_returns.csv          # Daily return calculations
├── monthly_returns.csv        # Monthly performance summary
├── drawdown_periods.csv       # Drawdown analysis
└── model_performance.csv      # Model accuracy metrics
```

### Automated Monitoring

#### Performance Alerts

The system automatically monitors key metrics and sends alerts when thresholds are exceeded:

```python
def check_performance_alerts():
    alerts = []

    # Check drawdown
    current_drawdown = calculate_current_drawdown()
    if current_drawdown > 0.15:  # 15% threshold
        alerts.append({
            'type': 'HIGH_DRAWDOWN',
            'message': f'Current drawdown: {current_drawdown:.2%}',
            'severity': 'HIGH'
        })

    # Check win rate
    recent_win_rate = calculate_recent_win_rate(days=7)
    if recent_win_rate < 0.40:  # 40% threshold
        alerts.append({
            'type': 'LOW_WIN_RATE',
            'message': f'7-day win rate: {recent_win_rate:.2%}',
            'severity': 'MEDIUM'
        })

    # Check model performance
    for terminal_id in range(1, 6):
        model_accuracy = get_model_accuracy(terminal_id)
        if model_accuracy < 0.55:  # 55% threshold
            alerts.append({
                'type': 'LOW_MODEL_ACCURACY',
                'message': f'Terminal {terminal_id} accuracy: {model_accuracy:.2%}',
                'severity': 'MEDIUM'
            })

    return alerts
```

#### System Health Monitoring

```python
def monitor_system_health():
    health_status = {}

    # Check MT5 connections
    for terminal_id in range(1, 6):
        connection_status = check_mt5_connection(terminal_id)
        health_status[f'terminal_{terminal_id}'] = connection_status

    # Check model loading
    model_status = check_model_availability()
    health_status['models'] = model_status

    # Check data freshness
    data_freshness = check_data_freshness()
    health_status['data'] = data_freshness

    # Check disk space
    disk_usage = check_disk_usage()
    health_status['disk'] = disk_usage

    return health_status
```

### Visualization

#### Equity Curve

Real-time equity curve showing account balance over time:

```python
def generate_equity_curve():
    equity_data = load_equity_data()

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=equity_data['timestamp'],
        y=equity_data['equity'],
        mode='lines',
        name='Account Equity',
        line=dict(color='blue', width=2)
    ))

    fig.update_layout(
        title='Account Equity Curve',
        xaxis_title='Time',
        yaxis_title='Equity ($)',
        template='plotly_white'
    )

    return fig
```

#### Drawdown Analysis

Visualization of drawdown periods and recovery:

```python
def generate_drawdown_plot():
    drawdown_data = calculate_drawdown_series()

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=drawdown_data['timestamp'],
        y=drawdown_data['drawdown'],
        mode='lines',
        fill='tonexty',
        name='Drawdown',
        line=dict(color='red', width=1),
        fillcolor='rgba(255, 0, 0, 0.3)'
    ))

    fig.update_layout(
        title='Drawdown Analysis',
        xaxis_title='Time',
        yaxis_title='Drawdown (%)',
        template='plotly_white'
    )

    return fig
```

#### Model Performance Comparison

Comparative analysis of model performance across terminals:

```python
def generate_model_comparison():
    model_metrics = {}

    for terminal_id in range(1, 6):
        metrics = get_terminal_metrics(terminal_id)
        model_metrics[f'Terminal {terminal_id}'] = metrics

    # Create comparison chart
    fig = go.Figure()

    terminals = list(model_metrics.keys())
    accuracies = [model_metrics[t]['accuracy'] for t in terminals]

    fig.add_trace(go.Bar(
        x=terminals,
        y=accuracies,
        name='Model Accuracy',
        marker_color='lightblue'
    ))

    fig.update_layout(
        title='Model Performance Comparison',
        xaxis_title='Terminal',
        yaxis_title='Accuracy (%)',
        template='plotly_white'
    )

    return fig
```

### Performance Reports

#### Daily Performance Report

Automated daily report generation:

```python
def generate_daily_report():
    report_date = datetime.now().date()

    # Calculate daily metrics
    daily_pnl = calculate_daily_pnl(report_date)
    daily_trades = get_daily_trades(report_date)
    daily_win_rate = calculate_daily_win_rate(report_date)

    # Generate report
    report = {
        'date': report_date,
        'total_pnl': daily_pnl,
        'total_trades': len(daily_trades),
        'win_rate': daily_win_rate,
        'best_trade': max(daily_trades, key=lambda x: x['profit']),
        'worst_trade': min(daily_trades, key=lambda x: x['profit']),
        'terminal_performance': get_terminal_performance(report_date)
    }

    return report
```

#### Monthly Performance Summary

Comprehensive monthly analysis:

```python
def generate_monthly_summary():
    current_month = datetime.now().replace(day=1)

    summary = {
        'period': current_month.strftime('%Y-%m'),
        'total_return': calculate_monthly_return(),
        'sharpe_ratio': calculate_sharpe_ratio(),
        'max_drawdown': calculate_max_drawdown(),
        'total_trades': get_monthly_trade_count(),
        'win_rate': calculate_monthly_win_rate(),
        'profit_factor': calculate_profit_factor(),
        'best_day': get_best_trading_day(),
        'worst_day': get_worst_trading_day(),
        'model_rankings': rank_models_by_performance()
    }

    return summary
```

### Integration with Trading System

#### Real-Time Updates

The monitoring system integrates with the trading bot to provide real-time updates:

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
        self.alerts = []

    def update_trade(self, trade_data):
        # Update performance metrics
        self.update_metrics(trade_data)

        # Check for alerts
        new_alerts = self.check_alerts()
        self.alerts.extend(new_alerts)

        # Update dashboard
        self.update_dashboard()

    def update_metrics(self, trade_data):
        # Update equity curve
        self.update_equity_curve(trade_data)

        # Update trade statistics
        self.update_trade_stats(trade_data)

        # Update model performance
        self.update_model_performance(trade_data)
```

### Monitoring Best Practices

1. **Regular Review**: Check dashboard daily for performance trends
2. **Alert Response**: Respond promptly to performance alerts
3. **Model Monitoring**: Track model accuracy and retrain when necessary
4. **Risk Management**: Monitor drawdown and adjust position sizes
5. **System Health**: Ensure all terminals and connections are healthy
6. **Data Backup**: Regular backup of performance data and logs

Last updated: 2025-06-06 16:10:00
