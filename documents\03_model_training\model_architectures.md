# Model Architectures

This document provides an overview of the different model types used in the MT5 Trading System.

## Overview

The MT5 Trading System implements multiple model types for price prediction, with each MT5 terminal specializing in a specific model type:

- **ARIMA (AutoRegressive Integrated Moving Average)**: Statistical time series forecasting model
- **LSTM (Long Short-Term Memory)**: Deep learning model for sequence prediction
- **TFT (Temporal Fusion Transformer)**: State-of-the-art time series forecasting model
- **Ensemble Models**: Combinations of multiple models for improved robustness

Each model type has its strengths and weaknesses, making them suitable for different market conditions and trading strategies.

## Terminal-Model Assignment

The MT5 Trading System assigns specific model types to each MT5 terminal:

| Terminal ID | Model Type | Description | Strengths |
|-------------|------------|-------------|-----------|
| 1 | ARIMA | Statistical time series forecasting | Statistical patterns, mean-reversion, works with limited data |
| 2 | LSTM | Deep learning sequence model | Complex patterns, sequence learning, non-linear relationships |
| 3 | TFT | Transformer-based forecasting | Advanced patterns with attention mechanisms, interpretability |
| 4 | LSTM+ARIMA Ensemble | Hybrid statistical and deep learning | Combines strengths of statistical and deep learning approaches |
| 5 | TFT+ARIMA Ensemble | Advanced hybrid approach | Combines transformer capabilities with statistical robustness |

## LSTM Model

### Overview

Long Short-Term Memory (LSTM) is a type of recurrent neural network (RNN) designed to recognize patterns in sequences of data. It is well-suited for time series forecasting tasks like price prediction.

### Features

- **Memory Cells**: Can remember information for long periods
- **Gates**: Controls what information to keep or forget
- **Sequence Learning**: Captures temporal dependencies in data
- **Feature Importance**: Can identify important features for prediction

### Implementation

The LSTM model is implemented in the `model/lstm_model.py` file and provides the `LSTMModelTrainer` class for training and evaluating LSTM models.

```python
from model.lstm_model import LSTMModelTrainer

# Initialize model
model = LSTMModelTrainer(
    input_size=10,
    hidden_size=64,
    num_layers=2,
    dropout=0.2
)

# Train model
history = model.train(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    batch_size=32,
    epochs=100,
    patience=10
)

# Make predictions
predictions = model.predict(X_test)
```

### Performance Considerations

- Requires sufficient data for training
- Can capture complex non-linear patterns
- May overfit on noisy data
- Computationally intensive for training

## TFT Model

### Overview

The Temporal Fusion Transformer (TFT) is a state-of-the-art time series forecasting model that combines LSTM layers with self-attention mechanisms. It is designed to handle complex temporal patterns and provide interpretable predictions.

### Features

- **Temporal Fusion Transformer Architecture**: Combines LSTM layers with self-attention mechanisms
- **Multi-horizon Forecasting**: Capable of forecasting multiple steps ahead
- **Variable Selection Network**: Automatically selects relevant features
- **Interpretability**: Provides attention weights for feature importance analysis
- **Quantile Forecasting**: Supports probabilistic forecasting with quantile predictions
- **Gated Residual Network**: Enhances model capacity with skip connections
- **Interpretable Multi-Head Attention**: Provides insights into feature relationships

### Implementation

The TFT model is implemented in the `model/tft_model_pytorch.py` file (core model) and `model/tft_model_trainer.py` file (trainer interface) and provides the `TFTModelTrainer` class for training and evaluating TFT models.

```python
from model.tft_model_pytorch import TFTModel

# Initialize model
model = TFTModel(
    input_size=32,
    hidden_size=64,
    num_attention_heads=4,
    dropout_rate=0.1,
    learning_rate=0.001
)

# Train model
history = model.train_model(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    epochs=100,
    batch_size=32
)

# Make predictions
predictions = model.predict(X_test, feature_names)

# Standard approach with organized subdirectories
model.save_model("model/saved_models/tft/BTCUSD.a_M5_tft.pth")
model.load_model("model/saved_models/tft/BTCUSD.a_M5_tft.pth")
```

### Key Components

1. **TimeDistributed Layer**: Applies a layer to every temporal slice of an input
2. **GatedResidualNetwork**: Enhances model capacity with skip connections and gating mechanisms
3. **VariableSelectionNetwork**: Automatically selects relevant features for prediction
4. **InterpretableMultiHeadAttention**: Provides interpretable attention weights for feature importance

### Performance Considerations

- More computationally intensive than LSTM models
- Requires more data for training
- Excels at capturing both local and global patterns
- Provides better interpretability through attention mechanisms
- Handles missing data better than standard LSTM models
- More robust to noise and outliers

## ARIMA Model

### Overview

ARIMA (AutoRegressive Integrated Moving Average) is a statistical time series forecasting model that combines autoregression, differencing, and moving average components to model time series data.

### Features

- **AutoRegressive (AR) Component**: Uses past values to predict future values
- **Integrated (I) Component**: Applies differencing to make the time series stationary
- **Moving Average (MA) Component**: Uses past forecast errors in a regression-like model
- **Automatic Parameter Selection**: Automatically determines the optimal ARIMA order (p, d, q)
- **Exogenous Variables Support**: Can incorporate external variables into the model

### Implementation

The ARIMA model is implemented in the `model/arima_model.py` file and provides the `ARIMAModelTrainer` class for training and evaluating ARIMA models.

```python
from model.arima_model import ARIMAModelTrainer

# Initialize model
model = ARIMAModelTrainer(
    order=None,  # Auto-determine
    auto_determine=True
)

# Train model
history = model.train(
    y_train=y_train,
    exog_train=None  # No exogenous variables
)

# Make predictions
predictions = model.predict(steps=1)
```

### Performance Considerations

- Computationally efficient compared to deep learning models
- Works well with limited data
- Interpretable and provides confidence intervals
- Best suited for data with clear trends or seasonality
- May struggle with complex non-linear patterns

Last updated: 2025-06-06 15:50:00
