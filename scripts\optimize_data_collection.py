"""
Optimized Data Collection Script.
This script optimizes the data collection process for all terminals and timeframes.

Usage:
    python scripts/optimize_data_collection.py [options]

Options:
    --symbol BTCUSD.a        Symbol to collect (default: BTCUSD.a)
    --years 5                Years of historical data (default: 5)
    --parallel               Use parallel collection (faster but more resource intensive)
    --validate               Validate data quality after collection
    --clean-duplicates       Remove duplicate data files
"""
import argparse
import logging
import sys
import os
import glob
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import required modules
from data.data_collector import DataCollector
from data.validation.data_validator import DataValidator
from utils.path_utils import (
    get_historical_data_path,
    VALID_TIMEFRAMES,
    VALID_TERMINAL_IDS
)
# MT5 connector import
from trading.mt5_connector import MT5Connector

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_collection_optimization.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedDataCollector:
    """Optimized data collector with parallel processing and validation."""
    
    def __init__(self):
        """Initialize the optimized data collector."""
        self.mt5_connector = None
        self.data_collector = None
        self.data_validator = None
        self.collection_stats = {}
        
    def initialize(self) -> bool:
        """Initialize all components."""
        try:
            logger.info("Initializing optimized data collector...")
            
            # Initialize MT5 connector
            self.mt5_connector = MT5Connector()
            logger.info("[SUCCESS] MT5 Connector initialized")

            # Initialize data collector
            self.data_collector = DataCollector(self.mt5_connector)
            logger.info("[SUCCESS] Data Collector initialized")

            # Initialize data validator
            self.data_validator = DataValidator()
            logger.info("[SUCCESS] Data Validator initialized")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            return False
    
    def clean_duplicate_files(self, symbol: str) -> int:
        """Clean duplicate data files."""
        logger.info(f"\n=== CLEANING DUPLICATE FILES FOR {symbol} ===")
        
        cleaned_count = 0
        
        try:
            for terminal_id in VALID_TERMINAL_IDS:
                for timeframe in VALID_TIMEFRAMES:
                    # Get directory path
                    dir_path = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}"
                    
                    if not os.path.exists(dir_path):
                        continue
                    
                    # Find all parquet files
                    pattern = os.path.join(dir_path, "*.parquet")
                    files = glob.glob(pattern)
                    
                    if len(files) <= 1:
                        continue
                    
                    # Sort files by modification time (newest first)
                    files.sort(key=os.path.getmtime, reverse=True)
                    
                    # Keep the newest file, remove others
                    for old_file in files[1:]:
                        try:
                            os.remove(old_file)
                            logger.info(f"Removed duplicate file: {old_file}")
                            cleaned_count += 1
                        except Exception as e:
                            logger.error(f"Failed to remove {old_file}: {str(e)}")
            
            logger.info(f"[SUCCESS] Cleaned {cleaned_count} duplicate files")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning duplicate files: {str(e)}")
            return 0
    
    def collect_terminal_timeframe_data(
        self, 
        terminal_id: int, 
        timeframe: str, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> Tuple[int, str, bool, int]:
        """Collect data for a specific terminal and timeframe."""
        try:
            logger.info(f"Collecting {symbol} {timeframe} data for Terminal {terminal_id}...")
            
            # Check if data already exists
            data_path = get_historical_data_path(
                symbol=symbol,
                timeframe=timeframe,
                terminal_id=terminal_id,
                create_dirs=False
            )
            
            # Look for existing files
            dir_path = os.path.dirname(data_path)
            if os.path.exists(dir_path):
                existing_files = glob.glob(os.path.join(dir_path, "*.parquet"))
                if existing_files:
                    # Check the newest file
                    newest_file = max(existing_files, key=os.path.getmtime)
                    try:
                        df = pd.read_parquet(newest_file)
                        if len(df) > 1000:  # Reasonable amount of data
                            logger.info(f"[SUCCESS] Terminal {terminal_id} {timeframe}: Using existing data ({len(df)} bars)")
                            return terminal_id, timeframe, True, len(df)
                    except Exception:
                        pass  # File might be corrupted, collect new data
            
            # Collect new data
            df = self.data_collector.download_historical_data(
                symbol=symbol,
                timeframes=[timeframe],
                start_date=start_date,
                end_date=end_date,
                terminal_ids=[terminal_id],
                save=True,
                years_to_collect=None
            )
            
            if df and timeframe in df and df[timeframe] is not None and len(df[timeframe]) > 0:
                data_count = len(df[timeframe])
                logger.info(f"[SUCCESS] Terminal {terminal_id} {timeframe}: Collected {data_count} bars")
                return terminal_id, timeframe, True, data_count
            else:
                logger.warning(f"[WARNING] Terminal {terminal_id} {timeframe}: No data collected")
                return terminal_id, timeframe, False, 0

        except Exception as e:
            logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: {str(e)}")
            return terminal_id, timeframe, False, 0
    
    def collect_data_parallel(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        max_workers: int = 3
    ) -> Dict[str, Any]:
        """Collect data using parallel processing."""
        logger.info(f"\n=== PARALLEL DATA COLLECTION FOR {symbol} ===")
        logger.info(f"Using {max_workers} parallel workers")
        
        results = {}
        total_tasks = len(VALID_TERMINAL_IDS) * len(VALID_TIMEFRAMES)
        completed_tasks = 0
        successful_tasks = 0
        
        # Create tasks
        tasks = []
        for terminal_id in VALID_TERMINAL_IDS:
            for timeframe in VALID_TIMEFRAMES:
                tasks.append((terminal_id, timeframe))
        
        # Execute tasks in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(
                    self.collect_terminal_timeframe_data,
                    terminal_id, timeframe, symbol, start_date, end_date
                ): (terminal_id, timeframe)
                for terminal_id, timeframe in tasks
            }
            
            # Process completed tasks
            for future in as_completed(future_to_task):
                terminal_id, timeframe = future_to_task[future]
                completed_tasks += 1
                
                try:
                    _, _, success, count = future.result()
                    
                    if success:
                        successful_tasks += 1
                        status = "SUCCESS"
                    else:
                        status = "FAILED"
                    
                    # Store result
                    if terminal_id not in results:
                        results[terminal_id] = {}
                    results[terminal_id][timeframe] = {
                        'status': status,
                        'count': count
                    }
                    
                    # Progress update
                    progress = (completed_tasks / total_tasks) * 100
                    logger.info(f"Progress: {completed_tasks}/{total_tasks} ({progress:.1f}%) - "
                              f"Terminal {terminal_id} {timeframe}: {status}")
                    
                except Exception as e:
                    logger.error(f"Task failed for Terminal {terminal_id} {timeframe}: {str(e)}")
                    if terminal_id not in results:
                        results[terminal_id] = {}
                    results[terminal_id][timeframe] = {
                        'status': 'ERROR',
                        'count': 0
                    }
                    completed_tasks += 1
        
        # Summary
        success_rate = (successful_tasks / total_tasks) * 100
        logger.info(f"\nParallel collection completed: {successful_tasks}/{total_tasks} ({success_rate:.1f}%)")
        
        self.collection_stats = {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'success_rate': success_rate,
            'results': results
        }
        
        return results
    
    def collect_data_sequential(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Any]:
        """Collect data sequentially (safer but slower)."""
        logger.info(f"\n=== SEQUENTIAL DATA COLLECTION FOR {symbol} ===")
        
        results = {}
        total_tasks = len(VALID_TERMINAL_IDS) * len(VALID_TIMEFRAMES)
        completed_tasks = 0
        successful_tasks = 0
        
        for terminal_id in VALID_TERMINAL_IDS:
            results[terminal_id] = {}
            
            for timeframe in VALID_TIMEFRAMES:
                completed_tasks += 1
                
                try:
                    _, _, success, count = self.collect_terminal_timeframe_data(
                        terminal_id, timeframe, symbol, start_date, end_date
                    )
                    
                    if success:
                        successful_tasks += 1
                        status = "SUCCESS"
                    else:
                        status = "FAILED"
                    
                    results[terminal_id][timeframe] = {
                        'status': status,
                        'count': count
                    }
                    
                    # Progress update
                    progress = (completed_tasks / total_tasks) * 100
                    logger.info(f"Progress: {completed_tasks}/{total_tasks} ({progress:.1f}%)")
                    
                    # Small delay to avoid overwhelming MT5
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Error collecting Terminal {terminal_id} {timeframe}: {str(e)}")
                    results[terminal_id][timeframe] = {
                        'status': 'ERROR',
                        'count': 0
                    }
        
        # Summary
        success_rate = (successful_tasks / total_tasks) * 100
        logger.info(f"\nSequential collection completed: {successful_tasks}/{total_tasks} ({success_rate:.1f}%)")
        
        self.collection_stats = {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'success_rate': success_rate,
            'results': results
        }
        
        return results
    
    def validate_collected_data(self, symbol: str) -> Dict[str, Any]:
        """Validate all collected data."""
        logger.info(f"\n=== VALIDATING COLLECTED DATA FOR {symbol} ===")
        
        validation_results = {}
        total_validations = 0
        successful_validations = 0
        
        for terminal_id in VALID_TERMINAL_IDS:
            validation_results[terminal_id] = {}
            
            for timeframe in VALID_TIMEFRAMES:
                try:
                    # Find data file
                    dir_path = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}"
                    if not os.path.exists(dir_path):
                        continue
                    
                    files = glob.glob(os.path.join(dir_path, "*.parquet"))
                    if not files:
                        continue
                    
                    # Use the newest file
                    data_file = max(files, key=os.path.getmtime)
                    
                    # Load and validate data
                    df = pd.read_parquet(data_file)
                    validation_results_dict = self.data_validator.validate_data(df)

                    # Check if all validation rules passed
                    all_passed = True
                    issues = []

                    for rule_name, result in validation_results_dict.items():
                        if result.get('status') != 'pass':
                            all_passed = False
                            issues.append(f"{rule_name}: {result.get('message', 'Unknown error')}")

                    validation_result = {
                        'is_valid': all_passed,
                        'issues': issues
                    }
                    
                    total_validations += 1
                    
                    if validation_result['is_valid']:
                        successful_validations += 1
                        logger.info(f"[SUCCESS] Terminal {terminal_id} {timeframe}: Data quality OK ({len(df)} bars)")
                        status = "VALID"
                    else:
                        logger.warning(f"[WARNING] Terminal {terminal_id} {timeframe}: Data quality issues")
                        for issue in validation_result.get('issues', []):
                            logger.warning(f"  - {issue}")
                        status = "ISSUES"
                    
                    validation_results[terminal_id][timeframe] = {
                        'status': status,
                        'count': len(df),
                        'issues': validation_result.get('issues', [])
                    }
                    
                except Exception as e:
                    logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: Validation error - {str(e)}")
                    validation_results[terminal_id][timeframe] = {
                        'status': 'ERROR',
                        'count': 0,
                        'issues': [str(e)]
                    }
                    total_validations += 1
        
        # Summary
        if total_validations > 0:
            success_rate = (successful_validations / total_validations) * 100
            logger.info(f"Data validation completed: {successful_validations}/{total_validations} ({success_rate:.1f}%)")
        else:
            logger.warning("No data found for validation")
        
        return validation_results
    
    def generate_report(self, symbol: str) -> None:
        """Generate a comprehensive collection report."""
        logger.info("\n" + "="*80)
        logger.info(f"DATA COLLECTION OPTIMIZATION REPORT FOR {symbol}")
        logger.info("="*80)
        
        if self.collection_stats:
            stats = self.collection_stats
            logger.info(f"\n[STATISTICS] COLLECTION STATISTICS:")
            logger.info(f"Total tasks: {stats['total_tasks']}")
            logger.info(f"Successful tasks: {stats['successful_tasks']}")
            logger.info(f"Success rate: {stats['success_rate']:.1f}%")

            # Terminal breakdown
            logger.info(f"\n[BREAKDOWN] TERMINAL BREAKDOWN:")
            for terminal_id in VALID_TERMINAL_IDS:
                if terminal_id in stats['results']:
                    terminal_results = stats['results'][terminal_id]
                    successful = sum(1 for tf_result in terminal_results.values() 
                                   if tf_result['status'] == 'SUCCESS')
                    total = len(terminal_results)
                    logger.info(f"Terminal {terminal_id}: {successful}/{total} timeframes successful")
        
        logger.info("="*80)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Optimize data collection process')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Symbol to collect')
    parser.add_argument('--years', type=int, default=5, help='Years of historical data')
    parser.add_argument('--parallel', action='store_true', help='Use parallel collection')
    parser.add_argument('--validate', action='store_true', help='Validate data quality after collection')
    parser.add_argument('--clean-duplicates', action='store_true', help='Remove duplicate data files')
    
    args = parser.parse_args()
    
    logger.info("Starting optimized data collection...")
    logger.info(f"Symbol: {args.symbol}")
    logger.info(f"Years: {args.years}")
    logger.info(f"Parallel: {args.parallel}")
    logger.info(f"Validate: {args.validate}")
    logger.info(f"Clean duplicates: {args.clean_duplicates}")
    
    # Initialize collector
    collector = OptimizedDataCollector()
    if not collector.initialize():
        logger.error("Failed to initialize data collector")
        return 1
    
    # Clean duplicates if requested
    if args.clean_duplicates:
        collector.clean_duplicate_files(args.symbol)
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=args.years * 365)
    
    # Collect data
    if args.parallel:
        _ = collector.collect_data_parallel(args.symbol, start_date, end_date)
    else:
        _ = collector.collect_data_sequential(args.symbol, start_date, end_date)

    # Validate data if requested
    if args.validate:
        _ = collector.validate_collected_data(args.symbol)
    
    # Generate report
    collector.generate_report(args.symbol)
    
    logger.info("Data collection optimization completed!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
