"""
Data Validator Module.
This module provides tools for validating data quality and integrity.
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import json
import os

# Configure logger
logger = logging.getLogger('data.validation')

class DataValidator:
    """
    Data Validator class for validating data quality and integrity.
    """
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize DataValidator.

        Args:
            config_path: Path to validation configuration file (optional)
        """
        self.validation_rules = {}
        self.validation_results = {}

        # Load configuration if provided
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
        else:
            # Set up default validation rules for OHLCV data
            self._setup_default_rules()

    def load_config(self, config_path: str) -> bool:
        """
        Load validation configuration from a JSON file.

        Args:
            config_path: Path to validation configuration file

        Returns:
            bool: True if configuration was loaded successfully, False otherwise
        """
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)

            self.validation_rules = config.get('validation_rules', {})
            logger.info(f"Loaded validation configuration from {config_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to load validation configuration: {e}")
            return False

    def save_config(self, config_path: str) -> bool:
        """
        Save validation configuration to a JSON file.

        Args:
            config_path: Path to save validation configuration

        Returns:
            bool: True if configuration was saved successfully, False otherwise
        """
        try:
            config = {
                'validation_rules': self.validation_rules
            }

            with open(config_path, 'w') as f:
                json.dump(config, f, indent=4)

            logger.info(f"Saved validation configuration to {config_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save validation configuration: {e}")
            return False

    def _setup_default_rules(self):
        """Set up default validation rules for OHLCV data."""
        # Missing values check - allow up to 1% missing values
        self.add_rule(
            'missing_values_check',
            'missing_values',
            {
                'columns': ['open', 'high', 'low', 'close', 'volume'],
                'threshold': 0.01
            }
        )

        # Price integrity check - high >= low, close/open within reasonable bounds
        self.add_rule(
            'price_integrity_check',
            'range_check',
            {
                'columns': 'high',
                'relation': 'high >= low'
            }
        )

        # Price outliers check - prices should be positive
        self.add_rule(
            'price_outliers_check',
            'range_check',
            {
                'columns': {
                    'open': {'min_value': 0.0001},
                    'high': {'min_value': 0.0001},
                    'low': {'min_value': 0.0001},
                    'close': {'min_value': 0.0001}
                }
            }
        )

        # Timestamp check - ensure timestamps are in ascending order
        self.add_rule(
            'timestamp_check',
            'timestamp_check',
            {
                'timestamp_column': 'time',
                'ascending': True
            }
        )

        # Data type check - ensure numeric columns are numeric
        self.add_rule(
            'data_type_check',
            'data_type_check',
            {
                'columns': {
                    'open': 'float64',
                    'high': 'float64',
                    'low': 'float64',
                    'close': 'float64',
                    'volume': 'float64'
                }
            }
        )

        logger.info("Set up default validation rules for OHLCV data")

    def add_rule(self,
               rule_name: str,
               rule_type: str,
               parameters: Dict[str, Any]) -> bool:
        """
        Add a validation rule.

        Args:
            rule_name: Name of the rule
            rule_type: Type of rule (e.g., 'missing_values', 'range_check', 'outlier_check')
            parameters: Parameters for the rule

        Returns:
            bool: True if rule was added successfully, False otherwise
        """
        try:
            self.validation_rules[rule_name] = {
                'type': rule_type,
                'parameters': parameters
            }
            logger.info(f"Added validation rule: {rule_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add validation rule: {e}")
            return False

    def remove_rule(self, rule_name: str) -> bool:
        """
        Remove a validation rule.

        Args:
            rule_name: Name of the rule to remove

        Returns:
            bool: True if rule was removed successfully, False otherwise
        """
        if rule_name in self.validation_rules:
            del self.validation_rules[rule_name]
            logger.info(f"Removed validation rule: {rule_name}")
            return True
        else:
            logger.warning(f"Rule not found: {rule_name}")
            return False

    def validate_data(self,
                    df: pd.DataFrame,
                    rules: Optional[Dict[str, Dict]] = None,
                    parallel: bool = False,
                    max_workers: int = None) -> Dict[str, Dict]:
        """
        Validate data against rules.

        Args:
            df: DataFrame to validate
            rules: Dictionary of rules to apply (uses self.validation_rules if None)
            parallel: Whether to run validation in parallel
            max_workers: Maximum number of worker threads for parallel execution

        Returns:
            Dict[str, Dict]: Dictionary of validation results
        """
        if rules is None:
            rules = self.validation_rules

        if not rules:
            logger.warning("No validation rules defined")
            return {}

        # Create a copy of the DataFrame to avoid modifying the original
        # This is important for thread safety in parallel execution
        df_copy = df.copy()

        # Optimize: Pre-compute common statistics to avoid redundant calculations
        stats_cache = {}

        # For numeric columns, pre-compute min, max, mean, std
        numeric_cols = df_copy.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            stats_cache['numeric'] = {
                'min': df_copy[numeric_cols].min(),
                'max': df_copy[numeric_cols].max(),
                'mean': df_copy[numeric_cols].mean(),
                'std': df_copy[numeric_cols].std(),
                'isna_sum': df_copy[numeric_cols].isna().sum(),
                'isna_mean': df_copy[numeric_cols].isna().mean()
            }

        # For timestamp columns, pre-compute if they are monotonic
        if isinstance(df_copy.index, pd.DatetimeIndex):
            stats_cache['index_is_datetime'] = True
            stats_cache['index_is_monotonic'] = df_copy.index.is_monotonic_increasing
        else:
            stats_cache['index_is_datetime'] = False

        # Function to validate a single rule
        def validate_rule(rule_name, rule_config):
            rule_type = rule_config['type']
            parameters = rule_config['parameters']

            try:
                if rule_type == 'missing_values':
                    # Optimize: Use pre-computed isna statistics if available
                    if 'numeric' in stats_cache and all(col in numeric_cols for col in parameters.get('columns', [])):
                        cols = parameters.get('columns', [])
                        threshold = parameters.get('threshold', 0.1)

                        # Use pre-computed isna_mean
                        missing_percentages = stats_cache['numeric']['isna_mean'][cols]
                        failed_columns = missing_percentages[missing_percentages > threshold].index.tolist()

                        if failed_columns:
                            result = {
                                'status': 'fail',
                                'message': f"Columns with too many missing values: {failed_columns}",
                                'details': {col: float(missing_percentages[col]) for col in failed_columns}
                            }
                        else:
                            result = {
                                'status': 'pass',
                                'message': "Missing values check passed",
                                'details': {col: float(missing_percentages[col]) for col in cols}
                            }
                    else:
                        result = self._check_missing_values(df_copy, **parameters)

                elif rule_type == 'range_check':
                    # Optimize: Use pre-computed min/max if possible
                    if isinstance(parameters.get('columns'), str) and parameters['columns'] in numeric_cols and 'relation' not in parameters:
                        col = parameters['columns']
                        min_value = parameters.get('min_value')
                        max_value = parameters.get('max_value')

                        col_min = stats_cache['numeric']['min'][col]
                        col_max = stats_cache['numeric']['max'][col]

                        if min_value is not None and col_min < min_value:
                            result = {
                                'status': 'fail',
                                'message': f"Values below minimum in column {col}",
                                'details': {
                                    'min_allowed': min_value,
                                    'actual_min': float(col_min),
                                    'count_below_min': int((df_copy[col] < min_value).sum())
                                }
                            }
                        elif max_value is not None and col_max > max_value:
                            result = {
                                'status': 'fail',
                                'message': f"Values above maximum in column {col}",
                                'details': {
                                    'max_allowed': max_value,
                                    'actual_max': float(col_max),
                                    'count_above_max': int((df_copy[col] > max_value).sum())
                                }
                            }
                        else:
                            result = {
                                'status': 'pass',
                                'message': f"Value range check passed for column {col}",
                                'details': {
                                    'min_value': float(col_min),
                                    'max_value': float(col_max),
                                    'mean_value': float(stats_cache['numeric']['mean'][col]),
                                    'std_dev': float(stats_cache['numeric']['std'][col])
                                }
                            }
                    else:
                        result = self._check_value_range(df_copy, **parameters)

                elif rule_type == 'outlier_check':
                    result = self._check_outliers(df_copy, **parameters)

                elif rule_type == 'data_type_check':
                    # Handle parameter mapping for data_type_check
                    if 'columns' in parameters:
                        column_types = parameters['columns']
                        result = self._check_data_types(df_copy, column_types=column_types)
                    else:
                        result = self._check_data_types(df_copy, **parameters)

                elif rule_type == 'uniqueness_check':
                    result = self._check_uniqueness(df_copy, **parameters)

                elif rule_type == 'consistency_check':
                    result = self._check_consistency(df_copy, **parameters)

                elif rule_type == 'timestamp_check':
                    # Optimize: Use pre-computed monotonicity for index
                    if parameters.get('timestamp_column') == 'time' and stats_cache['index_is_datetime']:
                        ascending = parameters.get('ascending', True)

                        if ascending and not stats_cache['index_is_monotonic']:
                            result = {
                                'status': 'fail',
                                'message': f"Timestamps are not in ascending order in index",
                                'details': {
                                    'first_timestamp': df_copy.index[0].isoformat(),
                                    'last_timestamp': df_copy.index[-1].isoformat()
                                }
                            }
                        else:
                            result = {
                                'status': 'pass',
                                'message': f"Timestamp check passed for index",
                                'details': {
                                    'first_timestamp': df_copy.index[0].isoformat(),
                                    'last_timestamp': df_copy.index[-1].isoformat(),
                                    'count': len(df_copy.index)
                                }
                            }
                    else:
                        result = self._check_timestamps(df_copy, **parameters)

                elif rule_type == 'custom_check':
                    result = self._run_custom_check(df_copy, parameters.get('check_name', 'unknown'))

                else:
                    logger.warning(f"Unknown rule type: {rule_type}")
                    result = {'status': 'error', 'message': f"Unknown rule type: {rule_type}"}

                logger.info(f"Validated rule: {rule_name}, status: {result['status']}")
                return rule_name, result

            except Exception as e:
                logger.error(f"Error validating rule {rule_name}: {e}")
                import traceback
                logger.debug(traceback.format_exc())
                return rule_name, {'status': 'error', 'message': str(e)}

        # Execute validation rules
        results = {}

        if parallel and len(rules) > 1:
            try:
                # Import concurrent.futures only if parallel execution is requested
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # Submit all validation tasks
                    future_to_rule = {
                        executor.submit(validate_rule, rule_name, rule_config): rule_name
                        for rule_name, rule_config in rules.items()
                    }

                    # Collect results as they complete
                    for future in concurrent.futures.as_completed(future_to_rule):
                        rule_name, result = future.result()
                        results[rule_name] = result

                logger.info(f"Completed parallel validation of {len(rules)} rules")

            except ImportError:
                logger.warning("concurrent.futures not available, falling back to sequential validation")
                # Fall back to sequential execution
                for rule_name, rule_config in rules.items():
                    rule_name, result = validate_rule(rule_name, rule_config)
                    results[rule_name] = result
        else:
            # Sequential execution
            for rule_name, rule_config in rules.items():
                rule_name, result = validate_rule(rule_name, rule_config)
                results[rule_name] = result

        self.validation_results = results
        return results

    def _check_missing_values(self,
                           df: pd.DataFrame,
                           columns: Optional[List[str]] = None,
                           threshold: float = 0.1) -> Dict[str, Any]:
        """
        Check for missing values in the DataFrame.

        Args:
            df: DataFrame to check
            columns: List of columns to check (None for all columns)
            threshold: Maximum allowed percentage of missing values

        Returns:
            Dict[str, Any]: Validation result
        """
        if columns is None:
            columns = df.columns.tolist()
        else:
            # Filter to only include columns that exist in the DataFrame
            columns = [col for col in columns if col in df.columns]

        if not columns:
            return {'status': 'error', 'message': 'No valid columns to check'}

        # Calculate missing value percentages
        missing_percentages = df[columns].isna().mean()

        # Check if any column exceeds the threshold
        failed_columns = missing_percentages[missing_percentages > threshold].index.tolist()

        if failed_columns:
            return {
                'status': 'fail',
                'message': f"Columns with too many missing values: {failed_columns}",
                'details': {col: float(missing_percentages[col]) for col in failed_columns}
            }
        else:
            return {
                'status': 'pass',
                'message': "Missing values check passed",
                'details': {col: float(missing_percentages[col]) for col in columns}
            }

    def _check_value_range(self,
                        df: pd.DataFrame,
                        columns: Union[str, Dict[str, Dict[str, Any]]],
                        min_value: Optional[float] = None,
                        max_value: Optional[float] = None,
                        relation: Optional[str] = None) -> Dict[str, Any]:
        """
        Check if values in columns are within specified ranges or satisfy relations.

        Args:
            df: DataFrame to check
            columns: Either a single column name, or a dictionary mapping column names to constraints
                     Each constraint can have 'min_value', 'max_value', and 'relation' keys
            min_value: Minimum allowed value (None for no minimum) - used if columns is a string
            max_value: Maximum allowed value (None for no maximum) - used if columns is a string
            relation: Relation to check (e.g., 'high >= open') - used if columns is a string

        Returns:
            Dict[str, Any]: Validation result
        """
        # Handle single column case
        if isinstance(columns, str):
            column = columns
            if column not in df.columns:
                return {'status': 'error', 'message': f"Column not found: {column}"}

            # Get column values
            values = df[column].dropna()

            if len(values) == 0:
                return {'status': 'error', 'message': f"No non-null values in column: {column}"}

            # Check minimum value
            if min_value is not None and values.min() < min_value:
                return {
                    'status': 'fail',
                    'message': f"Values below minimum in column {column}",
                    'details': {
                        'min_allowed': min_value,
                        'actual_min': float(values.min()),
                        'count_below_min': int((values < min_value).sum())
                    }
                }

            # Check maximum value
            if max_value is not None and values.max() > max_value:
                return {
                    'status': 'fail',
                    'message': f"Values above maximum in column {column}",
                    'details': {
                        'max_allowed': max_value,
                        'actual_max': float(values.max()),
                        'count_above_max': int((values > max_value).sum())
                    }
                }

            # Check relation if provided
            if relation is not None:
                try:
                    # Evaluate the relation
                    mask = df.eval(relation)

                    if not isinstance(mask, pd.Series):
                        return {'status': 'error', 'message': 'Relation did not evaluate to a boolean Series'}

                    # Check if any rows violate the relation
                    violation_count = (~mask).sum()

                    if violation_count > 0:
                        return {
                            'status': 'fail',
                            'message': f"Relation check failed: {relation}",
                            'details': {
                                'violation_count': int(violation_count),
                                'violation_percentage': float(violation_count / len(df))
                            }
                        }
                except Exception as e:
                    return {'status': 'error', 'message': f"Error evaluating relation: {e}"}

            return {
                'status': 'pass',
                'message': f"Value range check passed for column {column}",
                'details': {
                    'min_value': float(values.min()),
                    'max_value': float(values.max()),
                    'mean_value': float(values.mean()),
                    'std_dev': float(values.std())
                }
            }

        # Handle multiple columns case
        elif isinstance(columns, dict):
            results = {}
            failed_columns = []

            for column, constraints in columns.items():
                if column not in df.columns:
                    results[column] = {'status': 'error', 'message': f"Column not found: {column}"}
                    failed_columns.append(column)
                    continue

                # Get column values
                values = df[column].dropna()

                if len(values) == 0:
                    results[column] = {'status': 'error', 'message': f"No non-null values in column: {column}"}
                    failed_columns.append(column)
                    continue

                # Get constraints
                col_min = constraints.get('min_value')
                col_max = constraints.get('max_value')
                col_relation = constraints.get('relation')

                # Check minimum value
                if col_min is not None and values.min() < col_min:
                    results[column] = {
                        'status': 'fail',
                        'message': f"Values below minimum in column {column}",
                        'details': {
                            'min_allowed': col_min,
                            'actual_min': float(values.min()),
                            'count_below_min': int((values < col_min).sum())
                        }
                    }
                    failed_columns.append(column)
                    continue

                # Check maximum value
                if col_max is not None and values.max() > col_max:
                    results[column] = {
                        'status': 'fail',
                        'message': f"Values above maximum in column {column}",
                        'details': {
                            'max_allowed': col_max,
                            'actual_max': float(values.max()),
                            'count_above_max': int((values > col_max).sum())
                        }
                    }
                    failed_columns.append(column)
                    continue

                # Check relation if provided
                if col_relation is not None:
                    try:
                        # Evaluate the relation
                        mask = df.eval(col_relation)

                        if not isinstance(mask, pd.Series):
                            results[column] = {'status': 'error', 'message': 'Relation did not evaluate to a boolean Series'}
                            failed_columns.append(column)
                            continue

                        # Check if any rows violate the relation
                        violation_count = (~mask).sum()

                        if violation_count > 0:
                            results[column] = {
                                'status': 'fail',
                                'message': f"Relation check failed: {col_relation}",
                                'details': {
                                    'violation_count': int(violation_count),
                                    'violation_percentage': float(violation_count / len(df))
                                }
                            }
                            failed_columns.append(column)
                            continue
                    except Exception as e:
                        results[column] = {'status': 'error', 'message': f"Error evaluating relation: {e}"}
                        failed_columns.append(column)
                        continue

                # If we get here, all checks passed for this column
                results[column] = {
                    'status': 'pass',
                    'message': f"Value range check passed for column {column}",
                    'details': {
                        'min_value': float(values.min()),
                        'max_value': float(values.max()),
                        'mean_value': float(values.mean()),
                        'std_dev': float(values.std())
                    }
                }

            # Determine overall status
            if failed_columns:
                return {
                    'status': 'fail',
                    'message': f"Value range check failed for columns: {failed_columns}",
                    'details': results
                }
            else:
                return {
                    'status': 'pass',
                    'message': "Value range check passed for all columns",
                    'details': results
                }

        else:
            return {'status': 'error', 'message': "Invalid 'columns' parameter type"}

    def _check_outliers(self,
                     df: pd.DataFrame,
                     column: str,
                     method: str = 'zscore',
                     threshold: float = 3.0) -> Dict[str, Any]:
        """
        Check for outliers in a column.

        Args:
            df: DataFrame to check
            column: Column to check
            method: Method to use for outlier detection ('zscore', 'iqr')
            threshold: Threshold for outlier detection

        Returns:
            Dict[str, Any]: Validation result
        """
        if column not in df.columns:
            return {'status': 'error', 'message': f"Column not found: {column}"}

        # Get column values
        values = df[column].dropna()

        if len(values) == 0:
            return {'status': 'error', 'message': f"No non-null values in column: {column}"}

        # Detect outliers
        if method == 'zscore':
            z_scores = np.abs((values - values.mean()) / values.std())
            outliers = values[z_scores > threshold]
        elif method == 'iqr':
            q1 = values.quantile(0.25)
            q3 = values.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - threshold * iqr
            upper_bound = q3 + threshold * iqr
            outliers = values[(values < lower_bound) | (values > upper_bound)]
        else:
            return {'status': 'error', 'message': f"Unknown outlier detection method: {method}"}

        # Check if outliers exceed threshold
        outlier_percentage = len(outliers) / len(values)
        max_outlier_percentage = 0.05  # 5% outliers allowed

        if outlier_percentage > max_outlier_percentage:
            return {
                'status': 'fail',
                'message': f"Too many outliers in column {column}",
                'details': {
                    'method': method,
                    'threshold': threshold,
                    'outlier_count': len(outliers),
                    'outlier_percentage': float(outlier_percentage),
                    'max_allowed_percentage': max_outlier_percentage
                }
            }

        return {
            'status': 'pass',
            'message': f"Outlier check passed for column {column}",
            'details': {
                'method': method,
                'threshold': threshold,
                'outlier_count': len(outliers),
                'outlier_percentage': float(outlier_percentage)
            }
        }

    def _check_data_types(self,
                       df: pd.DataFrame,
                       column_types: Dict[str, str]) -> Dict[str, Any]:
        """
        Check if columns have the expected data types.

        Args:
            df: DataFrame to check
            column_types: Dictionary mapping column names to expected data types

        Returns:
            Dict[str, Any]: Validation result
        """
        # Filter to only include columns that exist in the DataFrame
        valid_columns = {col: dtype for col, dtype in column_types.items() if col in df.columns}

        if not valid_columns:
            return {'status': 'error', 'message': 'No valid columns to check'}

        # Check data types
        failed_columns = {}

        for column, expected_type in valid_columns.items():
            actual_type = str(df[column].dtype)

            # Check if types match
            if expected_type == 'numeric':
                type_matches = pd.api.types.is_numeric_dtype(df[column])
            elif expected_type == 'datetime':
                type_matches = pd.api.types.is_datetime64_dtype(df[column])
            elif expected_type == 'string' or expected_type == 'str':
                type_matches = pd.api.types.is_string_dtype(df[column]) or pd.api.types.is_object_dtype(df[column])
            elif expected_type == 'boolean' or expected_type == 'bool':
                type_matches = pd.api.types.is_bool_dtype(df[column])
            else:
                type_matches = expected_type == actual_type

            if not type_matches:
                failed_columns[column] = {'expected': expected_type, 'actual': actual_type}

        if failed_columns:
            return {
                'status': 'fail',
                'message': f"Columns with incorrect data types: {list(failed_columns.keys())}",
                'details': failed_columns
            }
        else:
            return {
                'status': 'pass',
                'message': "Data type check passed",
                'details': {col: str(df[col].dtype) for col in valid_columns}
            }

    def _check_uniqueness(self,
                       df: pd.DataFrame,
                       columns: Union[str, List[str]],
                       allow_nulls: bool = False) -> Dict[str, Any]:
        """
        Check if values in columns are unique.

        Args:
            df: DataFrame to check
            columns: Column or list of columns to check for uniqueness
            allow_nulls: Whether to allow null values

        Returns:
            Dict[str, Any]: Validation result
        """
        if isinstance(columns, str):
            columns = [columns]

        # Filter to only include columns that exist in the DataFrame
        columns = [col for col in columns if col in df.columns]

        if not columns:
            return {'status': 'error', 'message': 'No valid columns to check'}

        # Check uniqueness
        if allow_nulls:
            subset = df[columns]
        else:
            subset = df[columns].dropna()

        duplicate_count = len(subset) - len(subset.drop_duplicates())

        if duplicate_count > 0:
            return {
                'status': 'fail',
                'message': f"Duplicate values found in columns: {columns}",
                'details': {
                    'duplicate_count': duplicate_count,
                    'duplicate_percentage': float(duplicate_count / len(subset))
                }
            }
        else:
            return {
                'status': 'pass',
                'message': f"Uniqueness check passed for columns: {columns}",
                'details': {
                    'row_count': len(subset)
                }
            }

    def _check_consistency(self,
                        df: pd.DataFrame,
                        condition: str) -> Dict[str, Any]:
        """
        Check if data satisfies a consistency condition.

        Args:
            df: DataFrame to check
            condition: String representation of a condition to evaluate

        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            # Evaluate the condition
            mask = df.eval(condition)

            if not isinstance(mask, pd.Series):
                return {'status': 'error', 'message': 'Condition did not evaluate to a boolean Series'}

            # Check if any rows violate the condition
            violation_count = (~mask).sum()

            if violation_count > 0:
                return {
                    'status': 'fail',
                    'message': f"Consistency check failed: {condition}",
                    'details': {
                        'violation_count': int(violation_count),
                        'violation_percentage': float(violation_count / len(df))
                    }
                }
            else:
                return {
                    'status': 'pass',
                    'message': f"Consistency check passed: {condition}",
                    'details': {
                        'row_count': len(df)
                    }
                }
        except Exception as e:
            return {'status': 'error', 'message': f"Error evaluating condition: {e}"}

    def _check_timestamps(self,
                       df: pd.DataFrame,
                       timestamp_column: str,
                       frequency: Optional[str] = None,
                       max_gap: Optional[str] = None,
                       ascending: bool = True) -> Dict[str, Any]:
        """
        Check if timestamps are valid and have the expected frequency.

        Args:
            df: DataFrame to check
            timestamp_column: Column containing timestamps
            frequency: Expected frequency (e.g., '1D', '1H', '15min')
            max_gap: Maximum allowed gap (e.g., '2D', '4H')
            ascending: Whether timestamps should be in ascending order

        Returns:
            Dict[str, Any]: Validation result
        """
        if timestamp_column not in df.columns:
            return {'status': 'error', 'message': f"Column not found: {timestamp_column}"}

        # Ensure column is datetime type
        try:
            timestamps = pd.to_datetime(df[timestamp_column])
        except Exception as e:
            return {'status': 'error', 'message': f"Failed to convert column to datetime: {e}"}

        # Check if timestamps are in the expected order
        if ascending and not timestamps.is_monotonic_increasing:
            return {
                'status': 'fail',
                'message': f"Timestamps are not in ascending order in column {timestamp_column}",
                'details': {
                    'first_timestamp': timestamps.iloc[0].isoformat(),
                    'last_timestamp': timestamps.iloc[-1].isoformat()
                }
            }
        elif not ascending and not timestamps.is_monotonic_decreasing:
            return {
                'status': 'fail',
                'message': f"Timestamps are not in descending order in column {timestamp_column}",
                'details': {
                    'first_timestamp': timestamps.iloc[0].isoformat(),
                    'last_timestamp': timestamps.iloc[-1].isoformat()
                }
            }

        # Check frequency if specified
        if frequency is not None:
            # Sort timestamps
            sorted_timestamps = timestamps.sort_values()

            # Calculate time differences
            time_diffs = sorted_timestamps.diff().dropna()

            # Convert frequency to timedelta
            try:
                expected_diff = pd.Timedelta(frequency)
            except Exception as e:
                return {'status': 'error', 'message': f"Invalid frequency format: {e}"}

            # Allow for small deviations (1% of expected frequency)
            tolerance = expected_diff * 0.01

            # Check if time differences match expected frequency
            invalid_diffs = time_diffs[(time_diffs < expected_diff - tolerance) |
                                     (time_diffs > expected_diff + tolerance)]

            if len(invalid_diffs) > 0:
                return {
                    'status': 'fail',
                    'message': f"Timestamps do not have the expected frequency {frequency}",
                    'details': {
                        'expected_frequency': frequency,
                        'invalid_count': len(invalid_diffs),
                        'invalid_percentage': float(len(invalid_diffs) / len(time_diffs)),
                        'min_diff': str(time_diffs.min()),
                        'max_diff': str(time_diffs.max()),
                        'median_diff': str(time_diffs.median())
                    }
                }

        # Check maximum gap if specified
        if max_gap is not None:
            # Sort timestamps
            sorted_timestamps = timestamps.sort_values()

            # Calculate time differences
            time_diffs = sorted_timestamps.diff().dropna()

            # Convert max_gap to timedelta
            try:
                max_gap_td = pd.Timedelta(max_gap)
            except Exception as e:
                return {'status': 'error', 'message': f"Invalid max_gap format: {e}"}

            # Check if any time difference exceeds the maximum gap
            excessive_gaps = time_diffs[time_diffs > max_gap_td]

            if len(excessive_gaps) > 0:
                return {
                    'status': 'fail',
                    'message': f"Timestamps have gaps exceeding the maximum allowed gap {max_gap}",
                    'details': {
                        'max_allowed_gap': max_gap,
                        'excessive_gap_count': len(excessive_gaps),
                        'max_actual_gap': str(time_diffs.max())
                    }
                }

        return {
            'status': 'pass',
            'message': f"Timestamp check passed for column {timestamp_column}",
            'details': {
                'first_timestamp': timestamps.iloc[0].isoformat(),
                'last_timestamp': timestamps.iloc[-1].isoformat(),
                'count': len(timestamps)
            }
        }

    def _run_custom_check(self,
                       df: pd.DataFrame,
                       check_name: str) -> Dict[str, Any]:
        """
        Run a custom validation check.

        Args:
            df: DataFrame to check
            check_name: Name of the custom check to run

        Returns:
            Dict[str, Any]: Validation result
        """
        # This is a placeholder for custom checks
        # In a real implementation, you would implement specific checks here

        if check_name == 'price_integrity':
            # Check that high >= low, high >= open, high >= close, low <= open, low <= close
            if not all(col in df.columns for col in ['high', 'low', 'open', 'close']):
                return {'status': 'error', 'message': "Missing required price columns"}

            # Check high >= low
            if (df['high'] < df['low']).any():
                return {
                    'status': 'fail',
                    'message': "Price integrity violated: high < low",
                    'details': {
                        'violation_count': int((df['high'] < df['low']).sum())
                    }
                }

            # Check high >= open
            if (df['high'] < df['open']).any():
                return {
                    'status': 'fail',
                    'message': "Price integrity violated: high < open",
                    'details': {
                        'violation_count': int((df['high'] < df['open']).sum())
                    }
                }

            # Check high >= close
            if (df['high'] < df['close']).any():
                return {
                    'status': 'fail',
                    'message': "Price integrity violated: high < close",
                    'details': {
                        'violation_count': int((df['high'] < df['close']).sum())
                    }
                }

            # Check low <= open
            if (df['low'] > df['open']).any():
                return {
                    'status': 'fail',
                    'message': "Price integrity violated: low > open",
                    'details': {
                        'violation_count': int((df['low'] > df['open']).sum())
                    }
                }

            # Check low <= close
            if (df['low'] > df['close']).any():
                return {
                    'status': 'fail',
                    'message': "Price integrity violated: low > close",
                    'details': {
                        'violation_count': int((df['low'] > df['close']).sum())
                    }
                }

            return {
                'status': 'pass',
                'message': "Price integrity check passed"
            }

        # Add more custom checks as needed

        return {'status': 'error', 'message': f"Unknown custom check: {check_name}"}

    def generate_validation_report(self,
                                results: Optional[Dict[str, Dict]] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive validation report with detailed metrics.

        Args:
            results: Validation results (uses self.validation_results if None)

        Returns:
            Dict[str, Any]: Validation report
        """
        if results is None:
            results = self.validation_results

        if not results:
            logger.warning("No validation results available")
            return {
                'status': 'error',
                'message': 'No validation results available',
                'timestamp': datetime.now().isoformat()
            }

        # Count results by status
        status_counts = {'pass': 0, 'fail': 0, 'error': 0}

        for rule_name, result in results.items():
            status = result.get('status', 'error')
            status_counts[status] = status_counts.get(status, 0) + 1

        # Calculate overall status
        if status_counts['error'] > 0:
            overall_status = 'error'
        elif status_counts['fail'] > 0:
            overall_status = 'fail'
        else:
            overall_status = 'pass'

        # Collect failed rule details for easier debugging
        failed_rules = {
            rule_name: result
            for rule_name, result in results.items()
            if result.get('status') == 'fail'
        }

        error_rules = {
            rule_name: result
            for rule_name, result in results.items()
            if result.get('status') == 'error'
        }

        # Calculate pass rate
        pass_rate = status_counts['pass'] / len(results) if results else 0

        # Group results by rule type
        rule_type_results = {}
        for rule_name, rule_config in self.validation_rules.items():
            if rule_name not in results:
                continue

            rule_type = rule_config.get('type', 'unknown')
            if rule_type not in rule_type_results:
                rule_type_results[rule_type] = {
                    'total': 0,
                    'passed': 0,
                    'failed': 0,
                    'errors': 0
                }

            rule_type_results[rule_type]['total'] += 1

            status = results[rule_name].get('status', 'unknown')
            if status == 'pass':
                rule_type_results[rule_type]['passed'] += 1
            elif status == 'fail':
                rule_type_results[rule_type]['failed'] += 1
            elif status == 'error':
                rule_type_results[rule_type]['errors'] += 1

        # Generate report
        report = {
            'status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_rules': len(results),
                'passed': status_counts['pass'],
                'failed': status_counts['fail'],
                'errors': status_counts['error'],
                'pass_rate': pass_rate
            },
            'rule_type_summary': rule_type_results,
            'failed_rules': failed_rules,
            'error_rules': error_rules,
            'details': results
        }

        # Log report summary
        logger.info(f"Validation report generated: {overall_status.upper()} - "
                   f"{status_counts['pass']}/{len(results)} rules passed "
                   f"({pass_rate:.1%} pass rate)")

        if status_counts['fail'] > 0:
            logger.warning(f"Failed rules: {list(failed_rules.keys())}")

        if status_counts['error'] > 0:
            logger.error(f"Rules with errors: {list(error_rules.keys())}")

        return report

    def save_validation_report(self,
                            report: Dict[str, Any],
                            report_path: str) -> bool:
        """
        Save validation report to a JSON file.

        Args:
            report: Validation report
            report_path: Path to save the report

        Returns:
            bool: True if report was saved successfully, False otherwise
        """
        try:
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=4)

            logger.info(f"Saved validation report to {report_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save validation report: {e}")
            return False
