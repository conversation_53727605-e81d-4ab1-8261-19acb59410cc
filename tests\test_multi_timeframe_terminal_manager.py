"""
Test script for the Multi-Timeframe Terminal Manager.
This script tests the basic functionality of the Terminal Manager.
"""
import logging
import time
import os
import sys
from datetime import datetime, timedelta

from trading.mt5_connector import MT5Connector
from trading.terminal_manager import TerminalManager
from model.model_trainer import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator
from config.trading_config import TRADING_CONFIG
from config.credentials import MT5_TERMINALS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/test_terminal_manager.log')
    ]
)
logger = logging.getLogger('test_terminal_manager')

def main():
    """Main function."""
    logger.info("Testing Terminal Manager...")
    
    # Initialize MT5 connector
    mt5_connector = MT5Connector()
    
    # Initialize all MT5 terminals in portable mode
    logger.info("Initializing all MT5 terminals in portable mode...")
    connected_terminals = mt5_connector.connection_manager.initialize_all_terminals()
    logger.info(f"Successfully initialized {len(connected_terminals)} MT5 terminals")
    
    # Initialize components
    data_collector = DataCollector(mt5_connector)
    feature_engineer = FeatureEngineer(data_collector=data_collector)
    model_manager = ModelManager()
    model_evaluator = ModelEvaluator()
    
    # Initialize terminal manager
    terminal_manager = TerminalManager(
        mt5_connector=mt5_connector,
        model_manager=model_manager,
        data_collector=data_collector,
        feature_engineer=feature_engineer,
        model_evaluator=model_evaluator
    )
    
    # Test terminal manager functions
    logger.info("Testing terminal manager functions...")
    
    # Test getting terminal timeframes
    for terminal_id in MT5_TERMINALS:
        timeframe = terminal_manager.get_terminal_timeframe(terminal_id)
        logger.info(f"Terminal {terminal_id} timeframe: {timeframe}")
    
    # Test getting terminal risk profiles
    for terminal_id in MT5_TERMINALS:
        risk_profile = terminal_manager.get_terminal_risk_profile(terminal_id)
        logger.info(f"Terminal {terminal_id} risk profile: {risk_profile}")
    
    # Test getting terminal signal thresholds
    for terminal_id in MT5_TERMINALS:
        thresholds = terminal_manager.get_terminal_signal_thresholds(terminal_id)
        logger.info(f"Terminal {terminal_id} signal thresholds: {thresholds}")
    
    # Test connecting to terminals
    for terminal_id in MT5_TERMINALS:
        success = terminal_manager.connect_terminal(terminal_id)
        logger.info(f"Connected to terminal {terminal_id}: {success}")
    
    # Test getting active terminals
    active_terminals = terminal_manager.get_active_terminals()
    logger.info(f"Active terminals: {active_terminals}")
    
    # Test getting terminal status
    for terminal_id in active_terminals:
        status = terminal_manager.get_terminal_status(terminal_id)
        logger.info(f"Terminal {terminal_id} status: {status}")
    
    logger.info("Terminal Manager tests completed")

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    main()
