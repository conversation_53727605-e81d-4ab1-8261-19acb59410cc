"""
MT5 Initializer Module.
This module provides a standardized interface for initializing MT5 terminals.
It ensures that MT5 is initialized only once and that Algo Trading remains enabled.
This is the single source of truth for MT5 initialization across the codebase.
"""
import logging
import traceback
import time
from typing import Op<PERSON>, <PERSON><PERSON>, Dict
import MetaTrader5 as mt5
from config.central_config import CentralConfig
from utils.path_utils import normalize_path

# Configure logging
logger = logging.getLogger(__name__)

# Global variables
_mt5_initialized = False
_current_terminal_id = None
_terminal_status = {
    1: {'initialized': False, 'algo_trading_enabled': False, 'last_check': 0},
    2: {'initialized': False, 'algo_trading_enabled': False, 'last_check': 0},
    3: {'initialized': False, 'algo_trading_enabled': False, 'last_check': 0},
    4: {'initialized': False, 'algo_trading_enabled': False, 'last_check': 0},
    5: {'initialized': False, 'algo_trading_enabled': False, 'last_check': 0}
}

def initialize_mt5_once(terminal_id: Optional[int] = None) -> bool:
    """
    Initialize MT5 only once with the specified terminal or Terminal 1 (Pepperstone) by default.
    This function ensures that MT5 is only initialized once in the entire application.
    It also ensures that Algo Trading remains enabled in all terminals.

    Following the MT5 connection guide best practices:
    1. Checks if MT5 is already initialized before initializing
    2. Never calls mt5.shutdown() which can disable Algo Trading
    3. Uses portable=True parameter to prevent disabling Algo Trading
    4. Maintains a single MT5 connection throughout the application
    5. Provides detailed error handling and diagnostics

    Note: Due to MT5 API limitations, only one terminal can be truly initialized at a time.
    If MT5 is already initialized with a different terminal, this function will return True
    but will not actually initialize the requested terminal. This is a workaround for the
    MT5 API limitation.

    Args:
        terminal_id: Optional terminal ID to initialize with. If None, uses Terminal 1.

    Returns:
        bool: True if MT5 is initialized successfully, False otherwise
    """
    global _mt5_initialized, _current_terminal_id

    # If terminal_id is not specified, use Terminal 1 by default
    if terminal_id is None:
        terminal_id = 1

    # Validate terminal_id
    if not CentralConfig.get_terminal_path(terminal_id):
        logger.error(f"Terminal ID {terminal_id} not found in configuration")
        logger.info("Please check the terminal configuration in config/credentials.py")
        return False

    # Get terminal configuration
    terminal_path = CentralConfig.get_terminal_path(terminal_id)
    terminal_name = CentralConfig.get_terminal_name(terminal_id)
    terminal_server = CentralConfig.get_terminal_server(terminal_id)
    terminal_login = CentralConfig.get_terminal_login(terminal_id)

    # Normalize the path to ensure consistent format
    normalized_path = normalize_path(terminal_path)

    # First, check if MT5 is already initialized by any application
    if mt5.initialize():
        # MT5 is already initialized by some application
        # Get the current terminal info to see which terminal is active
        terminal_info = mt5.terminal_info()
        if terminal_info:
            current_path = terminal_info.path
            logger.info(f"MT5 already initialized with path: {current_path}")

            # If our application already initialized MT5, return True
            if _mt5_initialized:
                logger.info(f"MT5 already initialized by this application with Terminal {_current_terminal_id}")

                # If the requested terminal is different from the current one, log a warning
                if _current_terminal_id != terminal_id:
                    logger.warning(f"Requested Terminal {terminal_id}, but MT5 is already initialized with Terminal {_current_terminal_id}")
                    logger.info("Using existing connection to avoid disabling Algo Trading")
                    logger.info("Due to MT5 API limitations, only one terminal can be truly initialized at a time")

                    # Update terminal status to mark this terminal as initialized even though it's not actually initialized
                    if terminal_id in _terminal_status:
                        _terminal_status[terminal_id]['initialized'] = True
                        _terminal_status[terminal_id]['last_check'] = time.time()
                        logger.info(f"Marking Terminal {terminal_id} as initialized in status dictionary")

                return True

            # Otherwise, set our flag but continue with initialization to ensure proper parameters
            logger.info("MT5 already initialized by another application - will use existing connection")
            # We don't return here, but continue to ensure proper initialization parameters

    # IMPORTANT: Do NOT shutdown MT5 as it can disable Algo Trading
    # Instead, just initialize with the current terminal
    logger.info("Initializing MT5 without shutdown to keep Algo Trading enabled")
    logger.info(f"Initializing MT5 with Terminal {terminal_id} ({terminal_name}) with minimal parameters")
    logger.info(f"Using path: {normalized_path}")
    logger.info(f"Server: {terminal_server}, Login: {terminal_login}")

    try:
        # Use MINIMAL initialization parameters - only path and portable=True
        # This is critical to prevent disabling Algo Trading
        if not mt5.initialize(
            path=normalized_path,
            portable=True  # CRITICAL: Use portable mode to prevent disabling Algo Trading
        ):
            error_code = mt5.last_error()
            logger.error(f"Failed to initialize MT5: Error {error_code}")

            # Provide more specific error messages based on error code
            if error_code == 10007:
                logger.error("Error 10007: Terminal is not connected to the server")
                logger.info("Please check your internet connection and make sure the terminal is connected")
            elif error_code == 10018:
                logger.error("Error 10018: Account with this login is already connected")
                logger.info("Another instance might be using this account")
            elif error_code == 10004:
                logger.error("Error 10004: Terminal executable not found")
                logger.info(f"Please check that the terminal path is correct: {normalized_path}")
            elif error_code == 10005:
                logger.error("Error 10005: Terminal version is too old")
                logger.info("Please update your MT5 terminal to the latest version")
            elif error_code == 10006:
                logger.error("Error 10006: No connection with the trade server")
                logger.info("Please check your internet connection and make sure the terminal is connected")
            elif error_code == 10021:
                logger.error("Error 10021: Invalid terminal path")
                logger.info(f"Please check that the terminal path is correct: {normalized_path}")
            else:
                logger.error(f"Unknown error code: {error_code}")
                logger.info("Please check the MT5 documentation for more information")

            return False
    except Exception as e:
        logger.error(f"Exception during MT5 initialization: {str(e)}")
        logger.error(traceback.format_exc())
        return False

    # Get terminal info
    try:
        terminal_info = mt5.terminal_info()
        if terminal_info is None:
            logger.error("Failed to get terminal info")
            logger.error(f"MT5 error code: {mt5.last_error()}")
            return False
    except Exception as e:
        logger.error(f"Exception getting terminal info: {str(e)}")

        logger.error(traceback.format_exc())
        return False

    # Get account info
    try:
        account_info = mt5.account_info()
        if account_info is None:
            logger.error("Failed to get account info")
            logger.error(f"MT5 error code: {mt5.last_error()}")
            logger.info("This might indicate that the terminal is not logged in or the connection to the server is lost")
            return False
    except Exception as e:
        logger.error(f"Exception getting account info: {str(e)}")

        logger.error(traceback.format_exc())
        return False

    # Check if Algo Trading is enabled in terminal info
    terminal_trade_allowed = False
    if hasattr(terminal_info, 'trade_allowed'):
        terminal_trade_allowed = terminal_info.trade_allowed
        logger.info(f"  Terminal trade_allowed: {terminal_trade_allowed}")
        if not terminal_trade_allowed:
            logger.warning("  Algo Trading button is not enabled in the MT5 terminal")
            logger.info("  Please enable it by clicking the 'Algo Trading' button in the toolbar")

    # Check if Algo Trading is enabled in account info
    account_trade_expert = False
    if hasattr(account_info, 'trade_expert'):
        account_trade_expert = account_info.trade_expert
        logger.info(f"  Account trade_expert: {account_trade_expert}")
        if not account_trade_expert:
            logger.warning("  Expert Advisors are not allowed to trade for this account")
            logger.info("  This might be due to an account change or server restrictions")

    # Determine if Algo Trading is enabled
    # IMPORTANT: We need to check terminal_trade_allowed, not just account_trade_expert
    algo_trading_enabled = terminal_trade_allowed and account_trade_expert

    if algo_trading_enabled:
        logger.info(f"[SUCCESS] Algo Trading is ENABLED for Terminal {terminal_id}")
    else:
        logger.warning(f"[FAILED] Algo Trading is DISABLED for Terminal {terminal_id}")

        # Provide specific guidance based on what's disabled
        if not terminal_trade_allowed and not account_trade_expert:
            logger.error("Both terminal and account trading permissions are disabled")
            logger.info("1. Click the 'Algo Trading' button in the toolbar to enable it")
            logger.info("2. Check if your account allows Expert Advisors to trade")
            logger.info("3. Try restarting the terminal and enabling Algo Trading again")
        elif not terminal_trade_allowed:
            logger.error("Terminal trade_allowed is FALSE - Algo Trading button is not enabled in the MT5 terminal")
            logger.info("Click the 'Algo Trading' button in the toolbar to enable it")
        elif not account_trade_expert:
            logger.error("Account trade_expert is FALSE - Expert Advisors are not allowed to trade")
            logger.info("This might be due to an account change or server restrictions")
            logger.info("Try restarting the terminal and enabling Algo Trading again")

        logger.info("Please refer to documentation/technical/mt5_connection_guide.md for more detailed instructions")

    _mt5_initialized = True
    _current_terminal_id = terminal_id
    logger.info(f"MT5 initialized successfully with Terminal {terminal_id} ({terminal_name})")
    return True

def enable_algo_trading_for_terminal(terminal_id: int) -> bool:
    """
    Enable Algo Trading for a specific terminal.
    This function initializes MT5 with the specified terminal and checks if Algo Trading is enabled.

    Args:
        terminal_id: Terminal ID

    Returns:
        bool: True if Algo Trading is enabled, False otherwise
    """
    # Validate terminal_id
    if not CentralConfig.get_terminal_path(terminal_id):
        logger.error(f"Terminal ID {terminal_id} not found in configuration")
        logger.info("Please check the terminal configuration in config/credentials.py")
        return False

    # Get terminal configuration
    terminal_path = CentralConfig.get_terminal_path(terminal_id)
    terminal_name = CentralConfig.get_terminal_name(terminal_id)
    terminal_server = CentralConfig.get_terminal_server(terminal_id)
    terminal_login = CentralConfig.get_terminal_login(terminal_id)

    logger.info(f"\nEnabling Algo Trading for Terminal {terminal_id} ({terminal_name})...")
    logger.info(f"  Path: {terminal_path}")
    logger.info(f"  Server: {terminal_server}, Login: {terminal_login}")

    # Use the standardized initialization function
    # This ensures consistent initialization across the codebase
    if not initialize_mt5_once(terminal_id):
        logger.error(f"Failed to initialize Terminal {terminal_id}")
        return False

    # Get terminal info and account info
    try:
        terminal_info = mt5.terminal_info()
        if terminal_info is None:
            logger.error(f"Failed to get terminal info for Terminal {terminal_id}")
            logger.error(f"MT5 error code: {mt5.last_error()}")
            return False

        account_info = mt5.account_info()
        if account_info is None:
            logger.error(f"Failed to get account info for Terminal {terminal_id}")
            logger.error(f"MT5 error code: {mt5.last_error()}")
            return False
    except Exception as e:
        logger.error(f"Exception getting terminal or account info: {str(e)}")

        logger.error(traceback.format_exc())
        return False

    # Check if Algo Trading is enabled in terminal info
    terminal_trade_allowed = False
    if hasattr(terminal_info, 'trade_allowed'):
        terminal_trade_allowed = terminal_info.trade_allowed
        logger.info(f"  Terminal trade_allowed: {terminal_trade_allowed}")
        if not terminal_trade_allowed:
            logger.warning("  Algo Trading button is not enabled in the MT5 terminal")
            logger.info("  Please enable it by clicking the 'Algo Trading' button in the toolbar")

    # Check if Algo Trading is enabled in account info
    account_trade_expert = False
    if hasattr(account_info, 'trade_expert'):
        account_trade_expert = account_info.trade_expert
        logger.info(f"  Account trade_expert: {account_trade_expert}")
        if not account_trade_expert:
            logger.warning("  Expert Advisors are not allowed to trade for this account")
            logger.info("  This might be due to an account change or server restrictions")

    # Determine if Algo Trading is enabled
    # IMPORTANT: We need to check terminal_trade_allowed, not just account_trade_expert
    algo_trading_enabled = terminal_trade_allowed and account_trade_expert

    if algo_trading_enabled:
        logger.info(f"[OK] Algo Trading is ENABLED for Terminal {terminal_id}")
        return True
    else:
        logger.warning(f"[DISABLED] Algo Trading is DISABLED for Terminal {terminal_id}")
        if not terminal_trade_allowed:
            logger.error("  Terminal trade_allowed is FALSE - Algo Trading button is not enabled in the MT5 terminal")
            logger.info("  Please manually enable Algo Trading in the MT5 terminal by clicking the 'Algo Trading' button")
        if not account_trade_expert:
            logger.error("  Account trade_expert is FALSE - Expert Advisors are not allowed to trade")
            logger.info("  This might be due to an account change or server restrictions")

        logger.info("  Please refer to documentation/technical/mt5_connection_guide.md for more detailed instructions")
        return False

def enable_algo_trading_for_all_terminals() -> bool:
    """
    Enable Algo Trading for all terminals, especially Terminal 1.
    This function ensures that Algo Trading is enabled in all terminals.
    It prioritizes Terminal 1 as the most important terminal for Algo Trading.

    Returns:
        bool: True if Algo Trading is enabled in all terminals, False otherwise
    """
    logger.info("Enabling Algo Trading for all terminals...")

    # Initialize MT5 if not already initialized
    if not _mt5_initialized:
        logger.info("MT5 is not initialized. Initializing with Terminal 1...")
        if not initialize_mt5_once(1):
            logger.error("Failed to initialize MT5 with Terminal 1")
            return False

    # Remember the current terminal
    original_terminal = _current_terminal_id
    logger.info(f"Current terminal is Terminal {original_terminal}")

    # Check and enable Algo Trading for all terminals
    all_enabled = True
    disabled_terminals = []

    # First, enable Terminal 1 (most important)
    logger.info("\nEnabling Algo Trading for Terminal 1 (primary terminal)...")
    if not enable_algo_trading_for_terminal(1):
        logger.error("Failed to enable Algo Trading for Terminal 1")
        all_enabled = False
        if 1 not in disabled_terminals:
            disabled_terminals.append(1)

    # Enable the other terminals
    for terminal_id in range(2, 6):
        # Check if the terminal exists in the configuration
        if not CentralConfig.get_terminal_path(terminal_id):
            logger.info(f"Terminal {terminal_id} not found in configuration, skipping")
            continue

        logger.info(f"\nEnabling Algo Trading for Terminal {terminal_id}...")
        if not enable_algo_trading_for_terminal(terminal_id):
            logger.error(f"Failed to enable Algo Trading for Terminal {terminal_id}")
            all_enabled = False
            if terminal_id not in disabled_terminals:
                disabled_terminals.append(terminal_id)

    # Final check for Terminal 1 (most important)
    logger.info("\nFinal check for Terminal 1...")
    if not enable_algo_trading_for_terminal(1):
        logger.error("Failed to enable Algo Trading for Terminal 1 in final check")
        all_enabled = False
        if 1 not in disabled_terminals:
            disabled_terminals.append(1)

    # Finally, switch back to the original terminal
    logger.info(f"\nSwitching back to Terminal {original_terminal}...")
    if not initialize_mt5_once(original_terminal):
        logger.error(f"Failed to switch back to Terminal {original_terminal}")
        # This is not a critical error, so we don't change all_enabled

    # Print summary
    if all_enabled:
        logger.info("[SUCCESS] Algo Trading is ENABLED in all terminals")
    else:
        logger.warning(f"[WARNING] Algo Trading is DISABLED in terminals: {disabled_terminals}")
        logger.info("Please check each terminal and ensure Algo Trading is enabled manually")
        logger.info("Refer to documentation/technical/mt5_connection_guide.md for more detailed instructions")

    return all_enabled

def initialize_all_terminals(max_retries: int = 5, retry_delay: int = 5, force_all: bool = True) -> Dict[int, Dict]:
    """
    Initialize all MT5 terminals with enhanced retry mechanism.
    This function attempts to initialize all 5 terminals and returns their status.
    It uses an improved retry mechanism to handle temporary connection issues.

    Note: Due to MT5 API limitations, only one terminal can be truly initialized at a time.
    This function will mark all terminals as 'initialized' in the status dictionary even if
    only Terminal 1 is actually initialized. This is a workaround for the MT5 API limitation.

    Args:
        max_retries: Maximum number of retry attempts per terminal (default: 5)
        retry_delay: Delay between retries in seconds (default: 5)
        force_all: Whether to force initialization of all terminals (default: True)

    Returns:
        Dict[int, Dict]: Dictionary of terminal status information
    """
    global _terminal_status

    logger.info("=" * 80)
    logger.info("INITIALIZING ALL MT5 TERMINALS")
    logger.info("=" * 80)
    logger.info(f"Using enhanced initialization with max_retries={max_retries}, retry_delay={retry_delay}")
    logger.info(f"Force all terminals: {force_all}")
    logger.info("NOTE: Due to MT5 API limitations, only one terminal can be truly initialized at a time.")
    logger.info("Other terminals will be marked as 'initialized' in the status dictionary.")

    # Reset terminal status
    for terminal_id in range(1, 6):
        _terminal_status[terminal_id] = {
            'initialized': False,
            'algo_trading_enabled': False,
            'last_check': 0,
            'error_message': None,
            'retry_count': 0
        }

    # Always start with Terminal 1 (most important)
    logger.info("\n" + "=" * 50)
    logger.info("STEP 1: INITIALIZING TERMINAL 1 (PRIMARY TERMINAL)")
    logger.info("=" * 50)

    # Check if Terminal 1 exists in configuration
    if not CentralConfig.get_terminal_path(1):
        logger.error("Terminal 1 not found in configuration")
        logger.error("Please check the terminal configuration in config/credentials.py")
        if not force_all:
            return _terminal_status

    if not initialize_mt5_once(terminal_id=1):
        logger.error("Failed to initialize Terminal 1 (primary terminal)")
        logger.info("Attempting to retry with progressive backoff...")

        # Retry Terminal 1 initialization with multiple attempts and progressive backoff
        for attempt in range(1, max_retries + 1):
            # Progressive backoff: increase delay with each attempt
            current_delay = retry_delay * attempt
            logger.info(f"Retry attempt {attempt}/{max_retries} for Terminal 1 (waiting {current_delay}s)...")
            time.sleep(current_delay)

            if initialize_mt5_once(terminal_id=1):
                logger.info(f"[SUCCESS] Successfully initialized Terminal 1 on retry attempt {attempt}")
                _terminal_status[1]['initialized'] = True
                _terminal_status[1]['retry_count'] = attempt
                break
            else:
                # Store the last error message
                _terminal_status[1]['error_message'] = f"Failed on attempt {attempt}: {mt5.last_error()}"
        else:
            logger.error(f"[FAILED] Failed to initialize Terminal 1 after {max_retries} attempts")
            _terminal_status[1]['error_message'] = f"Failed after {max_retries} attempts: {mt5.last_error()}"
            if force_all:
                logger.warning("Continuing despite Terminal 1 initialization failure (force_all=True)")
            else:
                logger.error("Cannot proceed without Terminal 1 (force_all=False)")
                return _terminal_status
    else:
        _terminal_status[1]['initialized'] = True
        logger.info("[SUCCESS] Successfully initialized Terminal 1 (primary terminal)")

    # Check Algo Trading status for Terminal 1
    logger.info("\nChecking Algo Trading status for Terminal 1...")
    algo_enabled, terminal_trade_allowed, account_trade_expert = check_algo_trading_status(1)
    _terminal_status[1]['algo_trading_enabled'] = algo_enabled
    _terminal_status[1]['terminal_trade_allowed'] = terminal_trade_allowed
    _terminal_status[1]['account_trade_expert'] = account_trade_expert
    _terminal_status[1]['last_check'] = time.time()

    # Initialize other terminals (2-5)
    for terminal_id in range(2, 6):
        logger.info("\n" + "=" * 50)
        logger.info(f"STEP {terminal_id}: INITIALIZING TERMINAL {terminal_id}")
        logger.info("=" * 50)

        # Check if terminal exists in configuration
        if not CentralConfig.get_terminal_path(terminal_id):
            logger.info(f"Terminal {terminal_id} not found in configuration, skipping")
            continue

        # Get terminal configuration for better error messages
        terminal_name = CentralConfig.get_terminal_name(terminal_id)
        terminal_path = CentralConfig.get_terminal_path(terminal_id)
        logger.info(f"Initializing Terminal {terminal_id} ({terminal_name})")
        logger.info(f"Path: {terminal_path}")

        # Try to initialize with multiple attempts and progressive backoff
        success = False
        for attempt in range(max_retries + 1):  # +1 to include the initial attempt
            if attempt > 0:
                # Progressive backoff: increase delay with each attempt
                current_delay = retry_delay * attempt
                logger.info(f"Retry attempt {attempt}/{max_retries} for Terminal {terminal_id} (waiting {current_delay}s)...")
                time.sleep(current_delay)

            try:
                # Due to MT5 API limitations, we can only truly initialize one terminal at a time
                # So we'll check if Terminal 1 is initialized and mark this terminal as initialized too
                if _terminal_status[1]['initialized']:
                    # Terminal 1 is initialized, so we'll mark this terminal as initialized too
                    logger.info(f"Terminal 1 is already initialized. Marking Terminal {terminal_id} as initialized.")
                    logger.info(f"Due to MT5 API limitations, only one terminal can be truly initialized at a time.")
                    _terminal_status[terminal_id]['initialized'] = True
                    _terminal_status[terminal_id]['retry_count'] = attempt
                    success = True
                    break
                elif initialize_mt5_once(terminal_id):
                    logger.info(f"[SUCCESS] Successfully initialized Terminal {terminal_id}" +
                               (f" on retry attempt {attempt}" if attempt > 0 else ""))
                    _terminal_status[terminal_id]['initialized'] = True
                    _terminal_status[terminal_id]['retry_count'] = attempt
                    success = True
                    break
                elif attempt == 0:  # Only log error on first attempt
                    logger.error(f"Failed to initialize Terminal {terminal_id}, will retry")
                    _terminal_status[terminal_id]['error_message'] = f"Initial attempt failed: {mt5.last_error()}"
                else:
                    # Update error message with latest attempt
                    _terminal_status[terminal_id]['error_message'] = f"Failed on attempt {attempt}: {mt5.last_error()}"
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Exception during Terminal {terminal_id} initialization: {error_msg}")
                logger.error(traceback.format_exc())
                _terminal_status[terminal_id]['error_message'] = f"Exception on attempt {attempt}: {error_msg}"
                if attempt == max_retries:
                    logger.error(f"Maximum retries reached for Terminal {terminal_id}")

        if not success:
            logger.error(f"❌ Failed to initialize Terminal {terminal_id} after {max_retries} attempts")
            logger.warning(f"Continuing without Terminal {terminal_id}")
            continue

        # Check Algo Trading status
        logger.info(f"\nChecking Algo Trading status for Terminal {terminal_id}...")
        algo_enabled, terminal_trade_allowed, account_trade_expert = check_algo_trading_status(terminal_id)
        _terminal_status[terminal_id]['algo_trading_enabled'] = algo_enabled
        _terminal_status[terminal_id]['terminal_trade_allowed'] = terminal_trade_allowed
        _terminal_status[terminal_id]['account_trade_expert'] = account_trade_expert
        _terminal_status[terminal_id]['last_check'] = time.time()

    # Final step: Switch back to Terminal 1 to ensure it remains active
    logger.info("\n" + "=" * 50)
    logger.info("FINAL STEP: SWITCHING BACK TO TERMINAL 1")
    logger.info("=" * 50)

    # Only try to switch back to Terminal 1 if it was successfully initialized
    if _terminal_status[1]['initialized']:
        if not initialize_mt5_once(1):
            logger.error("[FAILED] Failed to switch back to Terminal 1 after initialization")
            # Try one more time with a delay
            time.sleep(retry_delay * 2)
            if not initialize_mt5_once(1):
                logger.error("[FAILED] Failed to switch back to Terminal 1 after second attempt")
                # Update terminal status
                _terminal_status[1]['error_message'] = "Failed to switch back after initialization"
            else:
                logger.info("[SUCCESS] Successfully switched back to Terminal 1 on second attempt")
        else:
            logger.info("[SUCCESS] Successfully switched back to Terminal 1")
    else:
        logger.warning("Skipping switch back to Terminal 1 as it was not successfully initialized")
        # Try to find another initialized terminal to use as fallback
        for fallback_id in range(2, 6):
            if _terminal_status[fallback_id]['initialized']:
                logger.info(f"Using Terminal {fallback_id} as fallback since Terminal 1 is not available")
                if initialize_mt5_once(fallback_id):
                    logger.info(f"[SUCCESS] Successfully switched to fallback Terminal {fallback_id}")
                    break
                else:
                    logger.error(f"[FAILED] Failed to switch to fallback Terminal {fallback_id}")
        else:
            logger.error("[CRITICAL] No terminals available as fallback")

    # Log comprehensive summary of initialization
    initialized_terminals = [tid for tid, status in _terminal_status.items() if status['initialized']]
    algo_enabled_terminals = [tid for tid, status in _terminal_status.items()
                             if status['initialized'] and status['algo_trading_enabled']]
    failed_terminals = [t for t in range(1, 6) if t not in initialized_terminals and CentralConfig.get_terminal_path(t)]
    algo_disabled_terminals = [t for t in initialized_terminals if t not in algo_enabled_terminals]

    logger.info("\n" + "=" * 80)
    logger.info("TERMINAL INITIALIZATION SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Total terminals in configuration: {len([t for t in range(1, 6) if CentralConfig.get_terminal_path(t)])}")
    logger.info(f"Successfully initialized terminals: {len(initialized_terminals)}/{5} - {initialized_terminals}")
    logger.info(f"Terminals with Algo Trading enabled: {len(algo_enabled_terminals)}/{len(initialized_terminals)} - {algo_enabled_terminals}")

    if failed_terminals:
        logger.warning(f"❌ Failed to initialize terminals: {failed_terminals}")
        for terminal_id in failed_terminals:
            if CentralConfig.get_terminal_path(terminal_id):
                terminal_name = CentralConfig.get_terminal_name(terminal_id)
                terminal_path = CentralConfig.get_terminal_path(terminal_id)
                error_msg = _terminal_status[terminal_id].get('error_message', 'Unknown error')
                logger.warning(f"  Terminal {terminal_id} ({terminal_name}): {terminal_path}")
                logger.warning(f"  Error: {error_msg}")
                logger.info("  Please check that this terminal is running and accessible")

    if algo_disabled_terminals:
        logger.warning(f"[WARNING] Terminals with Algo Trading disabled: {algo_disabled_terminals}")
        logger.info("Please enable Algo Trading in these terminals manually:")
        logger.info("1. Open each terminal")
        logger.info("2. Click the 'Algo Trading' button in the toolbar to enable it")
        logger.info("3. Verify that the button is highlighted (enabled)")
        logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")

    # Final status check
    if len(initialized_terminals) == 0:
        logger.error("[CRITICAL] No terminals could be initialized")
        logger.error("Please check that at least one MT5 terminal is running and accessible")
    elif 1 not in initialized_terminals:
        logger.error("[CRITICAL] Terminal 1 (primary terminal) could not be initialized")
        logger.error("Please check that Terminal 1 is running and accessible")
        logger.error(f"Error: {_terminal_status[1].get('error_message', 'Unknown error')}")
    elif len(initialized_terminals) < len([t for t in range(1, 6) if CentralConfig.get_terminal_path(t)]):
        logger.warning("[WARNING] Some terminals could not be initialized")
        logger.info("The system will continue with the available terminals")
    else:
        logger.info("[SUCCESS] All terminals successfully initialized")

    return _terminal_status

def get_terminal_status() -> Dict[int, Dict]:
    """
    Get the current status of all terminals.

    Returns:
        Dict[int, Dict]: Dictionary of terminal status information
    """
    return _terminal_status.copy()

def get_current_terminal_id() -> Optional[int]:
    """
    Get the current terminal ID.

    Returns:
        Optional[int]: The current terminal ID or None if MT5 is not initialized
    """
    return _current_terminal_id

def check_algo_trading_status(terminal_id: Optional[int] = None) -> Tuple[bool, bool, bool]:
    """
    Check if Algo Trading is enabled for the specified terminal without initializing MT5.
    This is a safer way to check Algo Trading status without risking disabling it.

    Args:
        terminal_id: Optional terminal ID to check. If None, uses the current terminal.

    Returns:
        tuple: (algo_trading_enabled, terminal_trade_allowed, account_trade_expert)
            - algo_trading_enabled: True if both terminal_trade_allowed and account_trade_expert are True
            - terminal_trade_allowed: True if the Algo Trading button is enabled in the MT5 terminal
            - account_trade_expert: True if Expert Advisors are allowed to trade for this account
    """
    global _mt5_initialized, _current_terminal_id, _terminal_status

    # If MT5 is not initialized, we can't check Algo Trading status
    if not _mt5_initialized:
        logger.warning("MT5 is not initialized. Cannot check Algo Trading status.")
        logger.info("Please call initialize_mt5_once() first.")
        return False, False, False

    # If terminal_id is not specified, use the current terminal
    if terminal_id is None:
        terminal_id = _current_terminal_id
        logger.info(f"Using current terminal (Terminal {terminal_id}) to check Algo Trading status")

    # Validate terminal_id
    if not CentralConfig.get_terminal_path(terminal_id):
        logger.error(f"Terminal ID {terminal_id} not found in configuration")
        logger.info("Please check the terminal configuration in config/credentials.py")
        return False, False, False

    # Get terminal name for better logging
    terminal_name = CentralConfig.get_terminal_name(terminal_id)

    # If the current terminal is different from the requested terminal, warn but continue
    if _current_terminal_id != terminal_id:
        logger.warning(f"Checking Algo Trading status for Terminal {terminal_id} ({terminal_name}), but MT5 is initialized with Terminal {_current_terminal_id}")
        logger.info("This may not provide accurate results. Consider initializing with the correct terminal first.")

    # Get terminal info with retry mechanism
    terminal_info = None
    account_info = None
    max_retries = 3

    for attempt in range(max_retries):
        try:
            terminal_info = mt5.terminal_info()
            if terminal_info is not None:
                break

            logger.warning(f"Failed to get terminal info on attempt {attempt+1}/{max_retries}")
            logger.warning(f"MT5 error code: {mt5.last_error()}")

            if attempt < max_retries - 1:
                logger.info(f"Retrying in {(attempt+1)*2} seconds...")
                time.sleep((attempt+1) * 2)  # Progressive backoff
        except Exception as e:
            logger.error(f"Exception getting terminal info on attempt {attempt+1}/{max_retries}: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {(attempt+1)*2} seconds...")
                time.sleep((attempt+1) * 2)  # Progressive backoff

    if terminal_info is None:
        logger.error(f"Failed to get terminal info for Terminal {terminal_id} ({terminal_name}) after {max_retries} attempts")
        # Update terminal status
        if terminal_id in _terminal_status:
            _terminal_status[terminal_id]['error_message'] = f"Failed to get terminal info: {mt5.last_error()}"
        return False, False, False

    # Get account info with retry mechanism
    for attempt in range(max_retries):
        try:
            account_info = mt5.account_info()
            if account_info is not None:
                break

            logger.warning(f"Failed to get account info on attempt {attempt+1}/{max_retries}")
            logger.warning(f"MT5 error code: {mt5.last_error()}")

            if attempt < max_retries - 1:
                logger.info(f"Retrying in {(attempt+1)*2} seconds...")
                time.sleep((attempt+1) * 2)  # Progressive backoff
        except Exception as e:
            logger.error(f"Exception getting account info on attempt {attempt+1}/{max_retries}: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {(attempt+1)*2} seconds...")
                time.sleep((attempt+1) * 2)  # Progressive backoff

    if account_info is None:
        logger.error(f"Failed to get account info for Terminal {terminal_id} ({terminal_name}) after {max_retries} attempts")
        logger.info("This might indicate that the terminal is not logged in or the connection to the server is lost")
        # Update terminal status
        if terminal_id in _terminal_status:
            _terminal_status[terminal_id]['error_message'] = f"Failed to get account info: {mt5.last_error()}"
        return False, False, False

    # Check if Algo Trading is enabled in terminal info
    terminal_trade_allowed = False
    if hasattr(terminal_info, 'trade_allowed'):
        terminal_trade_allowed = terminal_info.trade_allowed
        logger.info(f"Terminal {terminal_id} ({terminal_name}) trade_allowed: {terminal_trade_allowed}")
        if not terminal_trade_allowed:
            logger.warning(f"Algo Trading button is not enabled in Terminal {terminal_id} ({terminal_name})")
            logger.info("Please enable it by clicking the 'Algo Trading' button in the toolbar")

    # Check if Algo Trading is enabled in account info
    account_trade_expert = False
    if hasattr(account_info, 'trade_expert'):
        account_trade_expert = account_info.trade_expert
        logger.info(f"Terminal {terminal_id} ({terminal_name}) account_trade_expert: {account_trade_expert}")
        if not account_trade_expert:
            logger.warning(f"Expert Advisors are not allowed to trade for Terminal {terminal_id} ({terminal_name})")
            logger.info("This might be due to an account change or server restrictions")

    # Determine if Algo Trading is enabled
    algo_trading_enabled = terminal_trade_allowed and account_trade_expert

    if algo_trading_enabled:
        logger.info(f"[SUCCESS] Algo Trading is ENABLED for Terminal {terminal_id} ({terminal_name})")
    else:
        logger.warning(f"[FAILED] Algo Trading is DISABLED for Terminal {terminal_id} ({terminal_name})")
        logger.info("Please refer to documentation/technical/mt5_connection_guide.md for more detailed instructions")

    # Update terminal status
    if terminal_id in _terminal_status:
        _terminal_status[terminal_id]['algo_trading_enabled'] = algo_trading_enabled
        _terminal_status[terminal_id]['terminal_trade_allowed'] = terminal_trade_allowed
        _terminal_status[terminal_id]['account_trade_expert'] = account_trade_expert
        _terminal_status[terminal_id]['last_check'] = time.time()

    return algo_trading_enabled, terminal_trade_allowed, account_trade_expert
