"""
PyTorch Security Utilities.
This module provides utilities for working with PyTorch's security features.
"""
import logging
import torch
from typing import Optional

# Configure logger
logger = logging.getLogger('utils.torch_security')

def register_safe_classes() -> bool:
    """
    Register custom classes as safe globals for PyTorch 2.6+.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Import required modules
        from torch.serialization import add_safe_globals
        import numpy as np

        # Import custom classes that need to be registered
        from model.lstm_model import TradingLSTM
        from torch.nn.modules.rnn import LSTM
        from torch.nn.modules.dropout import Dropout
        from torch.nn.modules.linear import Linear

        # Register classes
        add_safe_globals([
            TradingLSTM,
            LSTM,
            Dropout,
            Linear,
            np.ndarray,
            np.dtype
        ])

        logger.info("Successfully registered custom classes as PyTorch safe globals")
        return True
    except ImportError as e:
        logger.warning(f"Could not register safe globals (older PyTorch version or missing classes): {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error registering safe globals: {str(e)}")
        return False

def safe_load(filepath: str, map_location: Optional[torch.device] = None, device: Optional[torch.device] = None) -> dict:
    """
    Safely load a PyTorch model using the new security model.

    Args:
        filepath: Path to the model file
        device: Device to load the model to (default: None, uses CPU)

    Returns:
        dict: Loaded checkpoint

    Raises:
        FileNotFoundError: If the file does not exist
        RuntimeError: If the file cannot be loaded
    """
    # Register safe classes
    register_safe_classes()

    # Set device (prioritize map_location for compatibility)
    if map_location is not None:
        device = map_location
    elif device is None:
        device = torch.device('cpu')

    try:
        # Import required modules
        try:
            # PyTorch 2.6+ approach with safe_globals context manager
            from torch.serialization import safe_globals
            import numpy as np
            from model.lstm_model import TradingLSTM
            from torch.nn.modules.rnn import LSTM
            from torch.nn.modules.dropout import Dropout
            from torch.nn.modules.linear import Linear

            # Load with safe globals
            with safe_globals([
                TradingLSTM,
                LSTM,
                Dropout,
                Linear,
                np.ndarray,
                np.dtype
            ]):
                checkpoint = torch.load(filepath, map_location=device)
                logger.info(f"Successfully loaded model from {filepath} using safe_globals context manager")
        except ImportError:
            # Fallback for older PyTorch versions or if safe_globals is not available
            logger.warning("safe_globals not available. Using fallback method for loading model (weights_only=False)")
            checkpoint = torch.load(filepath, map_location=device, weights_only=False)
            logger.info(f"Successfully loaded model from {filepath} using weights_only=False")
        except Exception as e:
            # If safe_globals fails, try with weights_only=False
            logger.warning(f"Safe globals failed: {str(e)}. Trying with weights_only=False")
            checkpoint = torch.load(filepath, map_location=device, weights_only=False)
            logger.info(f"Successfully loaded model from {filepath} using weights_only=False after safe_globals failed")

        return checkpoint
    except Exception as e:
        logger.error(f"Error loading model from {filepath}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise RuntimeError(f"Failed to load model: {str(e)}")

def safe_save(checkpoint: dict, filepath: str) -> bool:
    """
    Safely save a PyTorch model using the new security model.

    Args:
        checkpoint: Model checkpoint to save
        filepath: Path to save the model to

    Returns:
        bool: True if successful, False otherwise
    """
    # Register safe classes
    register_safe_classes()

    try:
        # Import required modules
        try:
            # PyTorch 2.6+ approach with safe_globals context manager
            from torch.serialization import safe_globals
            import numpy as np
            from model.lstm_model import TradingLSTM
            from torch.nn.modules.rnn import LSTM
            from torch.nn.modules.dropout import Dropout
            from torch.nn.modules.linear import Linear

            # Save with safe globals
            with safe_globals([
                TradingLSTM,
                LSTM,
                Dropout,
                Linear,
                np.ndarray,
                np.dtype
            ]):
                torch.save(checkpoint, filepath)
                logger.info(f"Successfully saved model to {filepath} using safe_globals context manager")
        except ImportError:
            # Fallback for older PyTorch versions or if safe_globals is not available
            logger.warning("safe_globals not available. Using fallback method for saving model")
            torch.save(checkpoint, filepath)
            logger.info(f"Successfully saved model to {filepath} using standard torch.save")
        except Exception as e:
            # If safe_globals fails, try without it
            logger.warning(f"Safe globals failed: {str(e)}. Trying without safe globals")
            torch.save(checkpoint, filepath)
            logger.info(f"Successfully saved model to {filepath} using standard torch.save after safe_globals failed")

        return True
    except Exception as e:
        logger.error(f"Error saving model to {filepath}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False
