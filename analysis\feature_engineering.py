"""
Feature Engineering Module.
This module handles feature engineering for the trading bot.
"""
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
import pandas as pd
import numpy as np
from config.trading_config import TRADING_CONFIG
from data.data_collector import DataCollector  # Import for type hints only

# Configure logger
logger = logging.getLogger('analysis.feature_engineering')

class FeatureEngineer:
    """
    Feature Engineer class for creating features for the trading model.
    """
    def __init__(self, data_collector: Optional['DataCollector'] = None):
        """Initialize FeatureEngineer."""
        self.feature_config = TRADING_CONFIG['features']
        self.price_features = self.feature_config['price_features']
        self.technical_indicators = self.feature_config['technical_indicators']
        self.data_collector = data_collector
        self.auto_save = TRADING_CONFIG.get('auto_save_features', True)  # Auto-save features flag

    def create_features(self, df: pd.DataFrame, use_parallel: bool = False, symbol: str = None) -> pd.DataFrame:
        """
        Create features for the model.

        Args:
            df: DataFrame with OHLCV data
            use_parallel: Whether to use parallel processing for feature calculation (faster for large datasets)
            symbol: Symbol being processed (for auto-save features)

        Returns:
            pd.DataFrame: DataFrame with features
        """
        try:
            # Validate input
            if df is None or len(df) == 0:
                logger.error("Input DataFrame is empty or None")
                return pd.DataFrame()

            # Check if required columns exist
            required_columns = ['open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return pd.DataFrame()

            # Make a copy to avoid modifying the original
            df_features = df.copy()

            # Log the initial data size
            logger.debug(f"Initial data size: {len(df_features)}")

            # For large datasets, use parallel processing
            if use_parallel and len(df) > 50000:
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                    # Submit tasks
                    price_future = executor.submit(self._add_price_features, df_features.copy())

                    # Wait for price features to complete before calculating indicators
                    # (since indicators depend on price features)
                    price_df = price_future.result()

                    # Now calculate indicators in parallel
                    indicators_future = executor.submit(self._add_simplified_indicators, price_df)

                    # Get results
                    df_features = indicators_future.result()

                logger.info(f"Parallel feature calculation completed for {len(df_features)} rows")
            else:
                # Sequential processing for smaller datasets
                # Add price-based features
                df_features = self._add_price_features(df_features)
                logger.debug(f"Data size after adding price features: {len(df_features.dropna())}")

                # Add technical indicators (simplified set)
                df_features = self._add_simplified_indicators(df_features)
                logger.debug(f"Data size after adding technical indicators: {len(df_features.dropna())}")

            # Handle NaN values efficiently
            # First try forward fill which is faster
            df_features = df_features.ffill()

            # Then check if we still have NaNs and only do more expensive operations if needed
            if df_features.isna().any().any():
                df_features = df_features.bfill().fillna(0)

            logger.debug(f"Data size after handling NaN values: {len(df_features)}")

            # Auto-save features if enabled and data_collector is available
            # Do this in a separate thread for large datasets
            if self.auto_save and self.data_collector is not None:
                try:
                    # Extract timeframe from the index frequency if available
                    timeframe = self._determine_timeframe(df_features)

                    # If timeframe is determined, save features
                    if timeframe is not None:
                        # For large datasets, save in a background thread
                        if len(df_features) > 100000:
                            import threading
                            save_thread = threading.Thread(
                                target=self._save_features_thread,
                                args=(df_features.copy(), timeframe, symbol)
                            )
                            save_thread.daemon = True
                            save_thread.start()
                            logger.info(f"Saving features for timeframe {timeframe} in background thread")
                        else:
                            self.data_collector.save_features_to_storage(df_features, timeframe, symbol=symbol)
                            logger.info(f"Auto-saved features for timeframe {timeframe}")
                    else:
                        logger.warning("Could not determine timeframe for auto-saving features")
                except Exception as e:
                    logger.error(f"Failed to auto-save features: {str(e)}")
                    import traceback
                    logger.debug(traceback.format_exc())

            return df_features

        except Exception as e:
            logger.error(f"Error creating features: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return pd.DataFrame()

    def _determine_timeframe(self, df: pd.DataFrame) -> str:
        """
        Determine the timeframe from the DataFrame index.

        Args:
            df: DataFrame with datetime index

        Returns:
            str: Timeframe code or None if couldn't be determined
        """
        # Extract timeframe from the index frequency if available
        if hasattr(df.index, 'freq') and df.index.freq is not None:
            freq = df.index.freq
            if freq.name == 'M5':
                return 'M5'
            elif freq.name == 'M15':
                return 'M15'
            elif freq.name == 'M30':
                return 'M30'
            elif freq.name == 'H1':
                return 'H1'
            elif freq.name == 'H4':
                return 'H4'

        # If timeframe couldn't be determined, try to infer from the data
        # Check time difference between consecutive rows
        if len(df) > 1:
            time_diff = (df.index[1] - df.index[0]).total_seconds() / 60
            if 4 <= time_diff <= 6:
                return 'M5'
            elif 14 <= time_diff <= 16:
                return 'M15'
            elif 29 <= time_diff <= 31:
                return 'M30'
            elif 59 <= time_diff <= 61:
                return 'H1'
            elif 239 <= time_diff <= 241:
                return 'H4'

        return None

    def _save_features_thread(self, df: pd.DataFrame, timeframe: str, symbol: str = None):
        """
        Save features in a background thread.

        Args:
            df: DataFrame with features
            timeframe: Timeframe code
            symbol: Symbol being processed
        """
        try:
            self.data_collector.save_features_to_storage(df, timeframe, symbol=symbol)
            logger.info(f"Background save completed for features ({timeframe})")
        except Exception as e:
            logger.error(f"Error in background save thread: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _add_simplified_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add simplified technical indicators.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with technical indicators
        """
        # Simple Moving Averages
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['sma_200'] = df['close'].rolling(window=200).mean()

        # Exponential Moving Averages
        df['ema_12'] = df['close'].ewm(span=12, adjust=False).mean()
        df['ema_26'] = df['close'].ewm(span=26, adjust=False).mean()

        # Relative Strength Index (RSI)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi_14'] = 100 - (100 / (1 + rs))

        # Bollinger Bands
        df['bb_middle_20'] = df['close'].rolling(window=20).mean()
        df['bb_std_20'] = df['close'].rolling(window=20).std()
        df['bb_upper_20'] = df['bb_middle_20'] + 2 * df['bb_std_20']
        df['bb_lower_20'] = df['bb_middle_20'] - 2 * df['bb_std_20']
        df['bb_width_20'] = (df['bb_upper_20'] - df['bb_lower_20']) / df['bb_middle_20']
        df['bb_position_20'] = (df['close'] - df['bb_lower_20']) / (df['bb_upper_20'] - df['bb_lower_20'])

        # MACD
        df['macd_line'] = df['ema_12'] - df['ema_26']
        df['macd_signal_9'] = df['macd_line'].ewm(span=9, adjust=False).mean()
        df['macd_hist'] = df['macd_line'] - df['macd_signal_9']

        # Average True Range (ATR)
        high = df['high']
        low = df['low']
        close = df['close'].shift(1)
        tr1 = high - low
        tr2 = abs(high - close)
        tr3 = abs(low - close)
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        df['atr_14'] = tr.rolling(window=14).mean()

        # Add normalized ATR (ATR as percentage of price)
        df['atr_pct_14'] = df['atr_14'] / df['close']

        # Add Stochastic Oscillator
        df['stoch_k_14'] = 100 * (df['close'] - df['low'].rolling(window=14).min()) / (df['high'].rolling(window=14).max() - df['low'].rolling(window=14).min())
        df['stoch_d_14'] = df['stoch_k_14'].rolling(window=3).mean()

        # Add Rate of Change
        df['roc_10'] = ((df['close'] / df['close'].shift(10)) - 1) * 100

        # Add Commodity Channel Index (CCI)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        mean_deviation = abs(typical_price - typical_price.rolling(window=20).mean()).rolling(window=20).mean()
        df['cci_20'] = (typical_price - typical_price.rolling(window=20).mean()) / (0.015 * mean_deviation)

        # Add advanced features for better prediction
        self._add_advanced_features(df)

        return df

    def _add_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add advanced features for improved model performance.

        Args:
            df: DataFrame with OHLCV data and basic indicators

        Returns:
            pd.DataFrame: DataFrame with advanced features
        """
        # Add price momentum features
        df['momentum_close_5'] = df['close'] - df['close'].shift(5)
        df['momentum_close_10'] = df['close'] - df['close'].shift(10)

        # Add price acceleration
        df['acceleration_5'] = df['momentum_close_5'] - df['momentum_close_5'].shift(5)

        # Add moving average convergence/divergence features
        if 'sma_20' in df.columns and 'sma_50' in df.columns:
            df['ma_convergence_20_50'] = df['sma_20'] - df['sma_50']
            df['ma_convergence_pct_20_50'] = (df['sma_20'] / df['sma_50'] - 1) * 100

        # Add volatility ratio features
        if 'volatility_5' in df.columns and 'volatility_20' in df.columns:
            df['volatility_ratio_5_20'] = df['volatility_5'] / df['volatility_20']

        # Add RSI divergence
        if 'rsi_14' in df.columns:
            df['rsi_diff_5'] = df['rsi_14'] - df['rsi_14'].shift(5)

        # Add MACD acceleration
        if 'macd_hist' in df.columns:
            df['macd_hist_diff'] = df['macd_hist'] - df['macd_hist'].shift(1)

        # Add Bollinger Band squeeze indicator
        if 'bb_width_20' in df.columns:
            df['bb_squeeze'] = df['bb_width_20'].rolling(window=20).mean() / df['bb_width_20']

        # Add high-low range relative to ATR
        if 'atr_14' in df.columns:
            df['range_atr_ratio'] = (df['high'] - df['low']) / df['atr_14']

        # Add distance from moving averages
        for ma in ['sma_20', 'sma_50', 'sma_200']:
            if ma in df.columns:
                df[f'dist_from_{ma}'] = (df['close'] - df[ma]) / df[ma] * 100

        # Add crossover signals
        if 'sma_20' in df.columns and 'sma_50' in df.columns:
            df['cross_20_50'] = np.where(
                (df['sma_20'].shift(1) <= df['sma_50'].shift(1)) &
                (df['sma_20'] > df['sma_50']),
                1, np.where(
                    (df['sma_20'].shift(1) >= df['sma_50'].shift(1)) &
                    (df['sma_20'] < df['sma_50']),
                    -1, 0
                )
            )

        # Add price patterns
        df['higher_high'] = np.where(
            (df['high'] > df['high'].shift(1)) &
            (df['high'].shift(1) > df['high'].shift(2)),
            1, 0
        )

        df['lower_low'] = np.where(
            (df['low'] < df['low'].shift(1)) &
            (df['low'].shift(1) < df['low'].shift(2)),
            1, 0
        )

        # Add volume-based features if volume is available
        if 'volume' in df.columns:
            # Volume-price relationship
            df['volume_price_trend'] = df['volume'] * ((df['close'] - df['close'].shift(1)) / df['close'].shift(1))
            df['volume_price_trend_ma'] = df['volume_price_trend'].rolling(window=20).mean()

            # Money Flow Index
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            money_flow = typical_price * df['volume']

            positive_flow = np.where(typical_price > typical_price.shift(1), money_flow, 0)
            negative_flow = np.where(typical_price < typical_price.shift(1), money_flow, 0)

            positive_mf = pd.Series(positive_flow).rolling(window=14).sum()
            negative_mf = pd.Series(negative_flow).rolling(window=14).sum()

            money_ratio = positive_mf / negative_mf
            df['mfi_14'] = 100 - (100 / (1 + money_ratio))

        # Add time-based features
        if isinstance(df.index, pd.DatetimeIndex):
            df['hour'] = df.index.hour
            df['day_of_week'] = df.index.dayofweek
            df['is_weekend'] = np.where(df['day_of_week'] >= 5, 1, 0)

        return df

    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add price-based features.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with price features
        """
        # Calculate returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # Calculate price differences
        df['price_diff'] = df['close'] - df['open']
        df['high_low_diff'] = df['high'] - df['low']
        df['close_open_ratio'] = df['close'] / df['open']

        # Calculate candle features
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
        df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
        df['body_to_range_ratio'] = df['body_size'] / (df['high'] - df['low'])

        # Calculate normalized prices
        df['norm_open'] = df['open'] / df['close'].shift(1)
        df['norm_high'] = df['high'] / df['close'].shift(1)
        df['norm_low'] = df['low'] / df['close'].shift(1)
        df['norm_close'] = df['close'] / df['close'].shift(1)

        return df

    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with technical indicators
        """
        # Add moving averages
        for indicator in self.technical_indicators:
            if indicator['name'] == 'sma':
                window = indicator['params']['window']
                df[f'sma_{window}'] = df['close'].rolling(window=window).mean()

            elif indicator['name'] == 'ema':
                window = indicator['params']['window']
                df[f'ema_{window}'] = df['close'].ewm(span=window, adjust=False).mean()

            elif indicator['name'] == 'rsi':
                window = indicator['params']['window']
                df[f'rsi_{window}'] = self._calculate_rsi(df['close'], window)

            elif indicator['name'] == 'macd':
                fast = indicator['params']['fast']
                slow = indicator['params']['slow']
                signal = indicator['params']['signal']
                df[f'macd_line_{fast}_{slow}'] = df['close'].ewm(span=fast, adjust=False).mean() - df['close'].ewm(span=slow, adjust=False).mean()
                df[f'macd_signal_{signal}'] = df[f'macd_line_{fast}_{slow}'].ewm(span=signal, adjust=False).mean()
                df[f'macd_hist_{fast}_{slow}_{signal}'] = df[f'macd_line_{fast}_{slow}'] - df[f'macd_signal_{signal}']

            elif indicator['name'] == 'bbands':
                window = indicator['params']['window']
                std = indicator['params']['std']
                df[f'bb_middle_{window}'] = df['close'].rolling(window=window).mean()
                df[f'bb_upper_{window}'] = df[f'bb_middle_{window}'] + std * df['close'].rolling(window=window).std()
                df[f'bb_lower_{window}'] = df[f'bb_middle_{window}'] - std * df['close'].rolling(window=window).std()
                df[f'bb_width_{window}'] = (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}']) / df[f'bb_middle_{window}']
                df[f'bb_pct_{window}'] = (df['close'] - df[f'bb_lower_{window}']) / (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}'])

            elif indicator['name'] == 'atr':
                window = indicator['params']['window']
                df[f'atr_{window}'] = self._calculate_atr(df, window)

        return df

    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add volatility features.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with volatility features
        """
        # Calculate rolling volatility
        for window in [5, 10, 20]:
            df[f'volatility_{window}'] = df['returns'].rolling(window=window).std()

        # Calculate Garman-Klass volatility
        df['gk_volatility'] = np.sqrt(
            0.5 * np.log(df['high'] / df['low'])**2 -
            (2 * np.log(2) - 1) * np.log(df['close'] / df['open'])**2
        )

        # Calculate high-low range
        df['range'] = (df['high'] - df['low']) / df['close'].shift(1)

        return df

    def _add_trend_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add trend features.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with trend features
        """
        # Calculate ADX (Average Directional Index)
        for window in [14]:
            df[f'adx_{window}'] = self._calculate_adx(df, window)

        # Calculate moving average crossovers
        if 'sma_20' in df.columns and 'sma_50' in df.columns:
            df['sma_20_50_cross'] = np.where(df['sma_20'] > df['sma_50'], 1, -1)

        # Calculate price relative to moving averages
        for ma in ['sma_20', 'sma_50', 'sma_200']:
            if ma in df.columns:
                df[f'close_to_{ma}'] = df['close'] / df[ma] - 1

        # Calculate slope of moving averages
        for ma in ['sma_20', 'sma_50']:
            if ma in df.columns:
                df[f'{ma}_slope'] = df[ma].pct_change(5)

        return df

    def _add_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add momentum features.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with momentum features
        """
        # Calculate momentum
        for window in [5, 10, 20]:
            df[f'momentum_{window}'] = df['close'].pct_change(window)

        # Calculate Rate of Change (ROC)
        for window in [5, 10, 20]:
            df[f'roc_{window}'] = (df['close'] - df['close'].shift(window)) / df['close'].shift(window) * 100

        # Calculate Stochastic Oscillator
        for window in [14]:
            df[f'stoch_k_{window}'] = 100 * (df['close'] - df['low'].rolling(window=window).min()) / (df['high'].rolling(window=window).max() - df['low'].rolling(window=window).min())
            df[f'stoch_d_{window}'] = df[f'stoch_k_{window}'].rolling(window=3).mean()

        return df

    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add volume features.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with volume features
        """
        # Check if volume data is available
        if 'volume' not in df.columns or df['volume'].isnull().all():
            logger.warning("Volume data not available, skipping volume features")
            return df

        # Calculate volume indicators
        df['volume_change'] = df['volume'].pct_change()

        # Calculate On-Balance Volume (OBV)
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()

        # Calculate Volume Moving Averages
        for window in [5, 10, 20]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']

        # Calculate Volume-Price Trend (VPT)
        df['vpt'] = (df['volume'] * df['close'].pct_change()).fillna(0).cumsum()

        return df

    def _calculate_rsi(self, prices: pd.Series, window: int) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI).

        Args:
            prices: Price series
            window: RSI window

        Returns:
            pd.Series: RSI values
        """
        delta = prices.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(window=window).mean()
        avg_loss = loss.rolling(window=window).mean()

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def _calculate_atr(self, df: pd.DataFrame, window: int) -> pd.Series:
        """
        Calculate Average True Range (ATR).

        Args:
            df: DataFrame with OHLCV data
            window: ATR window

        Returns:
            pd.Series: ATR values
        """
        high = df['high']
        low = df['low']
        close = df['close']

        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=window).mean()

        return atr

    def _calculate_adx(self, df: pd.DataFrame, window: int = 14) -> pd.Series:
        """
        Calculate Average Directional Index (ADX).

        Args:
            df: DataFrame with OHLCV data
            window: ADX window

        Returns:
            pd.Series: ADX values
        """
        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate True Range
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=window).mean()

        # Calculate Directional Movement
        up_move = high - high.shift(1)
        down_move = low.shift(1) - low

        pos_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        neg_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)

        # Calculate Directional Indicators
        pdi = 100 * pd.Series(pos_dm).rolling(window=window).mean() / atr
        ndi = 100 * pd.Series(neg_dm).rolling(window=window).mean() / atr

        # Calculate Directional Index
        dx = 100 * abs(pdi - ndi) / (pdi + ndi)

        # Calculate Average Directional Index
        adx = dx.rolling(window=window).mean()

        return adx

    def _calculate_stochastic_oscillator(self, df: pd.DataFrame, window: int = 14) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate Stochastic Oscillator.

        Args:
            df: DataFrame with OHLCV data
            window: Stochastic window

        Returns:
            Tuple[pd.Series, pd.Series]: K and D values
        """
        # Calculate %K
        low_min = df['low'].rolling(window=window).min()
        high_max = df['high'].rolling(window=window).max()

        k = 100 * (df['close'] - low_min) / (high_max - low_min)

        # Calculate %D (3-period moving average of %K)
        d = k.rolling(window=3).mean()

        return k, d

    def _calculate_obv(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate On-Balance Volume (OBV).

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.Series: OBV values
        """
        if 'volume' not in df.columns:
            return pd.Series(index=df.index)

        close_diff = df['close'].diff()

        # Create OBV with direction based on price movement
        obv = pd.Series(0, index=df.index)

        for i in range(1, len(df)):
            if close_diff.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + df['volume'].iloc[i]
            elif close_diff.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - df['volume'].iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        return obv

    def normalize_features(self, df: pd.DataFrame, method: str = 'robust') -> pd.DataFrame:
        """
        Normalize features for the model using various methods.

        Args:
            df: DataFrame with features
            method: Normalization method ('standard', 'minmax', 'robust', or 'quantile')

        Returns:
            pd.DataFrame: DataFrame with normalized features
        """
        # Make a copy to avoid modifying the original
        df_norm = df.copy()

        # Define columns to exclude from normalization
        exclude_cols = ['time', 'date', 'timestamp', 'hour', 'day_of_week', 'is_weekend']

        # Define columns that should be preserved as is (categorical or binary)
        preserve_cols = ['cross_20_50', 'higher_high', 'lower_low']

        # Normalize features based on the selected method
        for col in df_norm.columns:
            # Skip excluded columns and NaN-only columns
            if col in exclude_cols or df_norm[col].isna().all():
                continue

            # Skip preserved columns (categorical or binary)
            if col in preserve_cols:
                continue

            # Handle columns with constant values
            if df_norm[col].std() == 0:
                df_norm[col] = 0
                continue

            # Apply the selected normalization method
            if method == 'standard':
                # Z-score normalization (mean=0, std=1)
                df_norm[col] = (df_norm[col] - df_norm[col].mean()) / df_norm[col].std()

            elif method == 'minmax':
                # Min-max scaling to [0, 1]
                min_val = df_norm[col].min()
                max_val = df_norm[col].max()
                if max_val > min_val:
                    df_norm[col] = (df_norm[col] - min_val) / (max_val - min_val)
                else:
                    df_norm[col] = 0

            elif method == 'robust':
                # Robust scaling using median and IQR
                # Less sensitive to outliers than standard scaling
                median = df_norm[col].median()
                q1 = df_norm[col].quantile(0.25)
                q3 = df_norm[col].quantile(0.75)
                iqr = q3 - q1
                if iqr > 0:
                    df_norm[col] = (df_norm[col] - median) / iqr
                else:
                    # Fall back to standard scaling if IQR is zero
                    df_norm[col] = (df_norm[col] - df_norm[col].mean()) / df_norm[col].std()

            elif method == 'quantile':
                # Quantile transformation to normal distribution
                try:
                    from sklearn.preprocessing import QuantileTransformer
                    # Reshape for sklearn
                    values = df_norm[col].values.reshape(-1, 1)
                    transformer = QuantileTransformer(output_distribution='normal')
                    df_norm[col] = transformer.fit_transform(values).flatten()
                except ImportError:
                    # Fall back to robust scaling if sklearn is not available
                    median = df_norm[col].median()
                    q1 = df_norm[col].quantile(0.25)
                    q3 = df_norm[col].quantile(0.75)
                    iqr = q3 - q1
                    if iqr > 0:
                        df_norm[col] = (df_norm[col] - median) / iqr
                    else:
                        df_norm[col] = (df_norm[col] - df_norm[col].mean()) / df_norm[col].std()

            # Clip extreme values to reduce impact of outliers
            df_norm[col] = df_norm[col].clip(-5, 5)

        return df_norm

    def create_sequences(
        self,
        df: pd.DataFrame,
        sequence_length: int,
        target_column: str = 'returns'
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences for LSTM model with optimized performance.

        Args:
            df: DataFrame with features
            sequence_length: Length of sequences
            target_column: Target column for prediction

        Returns:
            Tuple[np.ndarray, np.ndarray]: X and y arrays
        """
        logger.info(f"Creating sequences with optimized performance for {len(df)} rows...")

        # Validate inputs early
        if len(df) <= sequence_length:
            logger.error(f"Not enough data for sequence length {sequence_length}. Data size: {len(df)}")
            return np.array([]), np.array([])

        if target_column not in df.columns:
            logger.error(f"Target column '{target_column}' not found in DataFrame")
            return np.array([]), np.array([])

        # Create a working copy and handle NaN values efficiently
        logger.debug("Handling NaN values...")
        df_work = df.copy()

        # Use more efficient NaN handling
        if df_work.isna().any().any():
            # Forward fill first (fastest)
            df_work = df_work.ffill()
            # Only do backward fill if still needed
            if df_work.isna().any().any():
                df_work = df_work.bfill()
            # Fill any remaining NaN with 0
            if df_work.isna().any().any():
                df_work = df_work.fillna(0)

        # Get feature columns efficiently
        feature_columns = [col for col in df_work.columns if col != target_column]

        # Optimize column validation - vectorized approach
        logger.debug("Validating feature columns...")
        numeric_dtypes = ['int8', 'int16', 'int32', 'int64', 'float16', 'float32', 'float64', 'bool']

        # Filter numeric columns efficiently
        valid_columns = []
        for col in feature_columns:
            if df_work[col].dtype.name in numeric_dtypes:
                valid_columns.append(col)
            elif col == 'time':
                # Handle time column specially - convert to numeric if needed
                if hasattr(df_work[col].iloc[0], 'timestamp'):
                    df_work[col] = df_work[col].apply(lambda x: float(x.timestamp()) if hasattr(x, 'timestamp') else float(x))
                valid_columns.append(col)
            else:
                # Try to convert to numeric
                try:
                    df_work[col] = pd.to_numeric(df_work[col], errors='raise')
                    valid_columns.append(col)
                except (ValueError, TypeError):
                    logger.warning(f"Skipping non-numeric column: {col}")
                    continue

        feature_columns = valid_columns
        logger.info(f"Using {len(feature_columns)} feature columns for sequence creation")

        if len(feature_columns) == 0:
            logger.error("No valid feature columns found")
            return np.array([]), np.array([])

        # Calculate number of sequences
        num_sequences = len(df_work) - sequence_length
        if num_sequences <= 0:
            logger.error(f"Cannot create sequences: data length {len(df_work)} <= sequence_length {sequence_length}")
            return np.array([]), np.array([])

        logger.info(f"Creating {num_sequences} sequences...")

        try:
            # OPTIMIZED APPROACH: Use vectorized operations

            # Extract feature data as numpy array once
            feature_data = df_work[feature_columns].values.astype(np.float32)
            target_data = df_work[target_column].values.astype(np.float32)

            # Pre-allocate arrays for better performance
            X = np.zeros((num_sequences, sequence_length, len(feature_columns)), dtype=np.float32)
            y = np.zeros(num_sequences, dtype=np.float32)

            # Vectorized sequence creation using array slicing
            for i in range(num_sequences):
                X[i] = feature_data[i:i+sequence_length]
                y[i] = target_data[i+sequence_length]

            logger.info(f"Successfully created {len(X)} sequences with shape {X.shape}")
            return X, y

        except Exception as e:
            logger.error(f"Error in optimized sequence creation: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Fallback to chunked processing for very large datasets
            logger.info("Falling back to chunked processing...")
            return self._create_sequences_chunked(df_work, feature_columns, target_column, sequence_length)

    def prepare_data_for_training(
        self,
        df: pd.DataFrame,
        sequence_length: int,
        target_column: str = 'returns',
        train_test_split: float = 0.8,
        validation_split: float = 0.1
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Prepare data for training.

        Args:
            df: DataFrame with features
            sequence_length: Length of sequences
            target_column: Target column for prediction
            train_test_split: Train-test split ratio
            validation_split: Validation split ratio

        Returns:
            Tuple: X_train, y_train, X_val, y_val, X_test, y_test
        """
        # Log the data size
        logger.info(f"Data size before creating sequences: {len(df)}")

        # Check if we have enough data
        if len(df) <= sequence_length:
            logger.error(f"Not enough data for sequence length {sequence_length}. Data size: {len(df)}")
            return np.array([]), np.array([]), np.array([]), np.array([]), np.array([]), np.array([])

        # Create sequences
        X, y = self.create_sequences(df, sequence_length, target_column)

        # Log the sequences size
        logger.info(f"Created {len(X)} sequences")

        # Check if we have enough sequences
        if len(X) < 100:  # Minimum number of sequences for training
            logger.error(f"Not enough sequences for training. Sequences: {len(X)}")
            return np.array([]), np.array([]), np.array([]), np.array([]), np.array([]), np.array([])

        # Calculate split indices
        train_size = int(len(X) * train_test_split)
        val_size = int(len(X) * validation_split)

        # Ensure we have at least some data for each split
        if train_size < 10 or val_size < 10 or (len(X) - train_size - val_size) < 10:
            logger.error(f"Not enough data for splits. Train: {train_size}, Val: {val_size}, Test: {len(X) - train_size - val_size}")
            return np.array([]), np.array([]), np.array([]), np.array([]), np.array([]), np.array([])

        # Split data
        X_train, y_train = X[:train_size], y[:train_size]
        X_val, y_val = X[train_size:train_size+val_size], y[train_size:train_size+val_size]
        X_test, y_test = X[train_size+val_size:], y[train_size+val_size:]

        # Log the split sizes
        logger.info(f"Train size: {len(X_train)}, Val size: {len(X_val)}, Test size: {len(X_test)}")

        return X_train, y_train, X_val, y_val, X_test, y_test

    def _create_sequences_chunked(
        self,
        df: pd.DataFrame,
        feature_columns: List[str],
        target_column: str,
        sequence_length: int,
        chunk_size: int = 10000
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences using chunked processing for very large datasets.

        Args:
            df: DataFrame with features
            feature_columns: List of feature column names
            target_column: Target column name
            sequence_length: Length of sequences
            chunk_size: Size of chunks to process at a time

        Returns:
            Tuple[np.ndarray, np.ndarray]: X and y arrays
        """
        logger.info(f"Using chunked processing with chunk_size={chunk_size}")

        num_sequences = len(df) - sequence_length
        num_features = len(feature_columns)

        # Pre-allocate result arrays
        X = np.zeros((num_sequences, sequence_length, num_features), dtype=np.float32)
        y = np.zeros(num_sequences, dtype=np.float32)

        # Extract data as numpy arrays once
        feature_data = df[feature_columns].values.astype(np.float32)
        target_data = df[target_column].values.astype(np.float32)

        # Process in chunks
        for start_idx in range(0, num_sequences, chunk_size):
            end_idx = min(start_idx + chunk_size, num_sequences)
            chunk_sequences = end_idx - start_idx

            logger.debug(f"Processing chunk {start_idx//chunk_size + 1}: sequences {start_idx} to {end_idx-1}")

            # Create sequences for this chunk
            for i in range(chunk_sequences):
                seq_idx = start_idx + i
                X[seq_idx] = feature_data[seq_idx:seq_idx+sequence_length]
                y[seq_idx] = target_data[seq_idx+sequence_length]

        logger.info(f"Chunked processing completed: created {len(X)} sequences")
        return X, y
