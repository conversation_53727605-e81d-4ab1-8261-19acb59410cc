# LSTM Model Module

## Overview

The LSTM Model module implements the Long Short-Term Memory (LSTM) neural network for price prediction. It is implemented in the `model/lstm_model.py` and `model/model_trainer.py` files.

## TradingLSTM Class

The `TradingLSTM` class is the main model class in this module. It implements the LSTM neural network architecture.

### Initialization

```python
from model.lstm_model import TradingLSTM
import torch

# Initialize model
model = TradingLSTM(
    input_size=64,
    hidden_size=64,
    num_layers=2,
    output_size=1,
    dropout=0.2
)
```

**Parameters**:
- `input_size` (int): Number of input features
- `hidden_size` (int): Number of hidden units
- `num_layers` (int): Number of LSTM layers
- `output_size` (int): Number of output units
- `dropout` (float): Dropout rate

### Methods

#### forward

Forward pass through the network.

```python
output = model(x)
```

**Parameters**:
- `x` (torch.Tensor): Input tensor of shape (batch_size, sequence_length, input_size)

**Returns**:
- `torch.Tensor`: Output tensor of shape (batch_size, output_size)

## ModelTrainer Class

The `ModelTrainer` class handles training and evaluation of the LSTM model.

### Initialization

```python
from model.model_trainer import ModelTrainer

# Initialize trainer
trainer = ModelTrainer(
    input_size=64,
    hidden_size=64,
    num_layers=2,
    output_size=1,
    learning_rate=0.001,
    weight_decay=1e-5,
    device=None
)
```

**Parameters**:
- `input_size` (int): Number of input features
- `hidden_size` (int): Number of hidden units
- `num_layers` (int): Number of LSTM layers
- `output_size` (int): Number of output units
- `learning_rate` (float): Learning rate
- `weight_decay` (float): Weight decay for L2 regularization
- `device` (str): Device to use for training ('cpu', 'cuda', 'mps')

### Methods

#### train

Train the model.

```python
history = trainer.train(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    batch_size=32,
    epochs=100,
    patience=10,
    verbose=True
)
```

**Parameters**:
- `X_train` (np.ndarray): Training features
- `y_train` (np.ndarray): Training targets
- `X_val` (np.ndarray): Validation features
- `y_val` (np.ndarray): Validation targets
- `batch_size` (int): Batch size
- `epochs` (int): Number of epochs
- `patience` (int): Early stopping patience
- `verbose` (bool): Whether to print progress

**Returns**:
- `Dict[str, List[float]]`: Training history

#### predict

Make predictions.

```python
predictions = trainer.predict(X)
```

**Parameters**:
- `X` (np.ndarray): Input features

**Returns**:
- `np.ndarray`: Predictions

#### evaluate

Evaluate the model.

```python
metrics = trainer.evaluate(X, y)
```

**Parameters**:
- `X` (np.ndarray): Input features
- `y` (np.ndarray): Target values

**Returns**:
- `Dict[str, float]`: Evaluation metrics

#### save_model

Save the model.

```python
trainer.save_model(filepath)
```

**Parameters**:
- `filepath` (str): Path to save the model

#### load_model

Load the model.

```python
success = trainer.load_model(filepath)
```

**Parameters**:
- `filepath` (str): Path to load the model from

**Returns**:
- `bool`: True if model was loaded successfully, False otherwise

#### update_model

Update the model with new data (online learning).

```python
trainer.update_model(
    X=X,
    y=y,
    batch_size=32,
    epochs=10
)
```

**Parameters**:
- `X` (np.ndarray): Input features
- `y` (np.ndarray): Target values
- `batch_size` (int): Batch size
- `epochs` (int): Number of epochs

## ModelManager Class

The `ModelManager` class manages multiple LSTM models.

### Initialization

```python
from model.model_trainer import ModelManager

# Initialize manager
manager = ModelManager()
```

### Methods

#### create_model

Create a new LSTM model.

```python
trainer = manager.create_model(
    model_name="btcusd_m15",
    input_size=64,
    hidden_size=64,
    num_layers=2,
    output_size=1
)
```

**Parameters**:
- `model_name` (str): Name of the model
- `input_size` (int): Number of input features
- `hidden_size` (int): Number of hidden units
- `num_layers` (int): Number of LSTM layers
- `output_size` (int): Number of output units

**Returns**:
- `ModelTrainer`: Trainer for the model

#### get_model

Get a model by name.

```python
trainer = manager.get_model(model_name)
```

**Parameters**:
- `model_name` (str): Name of the model

**Returns**:
- `ModelTrainer`: Trainer for the model or None if not found

#### train_model

Train a model.

```python
history = manager.train_model(
    model_name="btcusd_m15",
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val
)
```

**Parameters**:
- `model_name` (str): Name of the model
- `X_train` (np.ndarray): Training features
- `y_train` (np.ndarray): Training targets
- `X_val` (np.ndarray): Validation features
- `y_val` (np.ndarray): Validation targets

**Returns**:
- `Dict[str, List[float]]`: Training history

#### update_model

Update a model with new data.

```python
manager.update_model(
    model_name="btcusd_m15",
    X=X,
    y=y
)
```

**Parameters**:
- `model_name` (str): Name of the model
- `X` (np.ndarray): Input features
- `y` (np.ndarray): Target values

#### predict

Make predictions with a model.

```python
predictions = manager.predict(
    model_name="btcusd_m15",
    X=X
)
```

**Parameters**:
- `model_name` (str): Name of the model
- `X` (np.ndarray): Input features

**Returns**:
- `np.ndarray`: Predictions

#### evaluate_model

Evaluate a model.

```python
metrics = manager.evaluate_model(
    model_name="btcusd_m15",
    X=X,
    y=y
)
```

**Parameters**:
- `model_name` (str): Name of the model
- `X` (np.ndarray): Input features
- `y` (np.ndarray): Target values

**Returns**:
- `Dict[str, float]`: Evaluation metrics

#### save_model

Save a model to disk.

```python
manager.save_model(model_name)
```

**Parameters**:
- `model_name` (str): Name of the model

#### load_model

Load a model from disk.

```python
success = manager.load_model(model_name)
```

**Parameters**:
- `model_name` (str): Name of the model

**Returns**:
- `bool`: True if model was loaded successfully, False otherwise

#### list_models

List all available models.

```python
models = manager.list_models()
```

**Returns**:
- `List[str]`: List of model names

#### delete_model

Delete a model.

```python
success = manager.delete_model(model_name)
```

**Parameters**:
- `model_name` (str): Name of the model

**Returns**:
- `bool`: True if model was deleted successfully, False otherwise

## LSTM Architecture

The LSTM model architecture is as follows:

1. **LSTM Layers**: One or more LSTM layers with configurable hidden size and number of layers
2. **Dropout Layer**: Dropout layer for regularization
3. **Fully Connected Layer**: Fully connected layer for output

The forward pass through the network works as follows:

1. Input tensor of shape (batch_size, sequence_length, input_size) is passed to the LSTM layers
2. LSTM layers process the input and produce output of shape (batch_size, sequence_length, hidden_size)
3. Only the output from the last time step is used: (batch_size, hidden_size)
4. Dropout is applied to the output
5. Fully connected layer maps the output to the desired output size: (batch_size, output_size)

## Training Process

The training process is as follows:

1. **Data Preparation**:
   - Convert numpy arrays to PyTorch tensors
   - Create DataLoader for batch processing

2. **Training Loop**:
   - For each epoch:
     - Set model to training mode
     - For each batch:
       - Forward pass
       - Calculate loss
       - Backward pass
       - Optimize
     - Validate on validation set
     - Update learning rate scheduler
     - Check for early stopping

3. **Early Stopping**:
   - If validation loss doesn't improve for `patience` epochs, stop training
   - Save the best model based on validation loss

## Model Evaluation

The model is evaluated using the following metrics:

- **Loss**: Mean Squared Error (MSE)
- **RMSE**: Root Mean Squared Error
- **MAE**: Mean Absolute Error
- **Direction Accuracy**: Percentage of correct price direction predictions

## Model Saving and Loading

The model is saved with the following information:

- **Model State**: Weights and biases of the model
- **Optimizer State**: State of the optimizer
- **Hyperparameters**: Model hyperparameters
- **Training History**: Loss and metrics during training

This allows the model to be loaded and continue training from where it left off.

## Online Learning

The model supports online learning, which allows it to be updated with new data without retraining from scratch. This is useful for adapting to changing market conditions.

The online learning process is as follows:

1. **Data Preparation**:
   - Convert numpy arrays to PyTorch tensors
   - Create DataLoader for batch processing

2. **Training Loop**:
   - For a small number of epochs:
     - Set model to training mode
     - For each batch:
       - Forward pass
       - Calculate loss
       - Backward pass
       - Optimize

## Usage Examples

### Creating and Training a Model

```python
from model.model_manager import ModelManager
import numpy as np

# Initialize manager
manager = ModelManager()

# Create model
trainer = manager.create_model(
    model_name="btcusd_m15",
    input_size=64,
    hidden_size=64,
    num_layers=2,
    output_size=1
)

# Create dummy data
X_train = np.random.randn(1000, 60, 64)
y_train = np.random.randn(1000)
X_val = np.random.randn(200, 60, 64)
y_val = np.random.randn(200)

# Train model
history = manager.train_model(
    model_name="btcusd_m15",
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val
)

print(f"Final train loss: {history['train_loss'][-1]:.6f}")
print(f"Final validation loss: {history['val_loss'][-1]:.6f}")
```

### Making Predictions

```python
from model.model_trainer import ModelManager
import numpy as np

# Initialize manager
manager = ModelManager()

# Load model
success = manager.load_model("btcusd_m15")
if not success:
    print("Failed to load model")
    exit(1)

# Create dummy data
X = np.random.randn(10, 60, 64)

# Make predictions
predictions = manager.predict(
    model_name="btcusd_m15",
    X=X
)

print(f"Predictions shape: {predictions.shape}")
print(f"Predictions: {predictions}")
```

### Evaluating a Model

```python
from model.model_trainer import ModelManager
import numpy as np

# Initialize manager
manager = ModelManager()

# Load model
success = manager.load_model("btcusd_m15")
if not success:
    print("Failed to load model")
    exit(1)

# Create dummy data
X = np.random.randn(1000, 60, 64)
y = np.random.randn(1000)

# Evaluate model
metrics = manager.evaluate_model(
    model_name="btcusd_m15",
    X=X,
    y=y
)

print(f"Metrics: {metrics}")
```

### Updating a Model

```python
from model.model_manager import ModelManager
import numpy as np

# Initialize manager
manager = ModelManager()

# Load model
success = manager.load_model("btcusd_m15")
if not success:
    print("Failed to load model")
    exit(1)

# Create dummy data
X = np.random.randn(100, 60, 64)
y = np.random.randn(100)

# Update model
manager.update_model(
    model_name="btcusd_m15",
    X=X,
    y=y
)

print("Model updated successfully")
```

## Dependencies

The LSTM model module depends on the following:

- **PyTorch**: Deep learning framework
- **NumPy**: Numerical operations
- **config/trading_config.py**: Model configuration

## Configuration

The LSTM model is configured through the `config/trading_config.py` file:

```python
TRADING_CONFIG = {
    # Model parameters
    "model": {
        "sequence_length": 60,       # Sequence length for LSTM
        "hidden_size": 64,           # Number of hidden units in LSTM
        "num_layers": 2,             # Number of LSTM layers
        "train_test_split": 0.8,     # Train-test split ratio
        "validation_split": 0.1,     # Validation split ratio
        "batch_size": 32,            # Batch size for training
        "epochs": 100,               # Number of epochs for training
        "learning_rate": 0.001,      # Learning rate
        "early_stopping_patience": 10, # Early stopping patience
        "retraining_interval": 24,   # Retraining interval in hours
    },
    
    # Additional configuration...
}
```

## Limitations

The LSTM model has the following limitations:

- **Vanishing/Exploding Gradients**: LSTM models can still suffer from vanishing or exploding gradients
- **Computational Complexity**: LSTM models are computationally expensive to train
- **Overfitting**: LSTM models can overfit to the training data
- **Prediction Horizon**: LSTM models are better at short-term predictions than long-term predictions
- **Market Noise**: Financial markets are noisy and unpredictable, which limits the accuracy of any model

## Best Practices

When using the LSTM model, follow these best practices:

1. **Data Preprocessing**: Normalize input features and target values
2. **Sequence Length**: Choose an appropriate sequence length based on the timeframe
3. **Model Complexity**: Balance model complexity with the amount of training data
4. **Regularization**: Use dropout and weight decay to prevent overfitting
5. **Early Stopping**: Use early stopping to prevent overfitting
6. **Learning Rate**: Use a learning rate scheduler to adjust the learning rate during training
7. **Evaluation**: Evaluate the model on multiple metrics, not just loss
8. **Continuous Learning**: Update the model with new data to adapt to changing market conditions
