# MT5 Multi-Terminal Trading System

A sophisticated multi-terminal MetaTrader 5 trading system with advanced machine learning models for automated trading across multiple timeframes and symbols.

## 🚀 Quick Start

### System Setup
```bash
# Complete system setup with optimization
python scripts/master_system_setup.py --symbol BTCUSD.a --years 5

# Quick setup for testing (faster, minimal data)
python scripts/master_system_setup.py --quick-setup --symbol BTCUSD.a
```

### Manual Setup
```bash
# 1. System optimization
python scripts/comprehensive_system_optimization.py --full-optimization

# 2. Data collection
python scripts/optimize_data_collection.py --symbol BTCUSD.a --parallel --validate

# 3. System validation and training
python scripts/validate_and_train_system.py --symbol BTCUSD.a --years 5

# 4. Start trading
python run_trading_bot.py --dashboard
```

## 🔧 Features

### Core Functionality
- **Multi-Terminal Management**: Simultaneous operation of 5 MT5 terminals
- **Advanced ML Models**: LSTM, TFT, ARIMA, and ensemble combinations
- **Automated Data Collection**: Historical data across multiple timeframes (M5, M15, M30, H1, H4)
- **Real-time Trading**: Signal generation and automated execution
- **Performance Monitoring**: Comprehensive analytics and visualization
- **Risk Management**: Built-in position sizing and risk controls

### Terminal Configuration
- **Terminal 1**: ARIMA models (Pepperstone Demo 1)
- **Terminal 2**: LSTM models (Pepperstone Demo 2)
- **Terminal 3**: TFT models (IC Markets Demo 1)
- **Terminal 4**: LSTM+ARIMA ensemble (IC Markets Demo 2)
- **Terminal 5**: TFT+ARIMA ensemble (IC Markets Demo 3)

### Data Organization
```
data/storage/historical/
├── terminal_1/
│   ├── BTCUSD.a_M5/
│   ├── BTCUSD.a_M15/
│   ├── BTCUSD.a_M30/
│   ├── BTCUSD.a_H1/
│   └── BTCUSD.a_H4/
├── terminal_2/
│   └── ... (same structure)
└── ... (terminals 3-5)
```

### Model Storage Structure
```
model/saved_models/
├── arima/
│   ├── BTCUSD.a_M5_arima.pkl
│   ├── BTCUSD.a_M5_arima_metrics.json
│   └── ... (all timeframes)
├── lstm/
│   ├── BTCUSD.a_M5_lstm.pth
│   ├── BTCUSD.a_M5_lstm_metrics.json
│   └── ... (all timeframes)
├── tft/
│   └── ... (similar structure)
└── ensemble/
    ├── BTCUSD.a_M5_lstm_arima.pkl
    ├── BTCUSD.a_M5_tft_arima.pkl
    └── ... (ensemble models)
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/r70pro/BTCUSD-Trading-Bot.git
cd BTCUSD-Trading-Bot
```

2. Install the required packages:
```bash
pip install -r requirements.txt
```

3. Configure your MT5 terminals in `config/credentials.py`.

## Command-Line Reference

The main script `run_trading_bot.py` supports the following arguments:

### Basic Options
- `--terminal ID`: Specify MT5 terminal ID (default: 1)
- `--skip-terminal-open`: Skip opening MT5 terminals automatically
- `--symbol SYMBOL`: Trading symbol (default: BTCUSD.a)

### Trading Operations
- `--train`: Train models
- `--backtest`: Run backtest
- `--optimize`: Run walk-forward optimization
- `--report`: Generate performance report
- `--dashboard`: Start dashboard only

### Date Range Options
- `--start-date DATE`: Start date for training/backtest (default: 2022-01-01)
- `--end-date DATE`: End date for training/backtest (default: current date)

### Visualization Options
- `--generate-dashboard`: Generate visualization dashboard
- `--sample-dashboard`: Generate sample dashboard with dummy data
- `--data-dir DIR`: Data directory for dashboard generation (default: data/storage)
- `--output-dir DIR`: Output directory for dashboard (default: monitoring/dashboard)
- `--days N`: Number of days for sample data generation (default: 90)

### Examples
```bash
# Start trading bot with dashboard
python run_trading_bot.py --dashboard

# Train models for specific date range
python run_trading_bot.py --train --start-date 2023-01-01 --end-date 2024-01-01

# Run backtest
python run_trading_bot.py --backtest --start-date 2023-01-01 --end-date 2024-01-01

# Generate sample dashboard
python run_trading_bot.py --sample-dashboard --days 30

# Skip terminal opening and start specific terminal
python run_trading_bot.py --skip-terminal-open --terminal 2
```

## Documentation Structure

### 01 System Architecture
- [System Overview](01_system_architecture/system_overview.md)
- [Terminal Configuration](01_system_architecture/terminal_configuration.md)
- [Data Flow Diagram](01_system_architecture/data_flow_diagram.md)
- [Component Relationships](01_system_architecture/component_relationships.md)

### 02 Data Management
- [Data Collection Process](02_data_management/data_collection_process.md)
- [Data Validation Procedures](02_data_management/data_validation_procedures.md)
- [Storage Organization](02_data_management/storage_organization.md)
- [Quality Assurance](02_data_management/quality_assurance.md)

### 03 Model Training
- [Model Architectures](03_model_training/model_architectures.md)
- [Training Procedures](03_model_training/training_procedures.md)
- [Ensemble Methods](03_model_training/ensemble_methods.md)
- [Performance Metrics](03_model_training/performance_metrics.md)

### 04 Trading Operations
- [Trading Strategies](04_trading_operations/trading_strategies.md)
- [Risk Management](04_trading_operations/risk_management.md)
- [Execution Procedures](04_trading_operations/execution_procedures.md)
- [Monitoring Protocols](04_trading_operations/monitoring_protocols.md)

### 05 Deployment and Monitoring
- [Deployment Guide](05_deployment_monitoring/deployment_guide.md)
- [System Monitoring](05_deployment_monitoring/system_monitoring.md)
- [Performance Tracking](05_deployment_monitoring/performance_tracking.md)
- [Maintenance Procedures](05_deployment_monitoring/maintenance_procedures.md)

Last updated: 2025-06-06 15:25:00
