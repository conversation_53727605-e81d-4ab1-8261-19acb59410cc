# Quick Start Guide

This document provides a quick start guide for setting up and using the trading system.

## Overview

The trading system is a multi-terminal, multi-model trading system that uses 5 MT5 terminals, each specializing in a specific model type:
- Terminal 1: ARIMA (Statistical time series forecasting)
- Terminal 2: LSTM (Deep learning sequence modeling)
- Terminal 3: TFT (Transformer-based forecasting)
- Terminal 4: LSTM+ARIMA ensemble (Hybrid approach)
- Terminal 5: TFT+ARIMA ensemble (Advanced hybrid approach)

## Prerequisites

Before you begin, ensure you have the following:
- Python 3.8 or higher
- MT5 terminals installed and configured
- Required Python packages (see `requirements.txt`)
- GPU with CUDA support (recommended for faster model training)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/trading-system.git
   cd trading-system
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the MT5 terminals:
   - Open `config/credentials.py`
   - Update the terminal paths and credentials
   - Save the file

## Configuration

The system configuration is managed by `config/central_config.py`. This file imports and combines configurations from other modules:
- Data collection
- Model training
- Trading
- Terminal-model mapping
- Path management

You can customize these settings by modifying the individual configuration files.

## Basic Usage

### Step 1: Initialize Terminals

Initialize the MT5 terminals:

```python
from utils.mt5_initializer import initialize_all_terminals

# Initialize all terminals
terminal_status = initialize_all_terminals()

# Check terminal status
for terminal_id, status in terminal_status.items():
    print(f"Terminal {terminal_id}: {'Initialized' if status['initialized'] else 'Not initialized'}")
```

### Step 2: Collect Data

Collect historical data:

```python
from data.data_collector import DataCollector

# Initialize the data collector
data_collector = DataCollector()

# Collect historical data
data = data_collector.collect_historical_data(
    symbol="BTCUSD.a",
    timeframe="M5",
    start_date="2023-01-01",
    end_date="2023-12-31",
    terminal_id=1
)

# Process data with technical indicators
processed_data = data_collector.process_data_with_indicators(
    data=data,
    symbol="BTCUSD.a",
    timeframe="M5",
    terminal_id=1
)
```

### Step 3: Train Models

Train models:

```python
from model.model_manager import ModelManager

# Initialize the model manager
model_manager = ModelManager()

# Train a model
history = model_manager.train_model(
    model_name="btcusd_m5_lstm",
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    feature_names=feature_names,
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

### Step 4: Make Predictions

Make predictions:

```python
# Make predictions
predictions = model_manager.predict(
    model_name="btcusd_m5_lstm",
    X=X_new,
    feature_names=feature_names,
    symbol="BTCUSD.a",
    timeframe="M5"
)
```

### Step 5: Execute Trades

Execute trades:

```python
from trading.trading_strategy import TradingStrategy

# Initialize the trading strategy
trading_strategy = TradingStrategy()

# Generate trading signals
signals = trading_strategy.generate_signals(
    predictions=predictions,
    symbol="BTCUSD.a",
    timeframe="M5"
)

# Execute trades
trading_strategy.execute_trades(signals)
```

## Using the Trading Bot

The `TradingBot` class provides a high-level interface for the trading system:

```python
from trading_bot import TradingBot

# Initialize the trading bot
trading_bot = TradingBot()

# Initialize terminals
trading_bot.initialize_terminals()

# Collect data
trading_bot.collect_data()

# Train models
trading_bot.train_models()

# Start trading
trading_bot.start_trading()
```

## Monitoring Performance

Monitor performance:

```python
# Monitor performance
performance = trading_bot.monitor_performance()

# Print performance metrics
print(f"Total profit: {performance['total_profit']}")
print(f"Win rate: {performance['win_rate']}")
print(f"Drawdown: {performance['drawdown']}")
```

## Next Steps

After completing this quick start guide, you can:
- Explore the main [README.md](../../README.md) for more detailed information
- Customize the [Trading Strategy](../trading/trading_strategy.md)
- Add new [Models](../models/model_types.md)
- Optimize [Performance](../trading/performance_monitoring.md)

## Troubleshooting

If you encounter issues:
- Check the logs in the `logs` directory
- Verify that the MT5 terminals are running and accessible
- Ensure that Algo Trading is enabled in the MT5 terminals
- Check the [Troubleshooting Guide](troubleshooting.md)

## Related Documentation

- [Installation Guide](installation_guide.md): Detailed installation instructions
- [Configuration Guide](configuration_guide.md): Information about system configuration
- [MT5 Connection Guide](../technical_reference/mt5_connection_guide.md): Guide for connecting to MT5 terminals
- [Data Collection Guide](../data_management/data_collection_guide.md): Information about data collection
- [Model Training Guide](../models/model_training_guide.md): Information about model training
- [Trading Guide](../trading/trading_guide.md): Information about trading
