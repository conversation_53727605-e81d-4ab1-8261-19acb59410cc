"""
Unified Model Training Script.
This script provides a unified interface for training different types of models
(LSTM, TFT, ARIMA, and ensembles) for all timeframes and symbols.
It handles data collection, feature engineering, model training, evaluation,
comparison, and selection.
"""
import logging
import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Tuple

from data.data_manager import DataManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_trainer import ModelTrainer
from model.tft_model_trainer import TFTModelTrainer
from model.model_comparison import ModelComparison
from model.model_selection import ModelSelector
from utils.path_utils import get_model_path, get_metrics_path, get_terminal_model_type
from trading.mt5_connector import MT5Connector
from utils.mt5_initializer import initialize_all_terminals
from utils.torch_security import register_safe_classes

# Create logs directory
os.makedirs('logs', exist_ok=True)

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join('logs', f'train_models_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'))
    ]
)
logger = logging.getLogger('train_models')

def load_timeframe_data(timeframe, start_date, end_date=None, terminal_id=1, symbol="BTCUSD.a"):
    """
    Load data for a specific timeframe from storage.

    Args:
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format (optional)
        terminal_id: Terminal ID to load data from (default: 1)
        symbol: Symbol to load data for (default: "BTCUSD.a")

    Returns:
        pd.DataFrame: Timeframe data
    """
    data_manager = DataManager()

    try:
        # First try to load from storage structure
        df = data_manager.load_historical_data(symbol, timeframe, terminal_id=terminal_id)

        if df is None or len(df) == 0:
            logger.info(f"No {timeframe} data found for {symbol} in storage from terminal {terminal_id}")

            # Try loading from merged data as fallback
            logger.info(f"Trying to load merged data for {symbol} ({timeframe})...")
            df = data_manager.load_historical_data(symbol, timeframe, merged=True)

            if df is None or len(df) == 0:
                # Try loading from the historical directory structure as last resort
                logger.info(f"Trying to load from historical directory for {symbol} ({timeframe})...")
                historical_path = f"data/historical/{timeframe}/{symbol}/terminal_{terminal_id}.parquet"
                if os.path.exists(historical_path):
                    df = pd.read_parquet(historical_path)
                    logger.info(f"Successfully loaded data from historical directory: {historical_path}")
                else:
                    # Try merged file in historical directory
                    merged_path = f"data/historical/{timeframe}/{symbol}/merged.parquet"
                    if os.path.exists(merged_path):
                        df = pd.read_parquet(merged_path)
                        logger.info(f"Successfully loaded merged data from historical directory: {merged_path}")
                    else:
                        logger.error(f"No {timeframe} data found for {symbol} in any location")
                        return None
            else:
                logger.info(f"Successfully loaded merged data for {symbol} ({timeframe})")

        # Ensure the index is datetime
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'time' in df.columns:
                df.set_index('time', inplace=True)
            else:
                logger.error(f"No time column found in data for {symbol} ({timeframe})")
                return None

        # Filter to the requested date range
        if start_date:
            start_ts = pd.Timestamp(start_date)
            df = df[df.index >= start_ts]
        if end_date:
            end_ts = pd.Timestamp(end_date)
            df = df[df.index <= end_ts]

        logger.info(f"Loaded {len(df)} {timeframe} bars for {symbol} from {df.index.min()} to {df.index.max()}")
        return df

    except Exception as e:
        logger.error(f"Failed to load {timeframe} data for {symbol}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def train_model(
    timeframe,
    df,
    model_type,
    symbol="BTCUSD.a",
    start_date=None,
    end_date=None,
    epochs=100,
    batch_size=64,
    sequence_length=10,
    hidden_size=64,
    num_layers=2,
    patience=10,
    num_attention_heads=4,
    dropout_rate=0.1,
    max_samples=50000,
    use_amp=True
):
    """
    Train model for a specific timeframe using the provided data.

    Args:
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        df: DataFrame with timeframe data
        model_type: Type of model to train ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format (optional)
        epochs: Number of epochs for training
        batch_size: Batch size for training
        sequence_length: Sequence length for sequence models
        hidden_size: Hidden size for sequence models
        num_layers: Number of layers for sequence models
        patience: Patience for early stopping
        num_attention_heads: Number of attention heads for TFT model
        dropout_rate: Dropout rate for sequence models
        max_samples: Maximum number of samples to use for training
        use_amp: Whether to use Automatic Mixed Precision for faster training

    Returns:
        bool: True if training was successful, False otherwise
    """
    try:
        # Create feature engineering instance
        feature_engineering = FeatureEngineer()

        # Generate features
        logger.info(f"Generating features for {timeframe} data...")
        df_features = feature_engineering.create_features(df, symbol=symbol)

        # Save features
        data_manager = DataManager()
        data_manager.save_features(
            df=df_features,
            symbol=symbol,
            timeframe=timeframe
        )
        logger.info(f"Saved feature data to storage for {timeframe}")

        # If dataset is too large, sample it
        if len(df_features) > max_samples:
            logger.info(f"Dataset is large ({len(df_features)} samples), sampling to {max_samples} samples")
            # Keep the most recent data
            df_features = df_features.iloc[-max_samples:]
            logger.info(f"Sampled dataset size: {len(df_features)}")

        # Create sequences for training with improved target variable
        logger.info(f"Creating sequences for training with sequence_length={sequence_length}...")
        X_train, y_train, X_val, y_val, X_test, y_test = feature_engineering.prepare_data_for_training(
            df=df_features,
            sequence_length=sequence_length,
            target_column='future_price_change',  # Use improved target variable
            train_test_split=0.8,
            validation_split=0.1
        )

        # Check if we have valid data
        if len(X_train) == 0 or len(X_val) == 0 or len(X_test) == 0:
            logger.error(f"Failed to create valid sequences for {timeframe}")
            return False

        # Log data shapes
        logger.info(f"Training data shapes: X_train: {X_train.shape}, y_train: {y_train.shape}")
        logger.info(f"Validation data shapes: X_val: {X_val.shape}, y_val: {y_val.shape}")
        logger.info(f"Test data shapes: X_test: {X_test.shape}, y_test: {y_test.shape}")

        # Set model parameters
        model_name = f"{symbol}_{timeframe}"
        input_size = X_train.shape[2]  # Number of features
        output_size = 1

        # Create model directories
        os.makedirs("model/saved_models", exist_ok=True)
        os.makedirs("model/visualizations", exist_ok=True)

        # Train model based on model type
        if model_type == 'lstm':
            # Train LSTM model using standardized path
            model_path = get_model_path(
                symbol=symbol,
                timeframe=timeframe,
                model_type='lstm'
            )

            # Create model trainer
            model_trainer = ModelTrainer(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size
            )

            # Train model with memory-efficient approach
            logger.info(f"Training LSTM model with hidden_size={hidden_size}, num_layers={num_layers}...")

            # Import memory-efficient dataset
            from model.dataset import BatchTimeSeriesDataset, create_data_loaders

            # Create data loaders for memory-efficient training
            train_loader, val_loader = create_data_loaders(
                X_train=X_train,
                y_train=y_train,
                X_val=X_val,
                y_val=y_val,
                batch_size=batch_size,
                num_workers=4,
                pin_memory=True
            )

            # Train with data loaders
            model_trainer.train_with_loaders(
                train_loader=train_loader,
                val_loader=val_loader,
                epochs=epochs,
                patience=patience,
                use_amp=use_amp
            )

            # Get feature names
            feature_columns = df_features.columns.tolist()
            feature_columns = [col for col in feature_columns if col != 'returns']

            # Evaluate model with feature names
            metrics = model_trainer.evaluate(X_test, y_test, feature_names=feature_columns)

            # Save model
            model_trainer.save_model(model_path)

        elif model_type == 'tft':
            # Train TFT model using standardized path
            model_path = get_model_path(
                symbol=symbol,
                timeframe=timeframe,
                model_type='tft'
            )

            # Create model trainer
            model_trainer = TFTModelTrainer(
                input_size=input_size,
                hidden_size=hidden_size,
                num_attention_heads=num_attention_heads,
                dropout_rate=dropout_rate,
                num_lstm_layers=num_layers
            )

            # Train model with memory-efficient approach
            logger.info(f"Training TFT model with hidden_size={hidden_size}, num_attention_heads={num_attention_heads}...")

            # Create data loaders for memory-efficient training
            from model.dataset import create_data_loaders
            train_loader, val_loader = create_data_loaders(
                X_train=X_train,
                y_train=y_train,
                X_val=X_val,
                y_val=y_val,
                batch_size=batch_size,
                num_workers=4,
                pin_memory=True
            )

            # Train with data loaders
            model_trainer.train_with_loaders(
                train_loader=train_loader,
                val_loader=val_loader,
                epochs=epochs,
                patience=patience,
                use_amp=use_amp
            )

            # Get feature names
            feature_columns = df_features.columns.tolist()
            feature_columns = [col for col in feature_columns if col != 'returns']

            # Evaluate model with feature names
            metrics = model_trainer.evaluate(X_test, y_test, feature_names=feature_columns)

            # Save model
            model_trainer.save_model(model_path)

        elif model_type == 'arima':
            # Train ARIMA model
            from model.arima_model import ARIMAModelTrainer

            model_path = get_model_path(
                model_type=model_type,
                symbol=symbol,
                timeframe=timeframe
            )

            # Create model
            model = ARIMAModelTrainer(auto_determine=True)

            # Train model
            logger.info(f"Training ARIMA model for {timeframe}...")

            # Get the returns data and ensure it's a simple numpy array
            returns_data = df_features['returns'].dropna().values

            # Train the model with the numpy array
            model.train(returns_data)

            # Evaluate model
            metrics = model.evaluate(returns_data)

            # Save model
            model.save_model(model_path)

            # Save metrics
            metrics_path = get_metrics_path(
                model_type=model_type,
                symbol=symbol,
                timeframe=timeframe
            )

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(metrics_path), exist_ok=True)

            # Save metrics to file
            with open(metrics_path, 'w') as f:
                json.dump(metrics, f, indent=4)

            logger.info(f"Saved metrics to {metrics_path}")

        elif model_type in ['lstm_arima', 'tft_arima']:
            # Train ensemble model using standardized path
            from model.ensemble_model import EnsembleModel

            # Determine base model type
            base_model_type = 'lstm' if model_type == 'lstm_arima' else 'tft'

            # Use standardized ensemble model path
            model_path = get_model_path(
                symbol=symbol,
                timeframe=timeframe,
                model_type=model_type
            )

            # Create and train base model
            if base_model_type == 'lstm':
                base_model = ModelTrainer(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    output_size=output_size
                )
            else:  # TFT
                base_model = TFTModelTrainer(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_attention_heads=num_attention_heads,
                    dropout_rate=dropout_rate,
                    num_lstm_layers=num_layers
                )

            # Train base model
            logger.info(f"Training {base_model_type} component of ensemble model...")
            base_model.train(
                X_train=X_train,
                y_train=y_train,
                X_val=X_val,
                y_val=y_val,
                epochs=epochs,
                batch_size=batch_size,
                patience=patience,
                use_amp=use_amp
            )

            # Create and train ARIMA model
            from model.arima_model import ARIMAModelTrainer
            arima_model = ARIMAModelTrainer(auto_determine=True)
            logger.info(f"Training ARIMA component of ensemble model...")
            arima_model.train(df_features['returns'].dropna())

            # Create ensemble model
            ensemble = EnsembleModel(
                models=[base_model, arima_model],
                model_types=[base_model_type, 'arima'],
                weights=[0.7, 0.3]  # Default weights
            )

            # Evaluate ensemble model
            feature_columns = df_features.columns.tolist()
            feature_columns = [col for col in feature_columns if col != 'returns']
            metrics = ensemble.evaluate(X_test, y_test, feature_names=feature_columns)

            # Save ensemble model
            ensemble.save_model(model_path)

        else:
            logger.error(f"Unknown model type: {model_type}")
            return False

        # Log metrics
        logger.info(f"Model {model_name} ({model_type}) trained and evaluated:")
        for metric_name, metric_value in metrics.items():
            logger.info(f"  {metric_name}: {metric_value:.6f}")

        logger.info(f"Model saved to {model_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to train {timeframe} {model_type} model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def prepare_data(
    symbols: List[str],
    timeframes: List[str],
    start_date: str = None,
    end_date: str = None
) -> Dict[str, Dict[str, pd.DataFrame]]:
    """
    Prepare data for model training by collecting from MT5 terminals.

    Args:
        symbols: List of symbols to prepare data for
        timeframes: List of timeframes to prepare data for
        start_date: Start date for data collection
        end_date: End date for data collection

    Returns:
        Dict[str, Dict[str, pd.DataFrame]]: Data for each symbol and timeframe
    """
    logger.info("Preparing data for model training")

    # Initialize MT5 connector
    connector = MT5Connector()

    # Initialize all terminals
    logger.info("Initializing all terminals")
    terminal_status = initialize_all_terminals(max_retries=3, retry_delay=5)

    # Get list of initialized terminals
    initialized_terminals = [tid for tid, status in terminal_status.items() if status['initialized']]

    if not initialized_terminals:
        logger.error("No terminals initialized. Cannot proceed with data collection.")
        return {}

    logger.info(f"Initialized terminals: {initialized_terminals}")

    # Initialize data collector
    collector = DataCollector(connector)

    # Set default dates if not provided
    if start_date is None:
        # Default to 3 years ago
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y-%m-%d')

    if end_date is None:
        # Default to current date
        end_date = datetime.now().strftime('%Y-%m-%d')

    logger.info(f"Collecting data from {start_date} to {end_date}")

    # Collect data for each symbol and timeframe
    data = {}

    for symbol in symbols:
        data[symbol] = {}

        for timeframe in timeframes:
            logger.info(f"Collecting data for {symbol} ({timeframe})")

            # Collect data from all terminals
            df = collector.collect_data_from_all_terminals(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )

            if df is not None and len(df) > 0:
                logger.info(f"Collected {len(df)} rows of data for {symbol} ({timeframe})")
                data[symbol][timeframe] = df
            else:
                logger.error(f"Failed to collect data for {symbol} ({timeframe})")

    return data

def prepare_features_target(df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
    """
    Prepare features and target from dataframe.

    Args:
        df: DataFrame with OHLCV data and technical indicators

    Returns:
        Tuple[np.ndarray, np.ndarray]: Features and target
    """
    # Make a copy to avoid modifying the original dataframe
    df_copy = df.copy()

    # Set time as index if it's not already
    if 'time' in df_copy.columns:
        df_copy.set_index('time', inplace=True)

    # Drop any rows with NaN values
    df_copy.dropna(inplace=True)

    # Define target as next period's close price change
    df_copy['target'] = df_copy['close'].pct_change(1).shift(-1)

    # Drop rows with NaN in target
    df_copy.dropna(inplace=True)

    # Define features (all columns except target)
    feature_cols = [col for col in df_copy.columns if col != 'target']

    # Convert to numpy arrays
    X = df_copy[feature_cols].values
    y = df_copy['target'].values

    return X, y

def split_data(
    X: np.ndarray,
    y: np.ndarray,
    train_size: float = 0.7,
    val_size: float = 0.15
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Split data into train, validation, and test sets.

    Args:
        X: Features
        y: Target
        train_size: Proportion of data to use for training
        val_size: Proportion of data to use for validation

    Returns:
        Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
            X_train, X_val, X_test, y_train, y_val, y_test
    """
    # Calculate split indices
    n = len(X)
    train_end = int(n * train_size)
    val_end = train_end + int(n * val_size)

    # Split data
    X_train = X[:train_end]
    X_val = X[train_end:val_end]
    X_test = X[val_end:]

    y_train = y[:train_end]
    y_val = y[train_end:val_end]
    y_test = y[val_end:]

    return X_train, X_val, X_test, y_train, y_val, y_test

def compare_and_select_models(
    symbols: List[str],
    timeframes: List[str],
    output_dir: str = 'model'
):
    """
    Compare and select the best models for each symbol and timeframe.

    Args:
        symbols: List of symbols to compare models for
        timeframes: List of timeframes to compare models for
        output_dir: Output directory for reports and visualizations
    """
    logger.info("Comparing and selecting models")

    # Initialize model comparison
    model_comparison = ModelComparison(symbols)

    # Compare all models
    comparison_results = model_comparison.compare_all_models()

    # Generate comparison report
    report_dir = os.path.join(output_dir, 'reports')
    report_path = model_comparison.generate_comparison_report(report_dir)

    logger.info(f"Model comparison report saved to {report_path}")

    # Initialize model selector
    model_selector = ModelSelector(symbols)

    # Select best models
    selected_models = model_selector.select_models()

    # Save model assignments
    assignments_path = os.path.join(output_dir, 'assignments.json')
    model_selector.save_model_assignments(assignments_path)

    logger.info(f"Model assignments saved to {assignments_path}")

    # Plot model selection
    visualizations_dir = os.path.join(output_dir, 'visualizations')
    model_selector.plot_model_selection(visualizations_dir)

    logger.info(f"Model selection visualizations saved to {visualizations_dir}")

def main():
    """
    Main function to parse arguments and train models.
    """
    # Register safe classes for PyTorch serialization
    register_safe_classes()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train models for cryptocurrency trading')
    parser.add_argument('--symbols', nargs='+', default=['ETHUSD.a', 'BTCUSD.a'],
                        help='Symbols to train models for')
    parser.add_argument('--timeframes', type=str, default='M5,M15,M30,H1,H4',
                        help='Comma-separated list of timeframes to train models for')
    parser.add_argument('--model-types', type=str, default='lstm,tft,arima,lstm_arima,tft_arima',
                        help='Comma-separated list of model types to train (lstm, tft, arima, lstm_arima, tft_arima)')
    parser.add_argument('--start-date', type=str, default=None,
                        help='Start date for training data (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default=None,
                        help='End date for training data (YYYY-MM-DD)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs for training')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size for training')
    parser.add_argument('--sequence-length', type=int, default=10,
                        help='Sequence length for sequence models')
    parser.add_argument('--hidden-size', type=int, default=64,
                        help='Hidden size for sequence models')
    parser.add_argument('--num-layers', type=int, default=2,
                        help='Number of layers for sequence models')
    parser.add_argument('--patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--num-attention-heads', type=int, default=4,
                        help='Number of attention heads for TFT model')
    parser.add_argument('--dropout-rate', type=float, default=0.1,
                        help='Dropout rate for sequence models')
    parser.add_argument('--max-samples', type=int, default=50000,
                        help='Maximum number of samples to use for training')
    parser.add_argument('--no-amp', action='store_true',
                        help='Disable Automatic Mixed Precision for training')
    parser.add_argument('--terminal-id', type=int, default=None,
                        help='Terminal ID to use for model type selection')
    parser.add_argument('--force-retrain', action='store_true',
                        help='Force retraining of all models')
    parser.add_argument('--compare-only', action='store_true',
                        help='Only compare existing models without training')
    parser.add_argument('--select-only', action='store_true',
                        help='Only select best models without training')
    parser.add_argument('--collect-data', action='store_true',
                        help='Collect fresh data from MT5 terminals')
    parser.add_argument('--output-dir', type=str, default='model',
                        help='Output directory for models and reports')

    args = parser.parse_args()

    # Process arguments
    symbols = args.symbols
    timeframes = args.timeframes.split(',')
    model_types = args.model_types.split(',')
    use_amp = not args.no_amp

    # If terminal ID is provided, determine model type
    if args.terminal_id is not None:
        # Only override model_types if not explicitly provided
        if args.model_types == 'lstm,tft,arima,lstm_arima,tft_arima':  # Default value
            model_type = get_terminal_model_type(args.terminal_id)
            model_types = [model_type]
            logger.info(f"Using model type {model_type} for terminal {args.terminal_id}")
        else:
            logger.info(f"Using explicitly provided model types: {model_types}")

    # Create logs directory
    os.makedirs('logs', exist_ok=True)

    # If only comparing or selecting models, skip data preparation and training
    if args.compare_only or args.select_only:
        if args.compare_only:
            logger.info("Only comparing existing models")
        if args.select_only:
            logger.info("Only selecting best models")

        compare_and_select_models(symbols, timeframes, args.output_dir)
        return

    # Prepare data
    if args.collect_data:
        # Collect fresh data from MT5 terminals
        logger.info("Collecting fresh data from MT5 terminals")
        data = prepare_data(symbols, timeframes, args.start_date, args.end_date)
    else:
        # Load data from storage
        data = {}
        for symbol in symbols:
            data[symbol] = {}
            for timeframe in timeframes:
                # Use terminal_id if provided, otherwise use terminal 1 (primary terminal)
                terminal_id = args.terminal_id if args.terminal_id is not None else 1
                logger.info(f"Loading data for {symbol} ({timeframe}) from terminal {terminal_id}")
                df = load_timeframe_data(timeframe, args.start_date, args.end_date, terminal_id=terminal_id, symbol=symbol)
                if df is not None:
                    data[symbol][timeframe] = df
                    logger.info(f"Successfully loaded {len(df)} rows for {symbol} ({timeframe})")
                else:
                    logger.error(f"Failed to load data for {symbol} ({timeframe})")

    # Train models for each symbol, timeframe, and model type
    for symbol in symbols:
        if symbol not in data:
            logger.error(f"No data found for symbol {symbol}")
            continue

        for timeframe in timeframes:
            if timeframe not in data[symbol]:
                logger.error(f"No data found for {symbol} ({timeframe})")
                continue

            logger.info(f"Processing {symbol} ({timeframe})")
            df = data[symbol][timeframe]

            for model_type in model_types:
                logger.info(f"Training {model_type} model for {symbol} ({timeframe})")

                # Train model
                success = train_model(
                    timeframe=timeframe,
                    df=df,
                    model_type=model_type,
                    symbol=symbol,
                    start_date=args.start_date,
                    end_date=args.end_date,
                    epochs=args.epochs,
                    batch_size=args.batch_size,
                    sequence_length=args.sequence_length,
                    hidden_size=args.hidden_size,
                    num_layers=args.num_layers,
                    patience=args.patience,
                    num_attention_heads=args.num_attention_heads,
                    dropout_rate=args.dropout_rate,
                    max_samples=args.max_samples,
                    use_amp=use_amp
                )

                if success:
                    logger.info(f"Successfully trained {model_type} model for {symbol} ({timeframe})")
                else:
                    logger.error(f"Failed to train {model_type} model for {symbol} ({timeframe})")

    # Compare and select models
    if not args.compare_only and not args.select_only:
        logger.info("Comparing and selecting models")
        compare_and_select_models(symbols, timeframes, args.output_dir)

    logger.info("Model training complete")

if __name__ == "__main__":
    main()