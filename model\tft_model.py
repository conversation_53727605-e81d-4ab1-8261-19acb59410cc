"""
Temporal Fusion Transformer (TFT) Model Module.
This module implements the TFT model architecture for time series forecasting.

Reference: https://arxiv.org/abs/1912.09363
"""
import logging
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from typing import Optional

# Configure logger
logger = logging.getLogger('model.tft_model')

class GatedResidualNetwork(nn.Module):
    """
    Gated Residual Network as described in the TFT paper.
    """
    def __init__(self, input_size, hidden_size, output_size, dropout_rate):
        """
        Initialize the GRN.

        Args:
            input_size: Size of input
            hidden_size: Size of hidden layer
            output_size: Size of output
            dropout_rate: Dropout rate
        """
        super().__init__()
        self.input_size = input_size
        self.output_size = output_size
        self.hidden_size = hidden_size
        self.dropout_rate = dropout_rate

        # Layer normalization
        self.norm = nn.LayerNorm(input_size)

        # FC layers
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(dropout_rate)

        # Skip connection if input and output dimensions differ
        self.skip_connection = nn.Linear(input_size, output_size) if input_size != output_size else nn.Identity()

        # Gating layer
        self.gate = nn.Linear(input_size, output_size)

    def forward(self, x):
        """Forward pass through the GRN."""
        # Normalize input
        normalized_x = self.norm(x)

        # Main branch
        hidden = F.elu(self.fc1(normalized_x))
        hidden = self.dropout(hidden)
        hidden = F.elu(self.fc2(hidden))
        hidden = self.dropout(hidden)

        # Skip connection
        skip = self.skip_connection(x)

        # Gating mechanism
        gate = torch.sigmoid(self.gate(normalized_x))

        # Apply gating and residual connection
        output = gate * hidden + skip

        return output

class TemporalFusionTransformer(nn.Module):
    """
    Temporal Fusion Transformer model for time series forecasting.
    """
    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_attention_heads: int = 4,
        dropout_rate: float = 0.1,
        num_lstm_layers: int = 2
    ):
        """
        Initialize the Temporal Fusion Transformer model.

        Args:
            input_size: Number of input features
            hidden_size: Size of hidden layers
            num_attention_heads: Number of attention heads
            dropout_rate: Dropout rate
            num_lstm_layers: Number of LSTM layers
        """
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.dropout_rate = dropout_rate
        self.num_lstm_layers = num_lstm_layers

        # Variable selection network
        self.variable_selection = GatedResidualNetwork(
            input_size=input_size,
            hidden_size=hidden_size,
            output_size=hidden_size,
            dropout_rate=dropout_rate
        )

        # LSTM encoder
        self.lstm = nn.LSTM(
            input_size=hidden_size,
            hidden_size=hidden_size,
            num_layers=num_lstm_layers,
            batch_first=True,
            dropout=dropout_rate if num_lstm_layers > 1 else 0
        )

        # Layer normalization before attention
        self.pre_attention_norm = nn.LayerNorm(hidden_size)

        # Multi-head attention
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_attention_heads,
            dropout=dropout_rate
        )

        # Dropout after attention
        self.attention_dropout = nn.Dropout(dropout_rate)

        # Final gated residual network
        self.final_grn = GatedResidualNetwork(
            input_size=hidden_size,
            hidden_size=hidden_size,
            output_size=hidden_size,
            dropout_rate=dropout_rate
        )

        # Output layer
        self.output_layer = nn.Linear(hidden_size, 1)

    def forward(self, x):
        """
        Forward pass through the TFT model.

        Args:
            x: Input tensor of shape [batch_size, sequence_length, input_size]

        Returns:
            Tensor: Output tensor of shape [batch_size, 1]
        """
        batch_size, seq_length, _ = x.shape

        # Variable selection
        var_selected = torch.zeros(batch_size, seq_length, self.hidden_size, device=x.device, dtype=x.dtype)
        for i in range(seq_length):
            var_selected[:, i, :] = self.variable_selection(x[:, i, :])

        # LSTM encoder
        lstm_out, _ = self.lstm(var_selected)

        # Self-attention
        # First, normalize the LSTM output
        norm_lstm_out = self.pre_attention_norm(lstm_out)

        # Transpose for attention: [batch_size, seq_length, hidden] -> [seq_length, batch_size, hidden]
        norm_lstm_out_t = norm_lstm_out.transpose(0, 1)

        # Apply self-attention
        attn_out, _ = self.attention(
            query=norm_lstm_out_t,
            key=norm_lstm_out_t,
            value=norm_lstm_out_t
        )

        # Transpose back: [seq_length, batch_size, hidden] -> [batch_size, seq_length, hidden]
        attn_out = attn_out.transpose(0, 1)

        # Residual connection and dropout
        attn_out = lstm_out + self.attention_dropout(attn_out)

        # Final GRN on the last time step
        final_hidden = self.final_grn(attn_out[:, -1, :])

        # Output layer
        output = self.output_layer(final_hidden)

        return output

class TFTModel:
    """
    TFT model wrapper class with training and evaluation methods.
    """
    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_attention_heads: int = 4,
        dropout_rate: float = 0.1,
        learning_rate: float = 0.001,
        num_lstm_layers: int = 2
    ):
        """
        Initialize the TFT model.

        Args:
            input_size: Number of input features
            hidden_size: Size of hidden layers
            num_attention_heads: Number of attention heads
            dropout_rate: Dropout rate
            learning_rate: Learning rate for optimizer
            num_lstm_layers: Number of LSTM layers
        """
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.num_lstm_layers = num_lstm_layers

        # Set device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")

        # Create model
        self.model = TemporalFusionTransformer(
            input_size=input_size,
            hidden_size=hidden_size,
            num_attention_heads=num_attention_heads,
            dropout_rate=dropout_rate,
            num_lstm_layers=num_lstm_layers
        ).to(self.device)

        # Create optimizer
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=learning_rate
        )

        # Create loss function
        self.criterion = nn.MSELoss()

        # Initialize history
        self.history = None

    def fit(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        epochs: int = 100,
        batch_size: int = 32,
        patience: int = 10,
        verbose: bool = True
    ):
        """
        Train the TFT model.

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            epochs: Number of epochs
            batch_size: Batch size
            patience: Patience for early stopping
            verbose: Whether to print progress

        Returns:
            self: Trained model
        """
        # Convert numpy arrays to tensors
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(self.device)
        y_train_tensor = torch.tensor(y_train, dtype=torch.float32).to(self.device)

        # Create dataset and dataloader
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

        # Validation data
        if X_val is not None and y_val is not None:
            X_val_tensor = torch.tensor(X_val, dtype=torch.float32).to(self.device)
            y_val_tensor = torch.tensor(y_val, dtype=torch.float32).to(self.device)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(val_dataset, batch_size=batch_size)
            do_validation = True
        else:
            do_validation = False

        # Initialize history
        self.history = {
            'train_loss': [],
            'val_loss': []
        }

        # Early stopping variables
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None

        # Training loop
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0
            for X_batch, y_batch in train_loader:
                # Forward pass
                self.optimizer.zero_grad()
                y_pred = self.model(X_batch)
                loss = self.criterion(y_pred, y_batch.unsqueeze(1))

                # Backward pass
                loss.backward()
                self.optimizer.step()

                train_loss += loss.item()

            train_loss /= len(train_loader)
            self.history['train_loss'].append(train_loss)

            # Validation
            if do_validation:
                self.model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for X_batch, y_batch in val_loader:
                        y_pred = self.model(X_batch)
                        loss = self.criterion(y_pred, y_batch.unsqueeze(1))
                        val_loss += loss.item()

                val_loss /= len(val_loader)
                self.history['val_loss'].append(val_loss)

                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    best_model_state = self.model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        if verbose:
                            logger.info(f"Early stopping at epoch {epoch+1}")
                        break

                if verbose and (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            else:
                if verbose and (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}")

        # Load best model if early stopping occurred
        if do_validation and best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        return self

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the TFT model.

        Args:
            X: Input features

        Returns:
            np.ndarray: Predictions
        """
        # Convert to tensor
        X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)

        # Set model to evaluation mode
        self.model.eval()

        # Make predictions
        with torch.no_grad():
            predictions = self.model(X_tensor)

        # Convert to numpy
        return predictions.cpu().numpy()

    def save(self, path: str):
        """
        Save the model to disk.

        Args:
            path: Path to save the model
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # Save model
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_attention_heads': self.num_attention_heads,
            'dropout_rate': self.dropout_rate,
            'learning_rate': self.learning_rate,
            'num_lstm_layers': self.num_lstm_layers,
            'history': self.history
        }, path)

        logger.info(f"Model saved to {path}")

    @classmethod
    def load(cls, path: str):
        """
        Load a model from disk with backward compatibility.

        Args:
            path: Path to load the model from

        Returns:
            TFTModel: Loaded model
        """
        # Load checkpoint with secure loading
        try:
            # Try secure loading first (PyTorch 2.0+)
            checkpoint = torch.load(path, map_location=torch.device('cpu'), weights_only=True)
        except Exception:
            # Fallback to legacy loading for older models
            logger.warning(f"Secure loading failed for {path}, using legacy mode")
            checkpoint = torch.load(path, map_location=torch.device('cpu'), weights_only=False)

        # Handle backward compatibility for missing parameters
        default_params = {
            'input_size': 111,  # Default feature count
            'hidden_size': 128,
            'num_attention_heads': 8,
            'dropout_rate': 0.1,
            'learning_rate': 0.001,
            'num_lstm_layers': 2
        }

        # Use checkpoint values if available, otherwise use defaults
        model_params = {}
        for param, default_value in default_params.items():
            model_params[param] = checkpoint.get(param, default_value)
            if param not in checkpoint:
                logger.warning(f"Parameter '{param}' not found in checkpoint, using default value: {default_value}")

        # Create model
        model = cls(**model_params)

        # Load state dictionaries with error handling
        try:
            model.model.load_state_dict(checkpoint['model_state_dict'])
            logger.info(f"Successfully loaded model state dict from {path}")
        except Exception as e:
            logger.error(f"Failed to load model state dict: {e}")
            raise

        try:
            model.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            logger.info(f"Successfully loaded optimizer state dict from {path}")
        except Exception as e:
            logger.warning(f"Failed to load optimizer state dict: {e}, will use default optimizer")

        # Load history if available
        model.history = checkpoint.get('history', {'train_loss': [], 'val_loss': []})

        logger.info(f"Model loaded from {path} with parameters: {model_params}")
        return model

    def get_feature_importance(self, X: np.ndarray) -> np.ndarray:
        """
        Calculate feature importance using attention weights.

        Args:
            X: Input features

        Returns:
            np.ndarray: Feature importance scores
        """
        # Convert to tensor
        X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)

        # Set model to evaluation mode
        self.model.eval()

        # Get attention weights
        with torch.no_grad():
            # Forward pass through variable selection
            batch_size, seq_length, _ = X_tensor.shape
            var_selected = torch.zeros(batch_size, seq_length, self.hidden_size, device=X_tensor.device, dtype=X_tensor.dtype)
            for i in range(seq_length):
                var_selected[:, i, :] = self.model.variable_selection(X_tensor[:, i, :])

            # LSTM encoder
            lstm_out, _ = self.model.lstm(var_selected)

            # Normalize LSTM output
            norm_lstm_out = self.model.pre_attention_norm(lstm_out)

            # Transpose for attention
            norm_lstm_out_t = norm_lstm_out.transpose(0, 1)

            # Get attention weights
            _, attn_weights = self.model.attention(
                query=norm_lstm_out_t,
                key=norm_lstm_out_t,
                value=norm_lstm_out_t,
                need_weights=True
            )

        # Average attention weights across heads
        avg_attn_weights = attn_weights.mean(dim=0)

        # Convert to numpy
        return avg_attn_weights.cpu().numpy()
