# Component Relationships

This document describes the detailed relationships and dependencies between components in the MT5 Trading System.

## Overview

The MT5 Trading System is built with a modular architecture where components have clear interfaces and well-defined dependencies. The system follows a hierarchical structure with the TradingBot as the main orchestrator, coordinating all other components through standardized interfaces.

## Component Hierarchy

```
TradingBot (Main Orchestrator)
├── Multi-Terminal Mode
│   └── TerminalManager
│       ├── MT5Connector (shared)
│       ├── ModelManager (shared)
│       ├── DataCollector (shared)
│       ├── FeatureEngineer (shared)
│       └── ModelEvaluator (shared)
└── Single-Terminal Mode
    ├── MT5Connector
    ├── OrderManager
    ├── TradingStrategy
    ├── ModelManager
    ├── DataCollector
    ├── FeatureEngineer
    └── ModelEvaluator

Shared Components:
├── PerformanceMonitor
├── Dashboard
├── ErrorTracker
└── Configuration (trading_config, credentials)
```

## Core Component Dependencies

### 1. TradingBot (Main Orchestrator)

**Dependencies:**
```python
# Core trading components
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from trading.trading_strategy import TradingStrategy
from trading.terminal_manager import TerminalManager

# Model components
from model.model_manager import ModelManager
from model.model_evaluator import ModelEvaluator

# Data components
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer

# Monitoring components
from monitoring.performance_monitor import PerformanceMonitor
from monitoring.dashboard import Dashboard
```

**Initialization Pattern:**
```python
class TradingBot:
    def __init__(self, terminal_id=None, multi_terminal=False):
        # Initialize core connector
        self.mt5_connector = MT5Connector()

        # Initialize data components
        self.data_collector = DataCollector(self.mt5_connector)
        self.feature_engineer = FeatureEngineer(data_collector=self.data_collector)

        # Initialize model components
        self.model_manager = ModelManager()
        self.model_evaluator = ModelEvaluator()

        # Initialize monitoring
        self.performance_monitor = PerformanceMonitor()
        self.dashboard = Dashboard(self.performance_monitor)

        if self.multi_terminal:
            # Multi-terminal mode
            self.terminal_manager = TerminalManager(
                mt5_connector=self.mt5_connector,
                model_manager=self.model_manager,
                data_collector=self.data_collector,
                feature_engineer=self.feature_engineer,
                model_evaluator=self.model_evaluator
            )
        else:
            # Single-terminal mode
            self.order_manager = OrderManager(self.mt5_connector)
            self.trading_strategy = TradingStrategy(
                mt5_connector=self.mt5_connector,
                order_manager=self.order_manager,
                model_manager=self.model_manager,
                data_collector=self.data_collector,
                feature_engineer=self.feature_engineer,
                model_evaluator=self.model_evaluator
            )
```

### 2. TerminalManager (Multi-Terminal Coordinator)

**Dependencies:**
```python
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_manager import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator
```

**Component Relationships:**
```python
class TerminalManager:
    def __init__(self, mt5_connector, model_manager, data_collector,
                 feature_engineer, model_evaluator):
        # Shared components
        self.mt5_connector = mt5_connector
        self.model_manager = model_manager
        self.data_collector = data_collector
        self.feature_engineer = feature_engineer
        self.model_evaluator = model_evaluator

        # Terminal-specific components
        self.order_managers = {}  # One per terminal
        self.trading_strategies = {}  # One per terminal

        # Initialize per-terminal components
        for terminal_id in range(1, 6):
            self.order_managers[terminal_id] = OrderManager(mt5_connector)
            self.trading_strategies[terminal_id] = TradingStrategy(
                mt5_connector=mt5_connector,
                order_manager=self.order_managers[terminal_id],
                model_manager=model_manager,
                data_collector=data_collector,
                feature_engineer=feature_engineer,
                model_evaluator=model_evaluator,
                terminal_id=terminal_id
            )
```

### 3. TradingStrategy (Signal Generation)

**Dependencies:**
```python
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_manager import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator
```

**Method Call Flow:**
```python
class TradingStrategy:
    def run_trading_cycle(self):
        # 1. Generate signals using ModelManager
        signals = self.generate_signals()

        # 2. Aggregate multi-timeframe signals
        final_signal = self.aggregate_timeframe_signals(signals)

        # 3. Execute trades through OrderManager
        if self._should_trade(final_signal):
            self.order_manager.execute_order(
                order_type=final_signal['direction'],
                volume=final_signal['volume']
            )

    def generate_signals(self):
        for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
            # Get data through DataCollector
            df = self.data_collector.get_latest_data(timeframe=timeframe)

            # Process features through FeatureEngineer
            features = self.feature_engineer.create_features(df)

            # Generate prediction through ModelManager
            model_name = f"{self.symbol}_{timeframe}_{self.model_type}"
            prediction = self.model_manager.predict(model_name, features)

            # Convert to signal
            signal = self._prediction_to_signal(prediction)
```

### 4. DataCollector (Data Management)

**Dependencies:**
```python
from trading.mt5_connector import MT5Connector
from data.technical_indicators import TechnicalIndicators
```

**Component Relationships:**
```python
class DataCollector:
    def __init__(self, mt5_connector):
        self.mt5 = mt5_connector  # Direct dependency
        self.technical_indicators = TechnicalIndicators()

    def collect_historical_data(self, symbol, timeframe, start_date, terminal_id):
        # 1. Get raw data from MT5Connector
        df = self.mt5.get_historical_data(
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date
        )

        # 2. Process with technical indicators
        processed_data = self.process_data_with_indicators(df)

        # 3. Save using standardized paths
        self.save_data(processed_data, symbol, timeframe, terminal_id)

        return processed_data
```

### 5. FeatureEngineer (Feature Processing)

**Dependencies:**
```python
from data.data_collector import DataCollector
from data.technical_indicators import TechnicalIndicators
```

**Component Relationships:**
```python
class FeatureEngineer:
    def __init__(self, data_collector=None):
        self.data_collector = data_collector  # Optional dependency
        self.technical_indicators = TechnicalIndicators()

    def create_features(self, df):
        # 1. Calculate technical indicators
        features = self.technical_indicators.calculate_all_indicators(df)

        # 2. Create additional features
        features = self._add_price_features(features)
        features = self._add_volume_features(features)

        # 3. Handle missing values
        features = self._handle_missing_values(features)

        return features

    def create_sequences(self, df, target_column, sequence_length):
        # Create sequences for model training
        X, y = self._create_sequences_internal(df, target_column, sequence_length)
        return X, y
```

### 6. ModelManager (Model Coordination)

**Dependencies:**
```python
from model.lstm_model import LSTMModelTrainer
from model.tft_model_trainer import TFTModelTrainer
from model.arima_model import ARIMAModelTrainer
from model.ensemble_model import EnsembleModel
```

**Component Relationships:**
```python
class ModelManager:
    def __init__(self):
        self.models = {}  # Model instances
        self.model_types = {}  # Model type mapping
        self.model_paths = {}  # Model file paths

    def create_model(self, model_name, model_type, **kwargs):
        # Create model based on type
        if model_type == 'lstm':
            model = LSTMModelTrainer(**kwargs)
        elif model_type == 'tft':
            model = TFTModelTrainer(**kwargs)
        elif model_type == 'arima':
            model = ARIMAModelTrainer(**kwargs)
        elif model_type in ['lstm_arima', 'tft_arima']:
            model = EnsembleModel(model_type=model_type, **kwargs)

        self.models[model_name] = model
        self.model_types[f"{model_name}_type"] = model_type

    def predict(self, model_name, X):
        # Get model and generate prediction
        model = self.models[model_name]
        prediction = model.predict(X)
        return prediction
```

### 7. OrderManager (Trade Execution)

**Dependencies:**
```python
from trading.mt5_connector import MT5Connector
from config.trading_config import TRADING_CONFIG
```

**Component Relationships:**
```python
class OrderManager:
    def __init__(self, mt5_connector):
        self.mt5 = mt5_connector  # Direct dependency for order execution

        # Load configuration
        self.symbol = TRADING_CONFIG['symbol']
        self.lot_size = TRADING_CONFIG['lot_size']
        self.max_risk_per_trade = TRADING_CONFIG['risk_management']['max_risk_per_trade']

    def execute_order(self, order_type, volume, stop_loss=None, take_profit=None):
        # 1. Validate order parameters
        if not self._validate_order(order_type, volume):
            return None

        # 2. Check risk limits
        if not self._check_risk_limits():
            return None

        # 3. Execute through MT5Connector
        ticket = self.mt5.place_order(
            symbol=self.symbol,
            order_type=order_type,
            volume=volume,
            sl=stop_loss,
            tp=take_profit
        )

        return ticket
```

## Data Flow Between Components

### 1. Data Collection Flow
```
MT5Connector.get_historical_data()
    ↓
DataCollector.collect_historical_data()
    ↓
TechnicalIndicators.calculate_all_indicators()
    ↓
FeatureEngineer.create_features()
    ↓
FeatureEngineer.create_sequences()
    ↓
ModelManager.train_model()
```

### 2. Signal Generation Flow
```
DataCollector.get_latest_data()
    ↓
FeatureEngineer.create_features()
    ↓
ModelManager.predict()
    ↓
TradingStrategy.generate_signals()
    ↓
TradingStrategy.aggregate_timeframe_signals()
    ↓
OrderManager.execute_order()
    ↓
MT5Connector.place_order()
```

### 3. Multi-Terminal Flow
```
TradingBot.start()
    ↓
TerminalManager.run_all_terminals()
    ↓
For each terminal:
    TradingStrategy.run_trading_cycle()
        ↓
    OrderManager.execute_order()
        ↓
    MT5Connector.place_order()
```

## Component Communication Patterns

### 1. Dependency Injection Pattern
Components receive their dependencies through constructor injection:
```python
# TradingStrategy receives all its dependencies
strategy = TradingStrategy(
    mt5_connector=mt5_connector,
    order_manager=order_manager,
    model_manager=model_manager,
    data_collector=data_collector,
    feature_engineer=feature_engineer,
    model_evaluator=model_evaluator
)
```

### 2. Shared Resource Pattern
Some components are shared across multiple terminals:
```python
# Shared components in TerminalManager
self.mt5_connector = mt5_connector  # Shared across all terminals
self.model_manager = model_manager  # Shared model repository
self.data_collector = data_collector  # Shared data access
```

### 3. Factory Pattern
ModelManager creates models based on type:
```python
def create_model(self, model_name, model_type, **kwargs):
    if model_type == 'lstm':
        return LSTMModelTrainer(**kwargs)
    elif model_type == 'tft':
        return TFTModelTrainer(**kwargs)
    # ... etc
```

## Error Handling and Recovery

### 1. Connection Recovery
```python
# MT5Connector handles connection failures
if not self.mt5.check_connection():
    logger.error("MT5 connection lost, attempting reconnection...")
    self.mt5.reconnect()
```

### 2. Model Fallbacks
```python
# ModelManager provides fallback models
try:
    prediction = self.model_manager.predict(model_name, X)
except Exception as e:
    logger.error(f"Model {model_name} failed, using fallback")
    prediction = self.model_manager.predict_fallback(X)
```

### 3. Data Validation
```python
# DataCollector validates data quality
if df is None or len(df) == 0:
    logger.error("No data received, skipping processing")
    return None
```

## Configuration Management

### 1. Centralized Configuration
```python
# All components use centralized configuration
from config.trading_config import TRADING_CONFIG
from config.credentials import MT5_TERMINALS

# Components access configuration consistently
self.symbol = TRADING_CONFIG['symbol']
self.terminals = MT5_TERMINALS
```

### 2. Path Standardization
```python
# Standardized path utilities used across components
from utils.path_utils import get_model_path, get_terminal_data_path

# Consistent path generation
model_path = get_model_path(model_type, symbol, timeframe)
data_path = get_terminal_data_path(terminal_id, symbol, timeframe)
```

Last updated: 2025-06-06 16:20:00
