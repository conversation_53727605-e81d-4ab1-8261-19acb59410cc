# Standardized Path Structure

This document outlines the standardized path structure used throughout the 3Mbot Trading System for data storage, model storage, and visualizations.

## Overview

The 3Mbot Trading System uses 5 MT5 terminals, each specializing in a specific model type:
- Terminal 1: ARIMA (Statistical time series forecasting)
- Terminal 2: LSTM (Deep learning sequence modeling)
- Terminal 3: TFT (Transformer-based forecasting)
- Terminal 4: LSTM+ARIMA ensemble (Hybrid approach)
- Terminal 5: TFT+ARIMA ensemble (Advanced hybrid approach)

Each terminal collects OHLCV data across all timeframes. The standardized path structure ensures consistent organization of data and models across all components.

## Historical Data Paths

Historical OHLCV data is stored in the following structure:

```
data/storage/historical/{symbol}_{timeframe}/{date_range}_{timestamp}.parquet
data/storage/historical/{symbol}_{timeframe}/latest.parquet
```

Examples:
```
data/storage/historical/BTCUSD.a_M5/2023-04-26_2025-04-25_20250425_140021.parquet
data/storage/historical/BTCUSD.a_M5/latest.parquet
```

The `latest.parquet` file is always updated with the most recent data for quick access.

## Processed Data Paths

Processed data with technical indicators is stored in a similar structure:

```
data/processed/{symbol}_{timeframe}/{date_range}_{timestamp}.parquet
data/processed/{symbol}_{timeframe}/latest.parquet
```

## Model Paths

Models are stored in the following structure based on model type:

### LSTM Models (Terminal 2)
```
model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
```

Example:
```
model/saved_models/lstm/BTCUSD.a_M5_lstm.pth
```

### TFT Models (Terminal 3)
```
model/saved_models/tft/{symbol}_{timeframe}_tft.pth
```

Example:
```
model/saved_models/tft/BTCUSD.a_M5_tft.pth
```

### ARIMA Models (Terminal 1)
```
model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
```

Example:
```
model/saved_models/arima/BTCUSD.a_M5_arima.pkl
```

### Ensemble Models (Terminal 4: LSTM+ARIMA)
```
model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima.pkl
```

Example:
```
model/saved_models/ensemble/BTCUSD.a_M5_lstm_arima.pkl
```

### Ensemble Models (Terminal 5: TFT+ARIMA)
```
model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima.pkl
```

Example:
```
model/saved_models/ensemble/BTCUSD.a_M5_tft_arima.pkl
```

## Model Metrics Paths

Model metrics are stored alongside the models with a `_metrics.json` suffix:

```
model/saved_models/lstm/{symbol}_{timeframe}_lstm_metrics.json
model/saved_models/tft/{symbol}_{timeframe}_tft_metrics.json
model/saved_models/arima/{symbol}_{timeframe}_arima_metrics.json
model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima_metrics.json
model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima_metrics.json
```

## Visualization Paths

Visualizations are stored in the following structure:

```
model/visualizations/{model_type}/{symbol}_{timeframe}/{visualization_type}.png
```

Examples:
```
model/visualizations/lstm/BTCUSD.a_M5/predictions.png
model/visualizations/tft/BTCUSD.a_M5/predictions.png
model/visualizations/tft/BTCUSD.a_M5/training_history.png
model/visualizations/ensemble/BTCUSD.a_M5/model_comparison.png
```

Dashboard visualizations are stored in a separate directory:

```
dashboards/{timeframe}/{timeframe}_dashboard.html
dashboards/{timeframe}/model/{model_type}/{symbol}_{timeframe}_architecture.png
dashboards/{timeframe}/model/{model_type}/{symbol}_{timeframe}_training_history.png
```

## Utility Functions

The system provides utility functions to generate and validate these paths consistently:

### Data Path Utilities (`data/data_path_utils.py`)

#### Path Generation
- `get_historical_data_path(symbol, timeframe)`
- `get_latest_data_path(symbol, timeframe)`
- `get_processed_data_path(symbol, timeframe)`
- `list_historical_data_files(symbol, timeframe)`

#### Path Validation
- `validate_path_format(path, expected_format)`
- `validate_historical_data_path(path)`
- `check_data_consistency(data_path)`

### Model Path Utilities (`utils/path_utils.py`)

#### Path Generation
- `get_model_path(symbol, timeframe, model_type, terminal_id=None)`
- `get_metrics_path(symbol, timeframe, model_type, terminal_id=None)`
- `get_visualization_path(symbol, timeframe, model_type, visualization_type, terminal_id=None)`
- `get_model_type_for_terminal(terminal_id)`
- `get_terminal_data_path(terminal_id, symbol, timeframe, start_date=None, end_date=None)`
- `get_terminal_features_path(terminal_id, symbol, timeframe)`

#### Path Validation
- `validate_model_path(path)`
- `check_model_exists(symbol, timeframe, model_type, terminal_id=None)`
- `get_available_models(symbol, timeframe, terminal_id=None)`

For more details on path validation, see [Path Validation](path_validation.md).

## Terminal-Model Type Mapping

The mapping between terminal IDs and model types is as follows:

| Terminal ID | Model Type |
|-------------|------------|
| 1           | arima      |
| 2           | lstm       |
| 3           | tft        |
| 4           | lstm_arima |
| 5           | tft_arima  |

This mapping is used to determine which model type to use for each terminal. Each terminal is responsible for collecting OHLCV data, training its assigned model type, and executing trades based on that model's predictions.

## Conclusion

The standardized path structure ensures consistent organization of data, models, and visualizations across all components of the 3Mbot Trading System. This structure is designed to be:

- **Consistent**: Follows a consistent pattern across all components
- **Informative**: Includes relevant information in the path (symbol, timeframe, model type)
- **Organized**: Groups related files together
- **Efficient**: Optimizes data access with "latest" files for quick retrieval
- **Extensible**: Can accommodate new model types and visualizations
- **Maintainable**: Easy to understand and maintain

The standardized file paths for data storage and model saving are a key component of the system's architecture, enabling:

1. **Consistent Data Collection**: All terminals collect and store data in the same format
2. **Model Interoperability**: Models can be easily shared and combined in ensembles
3. **Simplified Development**: Developers can easily locate and work with files
4. **Reduced Errors**: Standardized paths reduce the risk of path-related errors
5. **Improved Maintainability**: Clear organization makes the system easier to maintain

By following this standardized path structure, the 3Mbot Trading System maintains a clean, organized, and efficient file system that supports its multi-terminal, multi-model approach.
