"""
Model Trainer Module.
This module handles training and evaluation of the LSTM model.
"""
import logging
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from model.lstm_model import TradingLSTM
from model.arima_model import ARIMAModelTrainer
from model.dataset import BatchTimeSeriesDataset, create_data_loaders
from config.trading_config import TRADING_CONFIG
from utils.torch_security import safe_load, safe_save, register_safe_classes

# Register custom classes as safe globals
register_safe_classes()

# Configure logger
logger = logging.getLogger('model.trainer')

class ModelTrainer:
    """
    Model Trainer class for training and evaluating LSTM models.
    """
    def __init__(
        self,
        input_size: int,
        hidden_size: int = 64,
        num_layers: int = 2,
        output_size: int = 1,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-5,
        device: str = None
    ):
        """
        Initialize ModelTrainer.

        Args:
            input_size: Number of input features
            hidden_size: Number of hidden units
            num_layers: Number of LSTM layers
            output_size: Number of output units
            learning_rate: Learning rate
            weight_decay: Weight decay for L2 regularization
            device: Device to use for training ('cpu', 'cuda', 'mps')
        """
        # Set device
        if device is None:
            self.device = torch.device("mps" if torch.backends.mps.is_available() else
                                      "cuda" if torch.cuda.is_available() else
                                      "cpu")
        else:
            self.device = torch.device(device)

        logger.info(f"Using device: {self.device}")

        # Initialize model
        self.model = TradingLSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size
        ).to(self.device)

        # Initialize optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # Initialize loss function
        self.criterion = nn.MSELoss()

        # Initialize learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )

        # Initialize training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'test_loss': None,
            'learning_rate': []
        }

        # Save hyperparameters
        self.hyperparams = {
            'input_size': input_size,
            'hidden_size': hidden_size,
            'num_layers': num_layers,
            'output_size': output_size,
            'learning_rate': learning_rate,
            'weight_decay': weight_decay
        }

        # Store model parameters directly as attributes
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size

    def train_with_loaders(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        epochs: int = 100,
        patience: int = 10,
        verbose: bool = True,
        use_amp: bool = True  # Use Automatic Mixed Precision for faster training
    ) -> Dict[str, List[float]]:
        """
        Train the model with data loaders for memory-efficient training.

        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            epochs: Number of epochs
            patience: Early stopping patience
            verbose: Whether to print progress
            use_amp: Whether to use Automatic Mixed Precision (faster training on compatible GPUs)

        Returns:
            Dict[str, List[float]]: Training history
        """
        # Determine if we should use AMP based on device compatibility
        use_amp = use_amp and (self.device.type == 'cuda' or self.device.type == 'mps')
        if use_amp:
            logger.info("Using Automatic Mixed Precision for faster training")
            # Use the updated API for PyTorch 2.0+
            try:
                # Modern PyTorch API (2.0+)
                scaler = torch.amp.GradScaler(device_type=self.device.type) if self.device.type == 'cuda' else None
            except (TypeError, ValueError):
                try:
                    # Newer PyTorch API (1.10+)
                    scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
                except (TypeError, ValueError):
                    # Fallback for older PyTorch versions
                    try:
                        # Try the most modern approach first
                        scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
                    except (TypeError, ValueError):
                        # Last resort fallback for very old PyTorch versions
                        logger.warning("Using deprecated GradScaler constructor. Consider upgrading PyTorch.")
                        # We need to use the deprecated constructor despite the warning
                        # because this is the fallback for very old PyTorch versions
                        # that don't support the new API
                        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None

        # Initialize early stopping variables
        best_val_loss = float('inf')
        early_stopping_counter = 0

        # Determine validation frequency (less frequent for large datasets)
        val_freq = max(1, min(5, epochs // 20))  # Validate at most every 5 epochs for large datasets

        # Training loop
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0

            # Use tqdm for progress tracking if verbose
            iterator = train_loader
            if verbose and epoch % 10 == 0:
                try:
                    from tqdm import tqdm
                    iterator = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
                except ImportError:
                    pass

            for batch_X, batch_y in iterator:
                # Move data to device
                batch_X = batch_X.to(self.device, non_blocking=True)
                batch_y = batch_y.to(self.device, non_blocking=True)

                # Forward pass with AMP if enabled
                if use_amp and self.device.type == 'cuda':
                    try:
                        # Use the updated API for PyTorch 2.0+
                        with torch.amp.autocast(device_type=self.device.type):
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1) if batch_y.dim() == 1 else batch_y)
                    except (TypeError, ValueError):
                        # Fallback for older PyTorch versions
                        try:
                            with torch.amp.autocast('cuda'):
                                outputs = self.model(batch_X)
                                loss = self.criterion(outputs, batch_y.unsqueeze(1) if batch_y.dim() == 1 else batch_y)
                        except (TypeError, ValueError):
                            # Last resort fallback
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1) if batch_y.dim() == 1 else batch_y)

                    # Backward pass with gradient scaling
                    self.optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.step(self.optimizer)
                    scaler.update()
                else:
                    # Standard forward pass
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y.unsqueeze(1) if batch_y.dim() == 1 else batch_y)

                    # Standard backward pass
                    self.optimizer.zero_grad()
                    loss.backward()

                    # Apply gradient clipping to prevent exploding gradients
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                    self.optimizer.step()

                train_loss += loss.item()

            # Calculate average training loss
            train_loss /= len(train_loader)

            # Validation
            self.model.eval()
            val_loss = 0.0
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    # Move data to device
                    batch_X = batch_X.to(self.device, non_blocking=True)
                    batch_y = batch_y.to(self.device, non_blocking=True)

                    # Forward pass
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y.unsqueeze(1) if batch_y.dim() == 1 else batch_y)
                    val_loss += loss.item()

            # Calculate average validation loss
            val_loss /= len(val_loader)

            # Update learning rate scheduler
            self.scheduler.step(val_loss)

            # Save history
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['learning_rate'].append(self.optimizer.param_groups[0]['lr'])

            # Print progress
            if verbose and (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

            # Check for early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                early_stopping_counter = 0
                # Save best model
                self.save_model('model/saved_models/best_model.pth')
            else:
                early_stopping_counter += 1
                if early_stopping_counter >= patience:
                    logger.info(f"Early stopping at epoch {epoch+1}")
                    break

        # Load best model
        self.load_model('model/saved_models/best_model.pth')

        return self.history

    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: np.ndarray,
        y_val: np.ndarray,
        batch_size: int = 32,
        epochs: int = 100,
        patience: int = 10,
        verbose: bool = True,
        use_amp: bool = True,  # Use Automatic Mixed Precision for faster training
        num_workers: int = 4,  # Number of workers for data loading
        pin_memory: bool = True  # Pin memory for faster data transfer to GPU
    ) -> Dict[str, List[float]]:
        """
        Train the model with optimized performance.

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            batch_size: Batch size
            epochs: Number of epochs
            patience: Early stopping patience
            verbose: Whether to print progress
            use_amp: Whether to use Automatic Mixed Precision (faster training on compatible GPUs)
            num_workers: Number of workers for data loading (0 for single-process)
            pin_memory: Whether to pin memory for faster data transfer to GPU

        Returns:
            Dict[str, List[float]]: Training history
        """
        # Determine if we should use AMP based on device compatibility
        use_amp = use_amp and (self.device.type == 'cuda' or self.device.type == 'mps')
        if use_amp:
            logger.info("Using Automatic Mixed Precision for faster training")
            # Use the updated API for PyTorch 2.0+
            try:
                # Modern PyTorch API (2.0+)
                scaler = torch.amp.GradScaler(device_type=self.device.type) if self.device.type == 'cuda' else None
            except (TypeError, ValueError):
                try:
                    # Newer PyTorch API (1.10+)
                    scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
                except (TypeError, ValueError):
                    # Fallback for older PyTorch versions
                    try:
                        # Try the most modern approach first
                        scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
                    except (TypeError, ValueError):
                        # Last resort fallback for very old PyTorch versions
                        logger.warning("Using deprecated GradScaler constructor. Consider upgrading PyTorch.")
                        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None

        # Adjust num_workers based on system
        if self.device.type == 'cpu':
            # Reduce workers on CPU to avoid overhead
            num_workers = min(2, num_workers)
            pin_memory = False

        # Create memory-efficient datasets and data loaders
        from model.dataset import BatchTimeSeriesDataset, create_data_loaders
        train_dataset = BatchTimeSeriesDataset(X_train, y_train)
        val_dataset = BatchTimeSeriesDataset(X_val, y_val)

        # Create data loaders with optimized settings
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory,
            prefetch_factor=2 if num_workers > 0 else None,
            persistent_workers=True if num_workers > 0 else False
        )

        # Keep validation data on device for faster validation
        try:
            # Try direct conversion
            X_val_tensor = torch.tensor(X_val, dtype=torch.float32).to(self.device)
        except TypeError:
            # If that fails, we need to handle potential timestamp objects
            # Convert each row separately to handle mixed types
            X_processed = []
            for i in range(len(X_val)):
                sequence = []
                for j in range(len(X_val[i])):
                    row = []
                    for k in range(len(X_val[i][j])):
                        val = X_val[i][j][k]
                        # Check if it's a timestamp and convert to float
                        if hasattr(val, 'timestamp'):
                            row.append(float(val.timestamp()))
                        else:
                            # Try to convert to float
                            try:
                                row.append(float(val))
                            except (TypeError, ValueError):
                                # If conversion fails, use 0.0
                                row.append(0.0)
                    sequence.append(row)
                X_processed.append(sequence)

            # Convert to tensor
            X_val_tensor = torch.tensor(X_processed, dtype=torch.float32).to(self.device)

        # Convert target to tensor
        y_val_tensor = torch.tensor(y_val, dtype=torch.float32).to(self.device)

        # Initialize early stopping variables
        best_val_loss = float('inf')
        early_stopping_counter = 0

        # Determine validation frequency (less frequent for large datasets)
        val_freq = max(1, min(5, epochs // 20))  # Validate at most every 5 epochs for large datasets

        # Training loop
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0

            # Use tqdm for progress tracking if verbose
            iterator = train_loader
            if verbose and epoch % 10 == 0:
                try:
                    from tqdm import tqdm
                    iterator = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
                except ImportError:
                    pass

            for batch_X, batch_y in iterator:
                # Move data to device
                batch_X = batch_X.to(self.device, non_blocking=True)
                batch_y = batch_y.to(self.device, non_blocking=True)

                # Forward pass with AMP if enabled
                if use_amp and self.device.type == 'cuda':
                    try:
                        # Use the updated API for PyTorch 2.0+
                        with torch.amp.autocast(device_type=self.device.type):
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))
                    except (TypeError, ValueError):
                        # Fallback for older PyTorch versions
                        try:
                            with torch.amp.autocast('cuda'):
                                outputs = self.model(batch_X)
                                loss = self.criterion(outputs, batch_y.unsqueeze(1))
                        except (TypeError, ValueError):
                            # Last resort fallback
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))

                    # Backward pass with gradient scaling
                    self.optimizer.zero_grad()
                    scaler.scale(loss).backward()

                    # Unscale gradients for gradient clipping
                    scaler.unscale_(self.optimizer)

                    # Apply gradient clipping to prevent exploding gradients
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                    scaler.step(self.optimizer)
                    scaler.update()
                else:
                    # Standard forward pass
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y.unsqueeze(1))

                    # Standard backward pass
                    self.optimizer.zero_grad()
                    loss.backward()
                    self.optimizer.step()

                train_loss += loss.item()

            # Calculate average training loss
            train_loss /= len(train_loader)

            # Only validate periodically to save time
            if epoch % val_freq == 0 or epoch == epochs - 1:
                # Validation
                self.model.eval()
                with torch.no_grad():
                    val_outputs = self.model(X_val_tensor)
                    val_loss = self.criterion(val_outputs, y_val_tensor.unsqueeze(1)).item()

                # Update learning rate scheduler
                self.scheduler.step(val_loss)

                # Save history
                self.history['train_loss'].append(train_loss)
                self.history['val_loss'].append(val_loss)
                self.history['learning_rate'].append(self.optimizer.param_groups[0]['lr'])

                # Print progress
                if verbose and (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

                # Check for early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    early_stopping_counter = 0
                    # Save best model
                    self.save_model('model/saved_models/best_model.pth')
                else:
                    early_stopping_counter += 1
                    if early_stopping_counter >= patience:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break
            else:
                # Just log training loss when skipping validation
                if verbose and (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}")

                # Add placeholder values to keep history aligned
                self.history['train_loss'].append(train_loss)
                self.history['val_loss'].append(None)  # Will be interpolated later
                self.history['learning_rate'].append(self.optimizer.param_groups[0]['lr'])

        # Fill in missing validation values with interpolation
        if None in self.history['val_loss']:
            # Get indices of non-None values
            valid_indices = [i for i, x in enumerate(self.history['val_loss']) if x is not None]

            # Interpolate missing values
            for i in range(len(self.history['val_loss'])):
                if self.history['val_loss'][i] is None:
                    # Find nearest valid indices
                    left_idx = max([idx for idx in valid_indices if idx < i], default=valid_indices[0])
                    right_idx = min([idx for idx in valid_indices if idx > i], default=valid_indices[-1])

                    # Linear interpolation
                    left_val = self.history['val_loss'][left_idx]
                    right_val = self.history['val_loss'][right_idx]

                    if left_idx == right_idx:
                        self.history['val_loss'][i] = left_val
                    else:
                        weight = (i - left_idx) / (right_idx - left_idx)
                        self.history['val_loss'][i] = left_val + weight * (right_val - left_val)

        # Load best model
        self.load_model('model/saved_models/best_model.pth')

        return self.history

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions.

        Args:
            X: Input features

        Returns:
            np.ndarray: Predictions
        """
        try:
            # Convert data to PyTorch tensor with special handling for numpy.object_ type
            try:
                # Try direct conversion
                X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)
            except TypeError:
                # If that fails, we need to handle potential timestamp objects
                # Convert each row separately to handle mixed types
                X_processed = []
                for i in range(len(X)):
                    sequence = []
                    for j in range(len(X[i])):
                        row = []
                        for k in range(len(X[i][j])):
                            val = X[i][j][k]
                            # Check if it's a timestamp and convert to float
                            if hasattr(val, 'timestamp'):
                                row.append(float(val.timestamp()))
                            else:
                                # Try to convert to float
                                try:
                                    row.append(float(val))
                                except (TypeError, ValueError):
                                    # If conversion fails, use 0.0
                                    row.append(0.0)
                        sequence.append(row)
                    X_processed.append(sequence)

                # Convert to tensor
                X_tensor = torch.tensor(X_processed, dtype=torch.float32).to(self.device)

            # Set model to evaluation mode
            self.model.eval()

            # Make predictions
            with torch.no_grad():
                predictions = self.model(X_tensor)

            # Convert predictions to numpy array
            predictions = predictions.cpu().numpy()

            return predictions
        except Exception as e:
            logger.error(f"Error making predictions: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Return NaN predictions with the same shape as expected
            return np.full((X.shape[0], 1), np.nan)

    def evaluate(self, X: np.ndarray, y: np.ndarray, feature_names: List[str] = None) -> Dict[str, float]:
        """
        Evaluate the model.

        Args:
            X: Input features
            y: Target values
            feature_names: Optional list of feature names for feature importance

        Returns:
            Dict[str, float]: Evaluation metrics
        """
        # Convert data to PyTorch tensors with special handling for numpy.object_ type
        try:
            # Try direct conversion
            X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)
        except TypeError:
            # If that fails, we need to handle potential timestamp objects
            # Convert each row separately to handle mixed types
            X_processed = []
            for i in range(len(X)):
                sequence = []
                for j in range(len(X[i])):
                    row = []
                    for k in range(len(X[i][j])):
                        val = X[i][j][k]
                        # Check if it's a timestamp and convert to float
                        if hasattr(val, 'timestamp'):
                            row.append(float(val.timestamp()))
                        else:
                            # Try to convert to float
                            try:
                                row.append(float(val))
                            except (TypeError, ValueError):
                                # If conversion fails, use 0.0
                                row.append(0.0)
                    sequence.append(row)
                X_processed.append(sequence)

            # Convert to tensor
            X_tensor = torch.tensor(X_processed, dtype=torch.float32).to(self.device)

        # Convert target to tensor
        y_tensor = torch.tensor(y, dtype=torch.float32).to(self.device)

        # Set model to evaluation mode
        self.model.eval()

        # Make predictions
        with torch.no_grad():
            predictions = self.model(X_tensor)
            loss = self.criterion(predictions, y_tensor.unsqueeze(1)).item()

        # Convert predictions to numpy array
        predictions_np = predictions.cpu().numpy().flatten()

        # Calculate metrics
        mse = np.mean((predictions_np - y) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(predictions_np - y))

        # Calculate R-squared (coefficient of determination)
        # R² = 1 - (sum of squared residuals / total sum of squares)
        ss_res = np.sum((y - predictions_np) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0

        # Calculate directional accuracy
        direction_correct = np.sum((predictions_np > 0) == (y > 0))
        direction_accuracy = direction_correct / len(y)

        # Calculate profit factor (if predictions were used for trading)
        # Profit factor = sum of profits / sum of losses
        profits = np.sum(predictions_np[predictions_np > 0] * y[predictions_np > 0])
        losses = np.sum(predictions_np[predictions_np < 0] * y[predictions_np < 0])
        profit_factor = abs(profits / losses) if losses != 0 else float('inf')

        # Calculate Sharpe ratio (simplified)
        returns = predictions_np * y  # Simulated returns
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) != 0 else 0

        # Save test loss and predictions
        self.history['test_loss'] = loss

        # Store test predictions and actual values for visualization
        self.test_predictions = predictions_np
        self.test_actual = y

        # Calculate feature importance (using a simple permutation method)
        feature_importance = self._calculate_feature_importance(X, y)
        self.feature_importance = feature_importance

        # Store feature names if provided
        if feature_names is not None:
            self.feature_names = feature_names
        else:
            self.feature_names = [f"Feature {i+1}" for i in range(len(feature_importance))]

        # Return comprehensive metrics
        return {
            'loss': loss,
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'direction_accuracy': direction_accuracy,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio
        }

    def _calculate_feature_importance(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """
        Calculate feature importance using permutation importance.

        Args:
            X: Input features
            y: Target values

        Returns:
            np.ndarray: Feature importance scores
        """
        # Skip feature importance calculation for large datasets or when there are issues
        try:
            # Get the number of features from the shape of X
            if isinstance(X, np.ndarray) and len(X.shape) == 3:
                num_features = X.shape[2]
            else:
                # If X is not a proper 3D array, we can't calculate feature importance
                logger.warning("Skipping feature importance calculation due to incompatible data shape")
                return np.ones(10)  # Return dummy importance

            # Get baseline performance - reuse the same conversion logic from evaluate
            try:
                # Try direct conversion
                X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)
            except TypeError:
                # If that fails, we need to handle potential timestamp objects
                # Convert each row separately to handle mixed types
                X_processed = []
                for i in range(len(X)):
                    sequence = []
                    for j in range(len(X[i])):
                        row = []
                        for k in range(len(X[i][j])):
                            val = X[i][j][k]
                            # Check if it's a timestamp and convert to float
                            if hasattr(val, 'timestamp'):
                                row.append(float(val.timestamp()))
                            else:
                                # Try to convert to float
                                try:
                                    row.append(float(val))
                                except (TypeError, ValueError):
                                    # If conversion fails, use 0.0
                                    row.append(0.0)
                        sequence.append(row)
                    X_processed.append(sequence)

                # Convert to tensor
                X_tensor = torch.tensor(X_processed, dtype=torch.float32).to(self.device)

            # Convert target to tensor
            y_tensor = torch.tensor(y, dtype=torch.float32).to(self.device)

            self.model.eval()
            with torch.no_grad():
                baseline_pred = self.model(X_tensor)
                baseline_loss = self.criterion(baseline_pred, y_tensor.unsqueeze(1)).item()

            # Calculate importance for each feature
            importance = np.zeros(num_features)

            # Only calculate for a subset of features if there are many
            max_features_to_analyze = min(num_features, 20)  # Limit to 20 features for performance
            feature_indices = np.random.choice(num_features, max_features_to_analyze, replace=False)

            for idx, i in enumerate(feature_indices):
                # Create a copy of the data
                X_permuted = X.copy()

                # Permute the feature
                for j in range(X.shape[0]):
                    np.random.shuffle(X_permuted[j, :, i])

                # Calculate performance with permuted feature
                try:
                    # Try direct conversion
                    X_permuted_tensor = torch.tensor(X_permuted, dtype=torch.float32).to(self.device)
                except TypeError:
                    # If that fails, we need to handle potential timestamp objects
                    # Convert each row separately to handle mixed types
                    X_processed = []
                    for p in range(len(X_permuted)):
                        sequence = []
                        for j in range(len(X_permuted[p])):
                            row = []
                            for k in range(len(X_permuted[p][j])):
                                val = X_permuted[p][j][k]
                                # Check if it's a timestamp and convert to float
                                if hasattr(val, 'timestamp'):
                                    row.append(float(val.timestamp()))
                                else:
                                    # Try to convert to float
                                    try:
                                        row.append(float(val))
                                    except (TypeError, ValueError):
                                        # If conversion fails, use 0.0
                                        row.append(0.0)
                            sequence.append(row)
                        X_processed.append(sequence)

                    # Convert to tensor
                    X_permuted_tensor = torch.tensor(X_processed, dtype=torch.float32).to(self.device)

                with torch.no_grad():
                    permuted_pred = self.model(X_permuted_tensor)
                    permuted_loss = self.criterion(permuted_pred, y_tensor.unsqueeze(1)).item()

                # Importance is the increase in loss when the feature is permuted
                importance[i] = permuted_loss - baseline_loss

            # Normalize importance
            if np.sum(importance) != 0:
                importance = importance / np.sum(importance)

            return importance

        except Exception as e:
            logger.error(f"Error calculating feature importance: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Return dummy importance
            return np.ones(10)

    def save_model(self, filepath: str, symbol: str = None, timeframe: str = None, model_type: str = None, terminal_id: int = None):
        """
        Save the model using the PyTorch 2.6+ security model.
        If symbol and timeframe are provided, uses standardized path from path_utils.

        Args:
            filepath: Path to save the model
            symbol: Trading symbol (optional)
            timeframe: Timeframe (optional)
            model_type: Model type (optional)
            terminal_id: Terminal ID (optional)
        """
        # If symbol and timeframe are provided, use standardized path
        if symbol and timeframe:
            from utils.path_utils import get_model_path
            # Use provided model_type or default to 'lstm'
            model_type_to_use = model_type or 'lstm'
            # Get standardized path
            filepath = get_model_path(symbol, timeframe, model_type_to_use, terminal_id)
            logger.info(f"Using standardized path: {filepath}")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

        # Prepare performance metrics
        performance_metrics = {}
        if hasattr(self, 'test_predictions') and hasattr(self, 'test_actual'):
            # Calculate metrics if we have test data
            predictions = self.test_predictions
            actual = self.test_actual

            # Basic metrics
            mse = np.mean((predictions - actual) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(predictions - actual))

            # Calculate R-squared (coefficient of determination)
            ss_res = np.sum((actual - predictions) ** 2)
            ss_tot = np.sum((actual - np.mean(actual)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0

            # Direction accuracy
            direction_correct = np.sum((predictions > 0) == (actual > 0))
            direction_accuracy = direction_correct / len(actual)

            # Trading metrics
            profits = np.sum(predictions[predictions > 0] * actual[predictions > 0])
            losses = np.sum(predictions[predictions < 0] * actual[predictions < 0])
            profit_factor = abs(profits / losses) if losses != 0 else float('inf')

            # Sharpe ratio (simplified)
            returns = predictions * actual  # Simulated returns
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) != 0 else 0

            # Store metrics
            performance_metrics = {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2),
                'direction_accuracy': float(direction_accuracy),
                'profit_factor': float(profit_factor) if not np.isinf(profit_factor) else 999.99,
                'sharpe_ratio': float(sharpe_ratio)
            }

        # Save model with additional information
        checkpoint = {
            # We only save the state dict for better security and compatibility
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'hyperparams': self.hyperparams,
            'history': self.history,
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'output_size': self.output_size,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # Add new information for visualizations
            'performance_metrics': performance_metrics
        }

        # Add test predictions and actual values if available
        if hasattr(self, 'test_predictions') and hasattr(self, 'test_actual'):
            checkpoint['test_predictions'] = self.test_predictions
            checkpoint['test_actual'] = self.test_actual

        # Add feature importance if available
        if hasattr(self, 'feature_importance'):
            checkpoint['feature_importance'] = self.feature_importance

        # Add feature names if available
        if hasattr(self, 'feature_names'):
            checkpoint['feature_names'] = self.feature_names

        # Save checkpoint using the security utility
        if safe_save(checkpoint, filepath):
            logger.info(f"Model saved to {filepath}")
        else:
            logger.error(f"Failed to save model to {filepath}")

    def load_model(self, filepath: str, symbol: str = None, timeframe: str = None, model_type: str = None, terminal_id: int = None) -> bool:
        """
        Load the model using the PyTorch 2.6+ security model.
        If symbol and timeframe are provided, uses standardized path from path_utils.

        Args:
            filepath: Path to load the model from
            symbol: Trading symbol (optional)
            timeframe: Timeframe (optional)
            model_type: Model type (optional)
            terminal_id: Terminal ID (optional)

        Returns:
            bool: True if model was loaded successfully, False otherwise
        """
        # If symbol and timeframe are provided, use standardized path
        if symbol and timeframe:
            from utils.path_utils import get_model_path
            # Use provided model_type or default to 'lstm'
            model_type_to_use = model_type or 'lstm'
            # Get standardized path
            filepath = get_model_path(symbol, timeframe, model_type_to_use, terminal_id)
            logger.info(f"Using standardized path for loading: {filepath}")

        # Check if file exists
        if not os.path.exists(filepath):
            logger.error(f"Model file {filepath} not found")
            return False

        try:
            # Load checkpoint using the security utility
            try:
                # Register TradingLSTM class as a safe global
                register_safe_classes()

                # Load checkpoint using the security utility
                checkpoint = safe_load(filepath, self.device)

                logger.info(f"Successfully loaded model checkpoint from {filepath}")
            except Exception as e:
                logger.error(f"Failed to load model: {str(e)}")
                logger.warning("Attempting fallback loading method...")

                try:
                    # Fallback to direct loading with weights_only=False
                    checkpoint = torch.load(filepath, map_location=self.device, weights_only=False)
                    logger.info(f"Successfully loaded model using fallback method")
                except Exception as e2:
                    logger.error(f"Fallback loading also failed: {str(e2)}")
                    return False

            # Load model parameters
            if 'input_size' in checkpoint:
                self.input_size = checkpoint['input_size']
            elif 'hyperparams' in checkpoint and 'input_size' in checkpoint['hyperparams']:
                self.input_size = checkpoint['hyperparams']['input_size']

            if 'hidden_size' in checkpoint:
                self.hidden_size = checkpoint['hidden_size']
            elif 'hyperparams' in checkpoint and 'hidden_size' in checkpoint['hyperparams']:
                self.hidden_size = checkpoint['hyperparams']['hidden_size']

            if 'num_layers' in checkpoint:
                self.num_layers = checkpoint['num_layers']
            elif 'hyperparams' in checkpoint and 'num_layers' in checkpoint['hyperparams']:
                self.num_layers = checkpoint['hyperparams']['num_layers']

            if 'output_size' in checkpoint:
                self.output_size = checkpoint['output_size']
            elif 'hyperparams' in checkpoint and 'output_size' in checkpoint['hyperparams']:
                self.output_size = checkpoint['hyperparams']['output_size']

            # Recreate model with the loaded parameters
            self.model = TradingLSTM(
                input_size=self.input_size,
                hidden_size=self.hidden_size,
                num_layers=self.num_layers,
                output_size=self.output_size
            ).to(self.device)

            # Load model state
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"Successfully loaded model state dict")
            else:
                logger.warning(f"No model state dict found in checkpoint, using initialized weights")

            # Recreate optimizer
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=0.001,
                weight_decay=1e-5
            )

            # Load optimizer state if available
            if 'optimizer_state_dict' in checkpoint:
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                logger.info(f"Successfully loaded optimizer state dict")

            # Load history if available
            self.history = checkpoint.get('history', {'train_loss': [], 'val_loss': [], 'learning_rate': []})

            # Load hyperparams if available
            if 'hyperparams' in checkpoint:
                self.hyperparams = checkpoint['hyperparams']

            logger.info(f"Model loaded from {filepath} with input_size={self.input_size}, hidden_size={self.hidden_size}, num_layers={self.num_layers}")
            return True
        except Exception as e:
            logger.error(f"Error loading model from {filepath}: {str(e)}")
            return False

    def update_model(
        self,
        X: np.ndarray,
        y: np.ndarray,
        batch_size: int = 32,
        epochs: int = 10,
        use_amp: bool = True,
        num_workers: int = 2,
        pin_memory: bool = True
    ):
        """
        Update the model with new data (online learning) with optimized performance.

        Args:
            X: Input features
            y: Target values
            batch_size: Batch size
            epochs: Number of epochs
            use_amp: Whether to use Automatic Mixed Precision (faster training on compatible GPUs)
            num_workers: Number of workers for data loading (0 for single-process)
            pin_memory: Whether to pin memory for faster data transfer to GPU
        """
        # Determine if we should use AMP based on device compatibility
        use_amp = use_amp and (self.device.type == 'cuda' or self.device.type == 'mps')
        if use_amp:
            # Use the updated API for PyTorch 2.0+
            try:
                # Modern PyTorch API (2.0+)
                scaler = torch.amp.GradScaler(device_type=self.device.type) if self.device.type == 'cuda' else None
            except (TypeError, ValueError):
                try:
                    # Newer PyTorch API (1.10+)
                    scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
                except (TypeError, ValueError):
                    # Fallback for older PyTorch versions
                    try:
                        # Try the most modern approach first
                        scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
                    except (TypeError, ValueError):
                        # Last resort fallback for very old PyTorch versions
                        logger.warning("Using deprecated GradScaler constructor. Consider upgrading PyTorch.")
                        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None

        # Adjust num_workers based on system
        if self.device.type == 'cpu':
            # Reduce workers on CPU to avoid overhead
            num_workers = min(1, num_workers)
            pin_memory = False

        # For small updates, don't use multiple workers
        if len(X) < 1000:
            num_workers = 0
            pin_memory = False

        # Create memory-efficient dataset
        from model.dataset import BatchTimeSeriesDataset
        dataset = BatchTimeSeriesDataset(X, y)

        # Create data loader with optimized settings
        loader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory,
            prefetch_factor=2 if num_workers > 0 else None,
            persistent_workers=True if num_workers > 0 and len(X) > 5000 else False
        )

        # Training loop
        self.model.train()
        for epoch in range(epochs):
            train_loss = 0.0

            for batch_X, batch_y in loader:
                # Move data to device
                batch_X = batch_X.to(self.device, non_blocking=True)
                batch_y = batch_y.to(self.device, non_blocking=True)

                # Forward pass with AMP if enabled
                if use_amp and self.device.type == 'cuda':
                    try:
                        # Use the updated API for PyTorch 2.0+
                        with torch.amp.autocast(device_type=self.device.type):
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))
                    except (TypeError, ValueError):
                        # Fallback for older PyTorch versions
                        try:
                            with torch.amp.autocast('cuda'):
                                outputs = self.model(batch_X)
                                loss = self.criterion(outputs, batch_y.unsqueeze(1))
                        except (TypeError, ValueError):
                            # Last resort fallback
                            outputs = self.model(batch_X)
                            loss = self.criterion(outputs, batch_y.unsqueeze(1))

                    # Backward pass with gradient scaling
                    self.optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.step(self.optimizer)
                    scaler.update()
                else:
                    # Standard forward pass
                    outputs = self.model(batch_X)
                    loss = self.criterion(outputs, batch_y.unsqueeze(1))

                    # Standard backward pass
                    self.optimizer.zero_grad()
                    loss.backward()
                    self.optimizer.step()

                train_loss += loss.item()

            # Calculate average training loss
            train_loss /= len(loader)

            # Save history
            self.history['train_loss'].append(train_loss)

        logger.info(f"Model updated with {len(X)} samples, Final Loss: {train_loss:.6f}")

class ModelManager:
    """
    Manager for multiple model types (LSTM, TFT, ARIMA).
    """
    def __init__(self):
        """Initialize ModelManager."""
        self.models = {}  # Format: {model_name: model, model_name_type: model_type}
        self.model_config = TRADING_CONFIG['model']
        self.model_dir = 'model/saved_models'

        # Store current symbol and timeframe for standardized paths
        self.current_symbol = None
        self.current_timeframe = None

        # Create model directory if it doesn't exist
        os.makedirs(self.model_dir, exist_ok=True)

        # Create subdirectories for different model types
        os.makedirs(os.path.join(self.model_dir, 'lstm'), exist_ok=True)
        os.makedirs(os.path.join(self.model_dir, 'tft'), exist_ok=True)
        os.makedirs(os.path.join(self.model_dir, 'arima'), exist_ok=True)
        os.makedirs(os.path.join(self.model_dir, 'ensemble'), exist_ok=True)

    def create_model(
        self,
        model_name: str,
        model_type: str = "lstm",
        input_size: int = None,
        hidden_size: int = None,
        num_layers: int = None,
        output_size: int = 1,
        ensemble_config: Dict[str, Any] = None
    ) -> Any:
        """
        Create a new model.

        Args:
            model_name: Name of the model
            model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
            input_size: Number of input features
            hidden_size: Number of hidden units
            num_layers: Number of layers
            output_size: Number of output units
            ensemble_config: Configuration for ensemble models

        Returns:
            Any: Trainer for the model
        """
        # Use default values from config if not provided
        hidden_size = hidden_size or self.model_config['hidden_size']
        num_layers = num_layers or self.model_config['num_layers']

        # Create appropriate model based on type
        if model_type.lower() == 'lstm':
            if input_size is None:
                raise ValueError("input_size is required for LSTM models")

            trainer = ModelTrainer(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                learning_rate=self.model_config['learning_rate']
            )
        elif model_type.lower() == 'tft':
            if input_size is None:
                raise ValueError("input_size is required for TFT models")

            # Import here to avoid circular imports
            from model.tft_model_pytorch import TFTModel
            trainer = TFTModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_attention_heads=4,
                dropout_rate=0.1,
                learning_rate=self.model_config['learning_rate']
            )
        elif model_type.lower() == 'arima':
            trainer = ARIMAModelTrainer(
                order=None,  # Auto-determine
                auto_determine=True
            )
        elif model_type.lower() in ['lstm_arima', 'tft_arima']:
            # For ensemble models, we need to create the component models
            from model.ensemble_model import EnsembleModel

            # Determine component models based on ensemble type
            if model_type.lower() == 'lstm_arima':
                component_models = ['lstm', 'arima']
                weights = [0.6, 0.4]  # Default weights
            else:  # tft_arima
                component_models = ['tft', 'arima']
                weights = [0.7, 0.3]  # Default weights

            # Override with ensemble_config if provided
            if ensemble_config:
                if 'models' in ensemble_config:
                    component_models = ensemble_config['models']
                if 'weights' in ensemble_config:
                    weights = ensemble_config['weights']

            # Create component models
            component_model_names = []
            for component_type in component_models:
                component_name = f"{model_name}_{component_type}"
                component_model_names.append(component_name)

                # Create component model
                self.create_model(
                    model_name=component_name,
                    model_type=component_type,
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    output_size=output_size
                )

            # Create ensemble model
            trainer = EnsembleModel(
                model_manager=self,
                ensemble_name=model_name,
                model_names=component_model_names,
                weights=weights
            )
        else:
            raise ValueError(f"Unsupported model type: {model_type}")

        # Save model
        self.models[model_name] = trainer

        # Save model type
        self.models[f"{model_name}_type"] = model_type.lower()

        logger.info(f"Created {model_type.upper()} model: {model_name}")
        return trainer

    def get_model(self, model_name: str) -> Optional[ModelTrainer]:
        """
        Get a model by name.

        Args:
            model_name: Name of the model

        Returns:
            ModelTrainer: Trainer for the model or None if not found
        """
        if model_name not in self.models:
            logger.warning(f"Model {model_name} not found")
            return None

        return self.models[model_name]

    def train_model(
        self,
        model_name: str,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: np.ndarray = None,
        y_val: np.ndarray = None,
        feature_names: List[str] = None,
        symbol: str = None,
        timeframe: str = None
    ) -> Dict[str, List[float]]:
        """
        Train a model.

        Args:
            model_name: Name of the model
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional for ARIMA)
            y_val: Validation targets (optional for ARIMA)
            feature_names: Feature names (required for TFT)
            symbol: Trading symbol (optional, for standardized paths)
            timeframe: Timeframe (optional, for standardized paths)

        Returns:
            Dict[str, List[float]]: Training history
        """
        # Set current symbol and timeframe if provided
        if symbol and timeframe:
            self.set_current_symbol_timeframe(symbol, timeframe)
        try:
            # Get model
            trainer = self.get_model(model_name)
            if trainer is None:
                logger.error(f"Model {model_name} not found")
                return None

            # Get model type
            model_type = self.models.get(f"{model_name}_type", "lstm")

            # Train model based on type
            if model_type == 'lstm':
                # Validate inputs for LSTM
                if X_train is None or y_train is None or X_val is None or y_val is None:
                    logger.error(f"Invalid input data for training LSTM model {model_name}")
                    return None

                if len(X_train) == 0 or len(y_train) == 0 or len(X_val) == 0 or len(y_val) == 0:
                    logger.error(f"Empty input data for training LSTM model {model_name}")
                    return None

                # Train LSTM model
                history = trainer.train(
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    batch_size=self.model_config['batch_size'],
                    epochs=self.model_config['epochs'],
                    patience=self.model_config['early_stopping_patience']
                )

            elif model_type == 'tft':
                # Validate inputs for TFT
                if X_train is None or y_train is None or X_val is None or y_val is None:
                    logger.error(f"Invalid input data for training TFT model {model_name}")
                    return None

                if len(X_train) == 0 or len(y_train) == 0 or len(X_val) == 0 or len(y_val) == 0:
                    logger.error(f"Empty input data for training TFT model {model_name}")
                    return None

                if feature_names is None:
                    logger.error(f"Feature names are required for training TFT model {model_name}")
                    return None

                # Train TFT model
                history = trainer.train(
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    feature_names=feature_names,
                    sequence_length=self.model_config['sequence_length'],
                    batch_size=self.model_config['batch_size'],
                    epochs=self.model_config['epochs'],
                    patience=self.model_config['early_stopping_patience']
                )

            elif model_type == 'arima':
                # Validate inputs for ARIMA
                if y_train is None or len(y_train) == 0:
                    logger.error(f"Invalid input data for training ARIMA model {model_name}")
                    return None

                # For ARIMA, X_train is optional (exogenous variables)
                exog_train = None
                if X_train is not None and X_train.size > 0:
                    # Reshape X_train for ARIMA if needed
                    if len(X_train.shape) == 3:
                        # Convert 3D to 2D by taking the last timestep
                        exog_train = X_train[:, -1, :]
                    else:
                        exog_train = X_train

                # Train ARIMA model
                history = trainer.train(
                    y_train=y_train,
                    exog_train=exog_train
                )

            else:
                logger.error(f"Unsupported model type: {model_type}")
                return None

            # Save model with standardized path if symbol and timeframe are available
            if hasattr(self, 'current_symbol') and hasattr(self, 'current_timeframe'):
                self.save_model(model_name, symbol=self.current_symbol, timeframe=self.current_timeframe)
            else:
                self.save_model(model_name)

            return history

        except Exception as e:
            logger.error(f"Error training model {model_name}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def update_model(
        self,
        model_name: str,
        X: np.ndarray,
        y: np.ndarray,
        epochs: int = 10,
        use_amp: bool = True,
        symbol: str = None,
        timeframe: str = None
    ):
        """
        Update a model with new data using optimized performance settings.

        Args:
            model_name: Name of the model
            X: Input features
            y: Target values
            epochs: Number of epochs for the update (default: 10)
            use_amp: Whether to use Automatic Mixed Precision for faster training
            symbol: Trading symbol (optional, for standardized paths)
            timeframe: Timeframe (optional, for standardized paths)
        """
        # Set current symbol and timeframe if provided
        if symbol and timeframe:
            self.set_current_symbol_timeframe(symbol, timeframe)
        # Get model
        trainer = self.get_model(model_name)
        if trainer is None:
            logger.error(f"Model {model_name} not found")
            return

        # Get model type
        model_type = self.models.get(f"{model_name}_type", "lstm")

        # Determine batch size based on data size
        batch_size = self.model_config['batch_size']
        if len(X) < 1000:
            # Use smaller batch size for small datasets
            batch_size = min(batch_size, max(16, len(X) // 10))

        # Determine number of workers based on data size
        num_workers = 4 if len(X) > 10000 else (2 if len(X) > 1000 else 0)

        # Update model based on type
        if model_type in ['lstm', 'tft']:
            # Update neural network models with optimized settings
            trainer.update_model(
                X=X,
                y=y,
                batch_size=batch_size,
                epochs=epochs,
                use_amp=use_amp,
                num_workers=num_workers,
                pin_memory=num_workers > 0
            )
        elif model_type == 'arima':
            # For ARIMA, X is optional (exogenous variables)
            exog = None
            if X is not None and X.size > 0:
                # Reshape X for ARIMA if needed
                if len(X.shape) == 3:
                    # Convert 3D to 2D by taking the last timestep
                    exog = X[:, -1, :]
                else:
                    exog = X

            # Update ARIMA model
            trainer.update_model(
                y=y,
                exog=exog
            )
        elif model_type in ['lstm_arima', 'tft_arima', 'ensemble']:
            # For ensemble models, update each component
            trainer.update_model(
                X=X,
                y=y,
                batch_size=batch_size,
                epochs=epochs,
                use_amp=use_amp
            )
        else:
            logger.error(f"Unsupported model type for update: {model_type}")
            return

        # Save model with standardized path if symbol and timeframe are available
        if hasattr(self, 'current_symbol') and hasattr(self, 'current_timeframe'):
            self.save_model(model_name, symbol=self.current_symbol, timeframe=self.current_timeframe)
        else:
            self.save_model(model_name)

    def predict(
        self,
        model_name: str,
        X: np.ndarray,
        feature_names: List[str] = None,
        steps: int = 1,
        symbol: str = None,
        timeframe: str = None
    ) -> Optional[np.ndarray]:
        """
        Make predictions with a model.

        Args:
            model_name: Name of the model
            X: Input features
            feature_names: Feature names (required for TFT)
            steps: Number of steps to forecast (for ARIMA)
            symbol: Trading symbol (optional, for standardized paths)
            timeframe: Timeframe (optional, for standardized paths)

        Returns:
            np.ndarray: Predictions or None if model not found
        """
        # Set current symbol and timeframe if provided
        if symbol and timeframe:
            self.set_current_symbol_timeframe(symbol, timeframe)
        try:
            # Get model
            trainer = self.get_model(model_name)
            if trainer is None:
                logger.error(f"Model {model_name} not found")
                return None

            # Get model type
            model_type = self.models.get(f"{model_name}_type", "lstm")

            # Make predictions based on model type
            if model_type == 'lstm':
                predictions = trainer.predict(X)

            elif model_type == 'tft':
                if feature_names is None:
                    logger.error(f"Feature names are required for TFT model {model_name}")
                    return None

                predictions = trainer.predict(X, feature_names)

            elif model_type == 'arima':
                # For ARIMA, X is optional (exogenous variables)
                exog = None
                if X is not None and X.size > 0:
                    # Reshape X for ARIMA if needed
                    if len(X.shape) == 3:
                        # Convert 3D to 2D by taking the last timestep
                        exog = X[:, -1, :]
                    else:
                        exog = X

                predictions = trainer.predict(steps=steps, exog=exog)

            elif model_type in ['lstm_arima', 'tft_arima', 'ensemble']:
                # For ensemble models, we need to handle different input formats
                # Check if X is a dictionary with component inputs
                if isinstance(X, dict):
                    # X contains inputs for different component models
                    predictions = trainer.predict(
                        X=X,
                        feature_names=feature_names,
                        steps=steps,
                        method='weighted'  # Use weighted ensemble by default
                    )
                else:
                    # Use the same input for all component models
                    predictions = trainer.predict(
                        X=X,
                        feature_names=feature_names,
                        steps=steps,
                        method='weighted'  # Use weighted ensemble by default
                    )

            else:
                logger.error(f"Unsupported model type: {model_type}")
                return None

            return predictions

        except Exception as e:
            logger.error(f"Error making predictions with model {model_name}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def evaluate_model(
        self,
        model_name: str,
        X: np.ndarray,
        y: np.ndarray,
        symbol: str = None,
        timeframe: str = None
    ) -> Optional[Dict[str, float]]:
        """
        Evaluate a model.

        Args:
            model_name: Name of the model
            X: Input features
            y: Target values
            symbol: Trading symbol (optional, for standardized paths)
            timeframe: Timeframe (optional, for standardized paths)

        Returns:
            Dict[str, float]: Evaluation metrics or None if model not found
        """
        # Set current symbol and timeframe if provided
        if symbol and timeframe:
            self.set_current_symbol_timeframe(symbol, timeframe)
        # Get model
        trainer = self.get_model(model_name)
        if trainer is None:
            logger.error(f"Model {model_name} not found")
            return None

        # Evaluate model
        metrics = trainer.evaluate(X, y)

        return metrics

    def save_model(self, model_name: str, symbol: str = None, timeframe: str = None):
        """
        Save a model to disk using standardized paths.

        Args:
            model_name: Name of the model
            symbol: Trading symbol (optional)
            timeframe: Timeframe (optional)
        """
        # Get model
        trainer = self.get_model(model_name)
        if trainer is None:
            logger.error(f"Model {model_name} not found")
            return

        # Get model type
        model_type = self.models.get(f"{model_name}_type", "lstm")

        # Extract terminal_id from model_name if possible
        terminal_id = None
        if "_terminal_" in model_name:
            try:
                terminal_id = int(model_name.split("_terminal_")[1].split("_")[0])
            except (IndexError, ValueError):
                pass

        # If symbol and timeframe are provided, use standardized path
        if symbol and timeframe:
            # Save model using standardized path
            trainer.save_model(
                filepath="",  # Will be ignored when symbol and timeframe are provided
                symbol=symbol,
                timeframe=timeframe,
                model_type=model_type,
                terminal_id=terminal_id
            )
        else:
            # Use default path
            filepath = os.path.join(self.model_dir, f"{model_name}.pth")
            trainer.save_model(filepath)

    def load_model(self, model_name: str, symbol: str = None, timeframe: str = None) -> bool:
        """
        Load a model from disk using standardized paths if symbol and timeframe are provided.

        Args:
            model_name: Name of the model
            symbol: Trading symbol (optional)
            timeframe: Timeframe (optional)

        Returns:
            bool: True if model was loaded successfully, False otherwise
        """
        # Determine filepath based on inputs
        if symbol and timeframe:
            # Try to determine model type from model_name
            model_type = "lstm"  # Default
            if "_tft_" in model_name:
                model_type = "tft"
            elif "_arima_" in model_name:
                model_type = "arima"
            elif "_lstm_arima_" in model_name:
                model_type = "lstm_arima"
            elif "_tft_arima_" in model_name:
                model_type = "tft_arima"
            elif "_ensemble_" in model_name:
                model_type = "ensemble"

            # Extract terminal_id from model_name if possible
            terminal_id = None
            if "_terminal_" in model_name:
                try:
                    terminal_id = int(model_name.split("_terminal_")[1].split("_")[0])
                except (IndexError, ValueError):
                    pass

            # Use standardized path
            from utils.path_utils import get_model_path
            filepath = get_model_path(symbol, timeframe, model_type, terminal_id)
            logger.info(f"Using standardized path: {filepath}")
        else:
            # Use default path
            filepath = os.path.join(self.model_dir, f"{model_name}.pth")

        # Check if model file exists
        if not os.path.exists(filepath):
            logger.error(f"Model file {filepath} not found")
            return False

        # Try to determine model type from filename
        model_type = "lstm"  # Default
        if "_tft_" in model_name:
            model_type = "tft"
        elif "_arima_" in model_name:
            model_type = "arima"
        elif "_lstm_arima_" in model_name:
            model_type = "lstm_arima"
        elif "_tft_arima_" in model_name:
            model_type = "tft_arima"
        elif "_ensemble_" in model_name:
            model_type = "ensemble"

        # Create appropriate trainer based on model type
        if model_type == "lstm":
            trainer = ModelTrainer(input_size=32)  # Use 32 as the default input_size for features
            success = trainer.load_model(filepath)
        elif model_type == "tft":
            from model.tft_model_pytorch import TFTModel
            trainer = TFTModel(input_size=32)
            success = trainer.load_model(filepath)
        elif model_type == "arima":
            trainer = ARIMAModelTrainer()
            success = trainer.load_model(filepath)
        elif model_type in ["ensemble", "lstm_arima", "tft_arima"]:
            # For ensemble models, we need to load the component models first
            try:
                # Import the ensemble model class
                from model.ensemble_model import EnsembleModel

                # Load the ensemble model state
                with open(filepath, 'rb') as f:
                    import pickle
                    model_state = pickle.load(f)

                # Get component model names and weights
                component_model_names = model_state.get('model_names', [])
                weights = model_state.get('weights', [])

                # Check if component models exist
                for component_name in component_model_names:
                    if not os.path.exists(os.path.join(self.model_dir, f"{component_name}.pth")):
                        logger.error(f"Component model {component_name} not found")
                        return False

                # Load component models
                for component_name in component_model_names:
                    if not self.load_model(component_name):
                        logger.error(f"Failed to load component model {component_name}")
                        return False

                # Create ensemble model
                trainer = EnsembleModel(
                    model_manager=self,
                    ensemble_name=model_name,
                    model_names=component_model_names,
                    weights=weights
                )

                # Load ensemble model state
                success = True

                logger.info(f"Loaded ensemble model {model_name} with components: {component_model_names}")
            except Exception as e:
                logger.error(f"Error loading ensemble model: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                return False
        else:
            logger.error(f"Unknown model type for {model_name}")
            return False

        if not success:
            return False

        # Save model and its type
        self.models[model_name] = trainer
        self.models[f"{model_name}_type"] = model_type

        return True

    def set_current_symbol_timeframe(self, symbol: str, timeframe: str):
        """
        Set the current symbol and timeframe for standardized paths.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe
        """
        self.current_symbol = symbol
        self.current_timeframe = timeframe
        logger.info(f"Set current symbol to {symbol} and timeframe to {timeframe}")

    def list_models(self) -> List[str]:
        """
        List all available models.

        Returns:
            List[str]: List of model names
        """
        return list(self.models.keys())

    def delete_model(self, model_name: str) -> bool:
        """
        Delete a model.

        Args:
            model_name: Name of the model

        Returns:
            bool: True if model was deleted successfully, False otherwise
        """
        # Check if model exists
        if model_name not in self.models:
            logger.error(f"Model {model_name} not found")
            return False

        # Delete model
        del self.models[model_name]

        # Delete model type if exists
        if f"{model_name}_type" in self.models:
            del self.models[f"{model_name}_type"]

        # Delete model file
        filepath = os.path.join(self.model_dir, f"{model_name}.pth")
        if os.path.exists(filepath):
            os.remove(filepath)

        logger.info(f"Model {model_name} deleted")
        return True
