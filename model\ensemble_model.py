"""
Ensemble Model Module.
This module implements ensemble methods for combining predictions from multiple models.
"""
import logging
import os
import json
import pickle
import traceback
from datetime import datetime
import numpy as np
from typing import Dict, List, Optional

# Configure logger
logger = logging.getLogger('model.ensemble')

class EnsembleModel:
    """
    Ensemble Model class for combining predictions from multiple models.
    """
    def __init__(
        self,
        model_paths: List[str] = None,
        model_types: List[str] = None,
        weights: Optional[List[float]] = None,
        models: List = None
    ):
        """
        Initialize EnsembleModel.

        Args:
            model_paths: List of paths to model files
            model_types: List of model types corresponding to each path
            weights: Optional weights for each model (default: equal weights)
            models: Optional list of pre-loaded model objects
        """
        # Handle direct model objects
        if models is not None:
            self.models = models

            # If model_types is not provided, try to infer from models
            if model_types is None:
                self.model_types = []
                for model in models:
                    if hasattr(model, 'model_type'):
                        self.model_types.append(model.model_type)
                    else:
                        # Try to infer type from class name
                        model_class = model.__class__.__name__
                        if 'LSTM' in model_class:
                            self.model_types.append('lstm')
                        elif 'TFT' in model_class:
                            self.model_types.append('tft')
                        elif 'ARIMA' in model_class:
                            self.model_types.append('arima')
                        else:
                            self.model_types.append('unknown')
            else:
                self.model_types = model_types

            # Set dummy model paths and model names
            self.model_paths = [f"memory_model_{i}" for i in range(len(models))]
            self.model_names = [f"model_{i}" for i in range(len(models))]

            # Set weights
            if weights is None:
                # Equal weights
                self.weights = [1.0 / len(models)] * len(models)
            else:
                # Normalize weights
                if len(weights) != len(models):
                    raise ValueError(f"Number of weights ({len(weights)}) must match number of models ({len(models)})")

                total = sum(weights)
                self.weights = [w / total for w in weights]

            logger.info(f"Created ensemble model with {len(models)} pre-loaded models")

        # Handle model paths
        elif model_paths is not None and model_types is not None:
            self.model_paths = model_paths
            self.model_types = model_types

            # Generate model names from paths
            self.model_names = []
            for path in model_paths:
                # Extract filename without extension as model name
                import os
                filename = os.path.basename(path)
                name_without_ext = os.path.splitext(filename)[0]
                self.model_names.append(name_without_ext)

            # Validate inputs
            if len(model_paths) != len(model_types):
                raise ValueError(f"Number of model paths ({len(model_paths)}) must match number of model types ({len(model_types)})")

            # Set weights
            if weights is None:
                # Equal weights
                self.weights = [1.0 / len(model_paths)] * len(model_paths)
            else:
                # Normalize weights
                if len(weights) != len(model_paths):
                    raise ValueError(f"Number of weights ({len(weights)}) must match number of models ({len(model_paths)})")

                total = sum(weights)
                self.weights = [w / total for w in weights]

            # Initialize models
            self.models = []
            self._load_models()

            logger.info(f"Created ensemble model with {len(model_paths)} models")
        else:
            # Initialize empty ensemble (for loading from file)
            self.models = []
            self.model_paths = []
            self.model_types = []
            self.model_names = []
            self.weights = []
            logger.info("Created empty ensemble model (will be populated during loading)")

    def _load_models(self):
        """Load all models in the ensemble."""
        from model.model_loader import ModelLoader

        # Initialize model loader
        model_loader = ModelLoader()

        # Load each model
        for i, (model_path, model_type) in enumerate(zip(self.model_paths, self.model_types)):
            try:
                # Load model
                model = model_loader.load_model(model_path)

                if model is None:
                    logger.error(f"Failed to load model from {model_path}")
                    # Use a placeholder model
                    self.models.append(None)
                else:
                    self.models.append(model)
                    logger.info(f"Loaded model {i+1}/{len(self.model_paths)} from {model_path}")
            except Exception as e:
                logger.error(f"Error loading model from {model_path}: {str(e)}")
                logger.error(traceback.format_exc())
                # Use a placeholder model
                self.models.append(None)

    def set_weights(self, weights: List[float]):
        """
        Set new weights for the ensemble model.

        Args:
            weights: New weights for each model
        """
        if len(weights) != len(self.model_paths):
            raise ValueError(f"Number of weights ({len(weights)}) must match number of models ({len(self.model_paths)})")

        # Normalize weights
        total = sum(weights)
        self.weights = [w / total for w in weights]

        logger.info(f"Updated ensemble model weights: {self.weights}")

    def predict(
        self,
        X: np.ndarray,
        steps: int = 1,
        method: str = 'weighted'
    ) -> np.ndarray:
        """
        Make ensemble predictions.

        Args:
            X: Input features
            steps: Number of steps to forecast (for ARIMA models)
            method: Ensemble method ('weighted', 'majority', 'max', 'min')

        Returns:
            np.ndarray: Ensemble predictions
        """
        try:
            # Get predictions from each model
            all_predictions = []

            for i, (model, model_type) in enumerate(zip(self.models, self.model_types)):
                # Skip None models
                if model is None:
                    logger.warning(f"Model {i} is None, skipping")
                    continue

                try:
                    # Make prediction based on model type
                    if model_type == 'arima':
                        # For ARIMA models
                        try:
                            # Try to predict with X data
                            predictions = model.predict(steps=steps, X=X)
                        except Exception as arima_error:
                            logger.warning(f"Error with ARIMA prediction: {str(arima_error)}. Trying alternative approach.")
                            # Try with a simpler approach - just predict one step
                            try:
                                predictions = model.predict(steps=1)

                                # Ensure predictions have the right shape for ensemble
                                if isinstance(X, np.ndarray):
                                    # Get the expected shape from X
                                    expected_rows = X.shape[0]

                                    # Check if we need to reshape
                                    if len(predictions.shape) == 1:
                                        # Convert 1D array to 2D column vector
                                        predictions = predictions.reshape(-1, 1)

                                    if predictions.shape[0] == 1 and expected_rows > 1:
                                        # Repeat the prediction to match expected shape
                                        predictions = np.repeat(predictions, expected_rows, axis=0)
                                        logger.info(f"Reshaped ARIMA predictions from shape (1, 1) to match input shape ({expected_rows}, 1)")
                            except Exception as e:
                                logger.error(f"Alternative ARIMA prediction also failed: {str(e)}")
                                # Create a dummy prediction
                                predictions = np.zeros((X.shape[0] if isinstance(X, np.ndarray) else 1, 1))
                    elif hasattr(model, 'predict'):
                        # Use predict method if available
                        try:
                            predictions = model.predict(X)

                            # Convert PyTorch tensor to numpy if needed
                            if hasattr(predictions, 'detach'):
                                predictions = predictions.detach().cpu().numpy()
                        except Exception as predict_error:
                            logger.error(f"Error with model.predict: {str(predict_error)}")
                            # Create a dummy prediction
                            predictions = np.zeros((X.shape[0] if isinstance(X, np.ndarray) else 1, 1))
                    else:
                        # For LSTM and TFT models
                        try:
                            predictions = model(X)

                            # Convert PyTorch tensor to numpy if needed
                            if hasattr(predictions, 'detach'):
                                predictions = predictions.detach().cpu().numpy()
                        except Exception as call_error:
                            logger.error(f"Error calling model directly: {str(call_error)}")
                            # Create a dummy prediction
                            predictions = np.zeros((X.shape[0] if isinstance(X, np.ndarray) else 1, 1))

                    # Ensure predictions have the right shape
                    if len(predictions.shape) == 1:
                        predictions = predictions.reshape(-1, 1)

                    all_predictions.append(predictions)

                except Exception as e:
                    logger.error(f"Error making prediction with model {i} ({model_type}): {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())

            if not all_predictions:
                logger.error("No valid predictions from any model")
                return np.zeros((X.shape[0] if isinstance(X, np.ndarray) else 1, 1))

            # Combine predictions based on method
            if method == 'weighted':
                # Weighted average
                ensemble_pred = np.zeros_like(all_predictions[0])
                valid_weights = []
                valid_predictions = []

                for i, pred in enumerate(all_predictions):
                    if pred.shape == ensemble_pred.shape:
                        valid_weights.append(self.weights[i])
                        valid_predictions.append(pred)
                    else:
                        logger.warning(f"Model '{self.model_paths[i]}' prediction shape {pred.shape} doesn't match expected shape {ensemble_pred.shape}")

                        # Try to reshape the predictions to match the expected shape
                        try:
                            if pred.shape[0] == 1 and ensemble_pred.shape[0] > 1:
                                # Repeat the single prediction to match the expected number of rows
                                reshaped_pred = np.repeat(pred, ensemble_pred.shape[0], axis=0)
                                logger.info(f"Successfully reshaped model '{self.model_paths[i]}' predictions to {reshaped_pred.shape}")
                                valid_weights.append(self.weights[i])
                                valid_predictions.append(reshaped_pred)
                            elif len(pred.shape) == 1 and len(ensemble_pred.shape) == 2:
                                # Convert 1D array to 2D column vector
                                reshaped_pred = pred.reshape(-1, 1)
                                if reshaped_pred.shape == ensemble_pred.shape:
                                    logger.info(f"Successfully reshaped model '{self.model_paths[i]}' predictions to {reshaped_pred.shape}")
                                    valid_weights.append(self.weights[i])
                                    valid_predictions.append(reshaped_pred)
                        except Exception as reshape_error:
                            logger.error(f"Failed to reshape predictions: {str(reshape_error)}")

                # Normalize weights
                total = sum(valid_weights)
                normalized_weights = [w / total for w in valid_weights]

                # Compute weighted average
                for i, pred in enumerate(valid_predictions):
                    ensemble_pred += normalized_weights[i] * pred

            elif method == 'majority':
                # Majority vote (for classification)
                ensemble_pred = np.zeros_like(all_predictions[0])
                for i in range(ensemble_pred.shape[0]):
                    # Get sign of each prediction
                    votes = [np.sign(pred[i, 0]) for pred in all_predictions]
                    # Count votes
                    pos_votes = sum(1 for v in votes if v > 0)
                    neg_votes = sum(1 for v in votes if v < 0)
                    # Majority wins
                    if pos_votes > neg_votes:
                        ensemble_pred[i, 0] = 1.0
                    elif neg_votes > pos_votes:
                        ensemble_pred[i, 0] = -1.0
                    else:
                        ensemble_pred[i, 0] = 0.0

            elif method == 'max':
                # Maximum prediction
                ensemble_pred = np.max(np.stack(all_predictions, axis=0), axis=0)

            elif method == 'min':
                # Minimum prediction
                ensemble_pred = np.min(np.stack(all_predictions, axis=0), axis=0)

            else:
                logger.error(f"Unsupported ensemble method: {method}")
                return np.zeros((X.shape[0], 1))

            return ensemble_pred

        except Exception as e:
            logger.error(f"Error making ensemble predictions: {str(e)}")
            logger.error(traceback.format_exc())
            return np.zeros((X.shape[0], 1))

    def evaluate(
        self,
        X: np.ndarray,
        y: np.ndarray,
        method: str = 'weighted',
        feature_names: List[str] = None
    ) -> Dict[str, float]:
        """
        Evaluate the ensemble model.

        Args:
            X: Input features
            y: Target values
            method: Ensemble method ('weighted', 'majority', 'max', 'min')
            feature_names: Optional list of feature names (not used in evaluation but kept for API compatibility)

        Returns:
            Dict[str, float]: Evaluation metrics
        """
        try:
            # Make predictions
            predictions = self.predict(X, method=method)

            # Calculate metrics
            mse = np.mean((predictions.flatten() - y) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(predictions.flatten() - y))

            # Calculate R-squared (coefficient of determination)
            ss_res = np.sum((y - predictions.flatten()) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0

            # Calculate directional accuracy
            direction_correct = np.sum((predictions.flatten() > 0) == (y > 0))
            direction_accuracy = direction_correct / len(y)

            # Calculate profit factor
            profits = np.sum(predictions.flatten()[predictions.flatten() > 0] * y[predictions.flatten() > 0])
            losses = np.sum(predictions.flatten()[predictions.flatten() < 0] * y[predictions.flatten() < 0])
            profit_factor = abs(profits / losses) if losses != 0 else float('inf')

            # Calculate Sharpe ratio
            returns = predictions.flatten() * y
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) != 0 else 0

            metrics = {
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'direction_accuracy': direction_accuracy,
                'profit_factor': profit_factor,
                'sharpe_ratio': sharpe_ratio
            }

            logger.info(f"Ensemble evaluation metrics: MSE={mse:.6f}, RMSE={rmse:.6f}, MAE={mae:.6f}, "
                       f"R2={r2:.6f}, Direction Accuracy={direction_accuracy:.6f}")

            return metrics

        except Exception as e:
            logger.error(f"Error evaluating ensemble model: {str(e)}")
            logger.error(traceback.format_exc())
            return {}

    def save_model(self, filepath: str) -> bool:
        """
        Save the ensemble model to disk.

        Args:
            filepath: Path to save the model

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

            # Prepare comprehensive model state including actual models
            model_state = {
                'model_paths': self.model_paths,
                'model_types': self.model_types,
                'model_names': self.model_names,
                'weights': self.weights,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'ensemble_type': 'weighted',  # Default ensemble type
                'num_models': len(self.models) if hasattr(self, 'models') else len(self.model_paths)
            }

            # For models loaded from memory, we need to save references to their actual files
            # This ensures the ensemble can be properly reconstructed
            if hasattr(self, 'models') and self.models:
                # Save model state information for reconstruction
                model_state['has_loaded_models'] = True
                model_state['model_info'] = []

                for i, model in enumerate(self.models):
                    if model is not None:
                        model_info = {
                            'index': i,
                            'type': self.model_types[i] if i < len(self.model_types) else 'unknown',
                            'name': self.model_names[i] if i < len(self.model_names) else f'model_{i}',
                            'path': self.model_paths[i] if i < len(self.model_paths) else f'memory_model_{i}',
                            'weight': self.weights[i] if i < len(self.weights) else 1.0 / len(self.models)
                        }
                        model_state['model_info'].append(model_info)
            else:
                model_state['has_loaded_models'] = False

            # Save model using pickle with protocol 4 for better compatibility
            with open(filepath, 'wb') as f:
                pickle.dump(model_state, f, protocol=pickle.HIGHEST_PROTOCOL)

            # Verify the file was saved correctly
            file_size = os.path.getsize(filepath)
            if file_size < 100:  # If file is suspiciously small
                logger.warning(f"Ensemble model file size is only {file_size} bytes, which may indicate incomplete save")
            else:
                logger.info(f"Ensemble model saved to {filepath} (size: {file_size} bytes)")

            return True
        except Exception as e:
            logger.error(f"Error saving ensemble model: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    @classmethod
    def load(cls, filepath: str):
        """
        Load an ensemble model from disk.

        Args:
            filepath: Path to load the model from

        Returns:
            EnsembleModel: Loaded ensemble model or None if failed
        """
        try:
            # Check if file exists
            if not os.path.exists(filepath):
                logger.error(f"Model file not found: {filepath}")
                return None

            # Verify file size
            file_size = os.path.getsize(filepath)
            if file_size < 100:
                logger.warning(f"Ensemble model file is suspiciously small ({file_size} bytes)")

            # Load model using pickle
            with open(filepath, 'rb') as f:
                model_state = pickle.load(f)

            # Extract model information
            model_paths = model_state.get('model_paths', [])
            model_types = model_state.get('model_types', [])
            model_names = model_state.get('model_names', [])
            weights = model_state.get('weights', None)

            # Validate loaded data
            if not model_paths or not model_types:
                logger.error("Invalid ensemble model file: missing model paths or types")
                return None

            # Create new ensemble model instance
            ensemble = cls(model_paths=model_paths, model_types=model_types, weights=weights)

            # Set model_names if available
            if model_names:
                ensemble.model_names = model_names

            # Log successful load with details
            logger.info(f"Ensemble model loaded from {filepath}")
            logger.info(f"  - Number of models: {len(model_paths)}")
            logger.info(f"  - Model types: {model_types}")
            logger.info(f"  - Weights: {weights}")
            logger.info(f"  - File size: {file_size} bytes")

            return ensemble
        except Exception as e:
            logger.error(f"Error loading ensemble model: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    def save_metrics(self, filepath: str, metrics: Dict[str, float]) -> bool:
        """
        Save ensemble model metrics to disk.

        Args:
            filepath: Path to save the metrics
            metrics: Dictionary of metrics to save

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

            # Convert numpy types to Python types for JSON serialization
            serializable_metrics = {}
            for key, value in metrics.items():
                if isinstance(value, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                                     np.uint8, np.uint16, np.uint32, np.uint64)):
                    serializable_metrics[key] = int(value)
                elif isinstance(value, (np.float_, np.float16, np.float32, np.float64)):
                    serializable_metrics[key] = float(value)
                else:
                    serializable_metrics[key] = value

            # Add timestamp and ensemble info
            serializable_metrics['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            serializable_metrics['model_paths'] = self.model_paths
            serializable_metrics['model_types'] = self.model_types
            serializable_metrics['weights'] = [float(w) for w in self.weights]

            # Save to JSON
            with open(filepath, 'w') as f:
                json.dump(serializable_metrics, f, indent=4)

            logger.info(f"Ensemble metrics saved to {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error saving ensemble metrics: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
