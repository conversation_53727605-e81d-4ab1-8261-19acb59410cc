"""
Master System Setup Script.
This script orchestrates the complete setup and optimization of the MT5 multi-terminal trading system.

Usage:
    python scripts/master_system_setup.py [options]

Options:
    --symbol BTCUSD.a        Symbol to process (default: BTCUSD.a)
    --years 5                Years of historical data (default: 5)
    --skip-optimization      Skip system optimization step
    --skip-data-collection   Skip data collection step
    --skip-model-training    Skip model training step
    --parallel-collection    Use parallel data collection
    --force-retrain          Force retraining of existing models
    --quick-setup            Perform quick setup (minimal data, faster)
"""
import argparse
import logging
import sys
import os
import subprocess
from datetime import datetime
from typing import List

# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging with UTF-8 encoding and immediate flushing
import sys
stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.flush = lambda: sys.stdout.flush()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/master_system_setup.log', encoding='utf-8'),
        stream_handler
    ]
)
logger = logging.getLogger(__name__)

# Ensure immediate output flushing
def flush_output():
    """Force flush all output streams."""
    sys.stdout.flush()
    sys.stderr.flush()

class MasterSystemSetup:
    """Master system setup orchestrator."""
    
    def __init__(self):
        """Initialize the master setup."""
        self.setup_stats = {}
        self.start_time = datetime.now()
        
    def run_script(self, script_path: str, args: List[str] = None) -> bool:
        """Run a Python script with optional arguments and show real-time output."""
        try:
            cmd = [sys.executable, script_path]
            if args:
                cmd.extend(args)

            logger.info(f"[STARTING] Running: {' '.join(cmd)}")
            logger.info(f"[PROGRESS] Script execution starting...")
            flush_output()

            # Use Popen for real-time output streaming
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=os.getcwd()
            )

            # Stream output in real-time
            output_lines = []
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # Print to console immediately for real-time feedback
                    print(output.strip())
                    flush_output()
                    # Also log it
                    logger.info(f"[SCRIPT OUTPUT] {output.strip()}")
                    output_lines.append(output.strip())

            # Wait for process to complete
            return_code = process.poll()

            if return_code == 0:
                logger.info(f"[SUCCESS] Script completed successfully: {script_path}")
                logger.info(f"[PROGRESS] Script execution finished with success")
                return True
            else:
                logger.error(f"[FAILED] Script failed: {script_path}")
                logger.error(f"[ERROR] Return code: {return_code}")
                return False

        except Exception as e:
            logger.error(f"[EXCEPTION] Exception running script {script_path}: {str(e)}")
            return False
    
    def step_1_system_optimization(self, symbol: str) -> bool:
        """Step 1: System optimization and cleanup."""
        logger.info("\n" + "="*80)
        logger.info("STEP 1: SYSTEM OPTIMIZATION AND CLEANUP")
        logger.info("="*80)
        logger.info(f"[STEP 1] Starting system optimization for symbol: {symbol}")
        logger.info(f"[STEP 1] This will clean up old files, optimize data structures, and prepare the system")

        script_path = "scripts/comprehensive_system_optimization.py"
        args = [
            "--symbol", symbol,
            "--full-optimization"
        ]

        logger.info(f"[STEP 1] Executing optimization script with full optimization mode...")
        success = self.run_script(script_path, args)

        if success:
            logger.info(f"[STEP 1] [SUCCESS] System optimization completed successfully")
        else:
            logger.error(f"[STEP 1] [FAILED] System optimization failed")

        self.setup_stats['optimization'] = success
        return success
    
    def step_2_data_collection(self, symbol: str, years: int, parallel: bool = False) -> bool:
        """Step 2: Optimized data collection."""
        logger.info("\n" + "="*80)
        logger.info("STEP 2: OPTIMIZED DATA COLLECTION")
        logger.info("="*80)
        logger.info(f"[STEP 2] Starting data collection for symbol: {symbol}")
        logger.info(f"[STEP 2] Collecting {years} years of historical data")
        logger.info(f"[STEP 2] Parallel processing: {'ENABLED' if parallel else 'DISABLED'}")
        logger.info(f"[STEP 2] This will collect data for all 5 terminals and 5 timeframes (25 total tasks)")

        script_path = "scripts/optimize_data_collection.py"
        args = [
            "--symbol", symbol,
            "--years", str(years),
            "--validate",
            "--clean-duplicates"
        ]

        if parallel:
            args.append("--parallel")
            logger.info(f"[STEP 2] Using parallel collection for faster processing")
        else:
            logger.info(f"[STEP 2] Using sequential collection for stable processing")

        logger.info(f"[STEP 2] Executing data collection script...")
        success = self.run_script(script_path, args)

        if success:
            logger.info(f"[STEP 2] [SUCCESS] Data collection completed successfully")
        else:
            logger.error(f"[STEP 2] [FAILED] Data collection failed")

        self.setup_stats['data_collection'] = success
        return success
    
    def step_3_system_validation_and_training(
        self,
        symbol: str,
        years: int,
        force_retrain: bool = False,
        skip_data_collection: bool = False
    ) -> bool:
        """Step 3: System validation and model training."""
        logger.info("\n" + "="*80)
        logger.info("STEP 3: SYSTEM VALIDATION AND MODEL TRAINING")
        logger.info("="*80)
        logger.info(f"[STEP 3] Starting system validation and model training for symbol: {symbol}")
        logger.info(f"[STEP 3] Training models with {years} years of data")
        logger.info(f"[STEP 3] Force retrain: {'YES' if force_retrain else 'NO'}")
        logger.info(f"[STEP 3] Skip data collection: {'YES' if skip_data_collection else 'NO'}")
        logger.info(f"[STEP 3] This will train models for all 5 terminals:")
        logger.info(f"[STEP 3]   - Terminal 1: ARIMA models")
        logger.info(f"[STEP 3]   - Terminal 2: LSTM models")
        logger.info(f"[STEP 3]   - Terminal 3: TFT models")
        logger.info(f"[STEP 3]   - Terminal 4: LSTM+ARIMA ensemble")
        logger.info(f"[STEP 3]   - Terminal 5: TFT+ARIMA ensemble")

        script_path = "scripts/validate_and_train_system.py"
        args = [
            "--symbol", symbol,
            "--years", str(years)
        ]

        if force_retrain:
            args.append("--force-retrain")
            logger.info(f"[STEP 3] Force retraining enabled - will retrain existing models")

        if skip_data_collection:
            args.append("--skip-data-collection")
            logger.info(f"[STEP 3] Skipping data collection - using existing data")

        logger.info(f"[STEP 3] Executing validation and training script...")
        logger.info(f"[STEP 3] NOTE: This step may take a long time depending on data volume")
        success = self.run_script(script_path, args)

        if success:
            logger.info(f"[STEP 3] [SUCCESS] System validation and training completed successfully")
        else:
            logger.error(f"[STEP 3] [FAILED] System validation and training failed")

        self.setup_stats['validation_training'] = success
        return success
    
    def step_4_final_validation(self, symbol: str) -> bool:
        """Step 4: Final system validation."""
        logger.info("\n" + "="*80)
        logger.info("STEP 4: FINAL SYSTEM VALIDATION")
        logger.info("="*80)
        logger.info(f"[STEP 4] Starting final system validation for symbol: {symbol}")
        logger.info(f"[STEP 4] This will validate all trained models and system components")
        logger.info(f"[STEP 4] Checking model files, data integrity, and system readiness")

        script_path = "scripts/validate_and_train_system.py"
        args = [
            "--symbol", symbol,
            "--validate-only",
            "--skip-data-collection"
        ]

        logger.info(f"[STEP 4] Executing final validation script...")
        success = self.run_script(script_path, args)

        if success:
            logger.info(f"[STEP 4] [SUCCESS] Final validation completed successfully")
        else:
            logger.error(f"[STEP 4] [FAILED] Final validation failed")

        self.setup_stats['final_validation'] = success
        return success
    
    def generate_setup_report(self, symbol: str) -> None:
        """Generate a comprehensive setup report."""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        logger.info("\n" + "="*80)
        logger.info("MASTER SYSTEM SETUP COMPLETION REPORT")
        logger.info("="*80)
        
        logger.info(f"\n[SUMMARY] SETUP SUMMARY:")
        logger.info(f"Symbol: {symbol}")
        logger.info(f"Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Total duration: {duration}")

        logger.info(f"\n[RESULTS] STEP RESULTS:")
        step_names = {
            'optimization': 'System Optimization',
            'data_collection': 'Data Collection',
            'validation_training': 'Validation & Training',
            'final_validation': 'Final Validation'
        }
        
        successful_steps = 0
        total_steps = len(self.setup_stats)
        
        for step_key, step_name in step_names.items():
            if step_key in self.setup_stats:
                status = "[SUCCESS]" if self.setup_stats[step_key] else "[FAILED]"
                logger.info(f"  {step_name}: {status}")
                if self.setup_stats[step_key]:
                    successful_steps += 1
        
        success_rate = (successful_steps / total_steps) * 100 if total_steps > 0 else 0
        logger.info(f"\nOverall success rate: {successful_steps}/{total_steps} ({success_rate:.1f}%)")
        
        # System status and next steps
        logger.info("\n" + "="*80)
        if success_rate >= 75:
            logger.info("[SUCCESS] SYSTEM SETUP COMPLETED SUCCESSFULLY")
            logger.info("\n[READY] SYSTEM IS READY FOR TRADING")
            logger.info("\nNext steps:")
            logger.info("1. Start the multi-terminal trading bot:")
            logger.info("   python run_trading_bot.py --dashboard")
            logger.info("2. Monitor system performance:")
            logger.info("   python scripts/run_dashboard.py")
            logger.info("3. Check logs for any issues:")
            logger.info("   tail -f logs/trading_bot_*.log")
        else:
            logger.info("[WARNING] SYSTEM SETUP COMPLETED WITH ISSUES")
            logger.info("\n[ATTENTION] SYSTEM REQUIRES ATTENTION")
            logger.info("\nRecommended actions:")
            logger.info("1. Review the logs for failed steps")
            logger.info("2. Fix any identified issues")
            logger.info("3. Re-run the failed steps individually")
            logger.info("4. Contact support if issues persist")
        
        logger.info("="*80)
    
    def quick_setup(self, symbol: str) -> bool:
        """Perform a quick setup with minimal data."""
        logger.info("\n[QUICK SETUP] PERFORMING QUICK SETUP")
        logger.info("This will set up the system with minimal data for faster initialization.")
        
        # Quick optimization
        logger.info("\n1. Quick system optimization...")
        if not self.step_1_system_optimization(symbol):
            logger.warning("Quick optimization had issues, but continuing...")
        
        # Minimal data collection (1 year)
        logger.info("\n2. Minimal data collection...")
        if not self.step_2_data_collection(symbol, years=1, parallel=True):
            logger.error("Data collection failed")
            return False
        
        # Quick training
        logger.info("\n3. Quick model training...")
        if not self.step_3_system_validation_and_training(
            symbol, years=1, force_retrain=False, skip_data_collection=True
        ):
            logger.error("Model training failed")
            return False
        
        logger.info("\n[SUCCESS] QUICK SETUP COMPLETED")
        return True

    def full_setup(
        self,
        symbol: str,
        years: int,
        skip_optimization: bool = False,
        skip_data_collection: bool = False,
        skip_model_training: bool = False,
        parallel_collection: bool = False,
        force_retrain: bool = False
    ) -> bool:
        """Perform a full system setup."""
        logger.info("\n[FULL SETUP] PERFORMING FULL SYSTEM SETUP")
        logger.info("This will set up the complete system with all optimizations.")
        logger.info(f"[FULL SETUP] Configuration:")
        logger.info(f"[FULL SETUP]   Symbol: {symbol}")
        logger.info(f"[FULL SETUP]   Years of data: {years}")
        logger.info(f"[FULL SETUP]   Skip optimization: {skip_optimization}")
        logger.info(f"[FULL SETUP]   Skip data collection: {skip_data_collection}")
        logger.info(f"[FULL SETUP]   Skip model training: {skip_model_training}")
        logger.info(f"[FULL SETUP]   Parallel collection: {parallel_collection}")
        logger.info(f"[FULL SETUP]   Force retrain: {force_retrain}")

        # Step 1: System optimization
        logger.info(f"\n[FULL SETUP] Preparing to execute Step 1...")
        if not skip_optimization:
            logger.info(f"[FULL SETUP] Step 1 will be executed")
            if not self.step_1_system_optimization(symbol):
                logger.warning("[FULL SETUP] System optimization had issues, but continuing...")
        else:
            logger.info("[FULL SETUP] Skipping system optimization as requested")
            self.setup_stats['optimization'] = True

        # Step 2: Data collection
        logger.info(f"\n[FULL SETUP] Preparing to execute Step 2...")
        if not skip_data_collection:
            logger.info(f"[FULL SETUP] Step 2 will be executed")
            if not self.step_2_data_collection(symbol, years, parallel_collection):
                logger.error("[FULL SETUP] Data collection failed - aborting setup")
                return False
        else:
            logger.info("[FULL SETUP] Skipping data collection as requested")
            self.setup_stats['data_collection'] = True

        # Step 3: System validation and training
        logger.info(f"\n[FULL SETUP] Preparing to execute Step 3...")
        if not skip_model_training:
            logger.info(f"[FULL SETUP] Step 3 will be executed")
            if not self.step_3_system_validation_and_training(
                symbol, years, force_retrain, skip_data_collection
            ):
                logger.error("[FULL SETUP] System validation and training failed - aborting setup")
                return False
        else:
            logger.info("[FULL SETUP] Skipping model training as requested")
            self.setup_stats['validation_training'] = True

        # Step 4: Final validation
        logger.info(f"\n[FULL SETUP] Preparing to execute Step 4...")
        if not self.step_4_final_validation(symbol):
            logger.warning("[FULL SETUP] Final validation had issues, but setup is complete")

        logger.info("\n[FULL SETUP] [SUCCESS] FULL SETUP COMPLETED")
        return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Master system setup and optimization')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Symbol to process')
    parser.add_argument('--years', type=int, default=5, help='Years of historical data')
    parser.add_argument('--skip-optimization', action='store_true', help='Skip system optimization')
    parser.add_argument('--skip-data-collection', action='store_true', help='Skip data collection')
    parser.add_argument('--skip-model-training', action='store_true', help='Skip model training')
    parser.add_argument('--parallel-collection', action='store_true', help='Use parallel data collection')
    parser.add_argument('--force-retrain', action='store_true', help='Force retraining of models')
    parser.add_argument('--quick-setup', action='store_true', help='Perform quick setup')
    
    args = parser.parse_args()

    logger.info("="*80)
    logger.info("MT5 MULTI-TERMINAL TRADING SYSTEM - MASTER SETUP")
    logger.info("="*80)
    logger.info(f"[MAIN] Starting master system setup...")
    logger.info(f"[MAIN] Configuration parameters:")
    logger.info(f"[MAIN]   Symbol: {args.symbol}")
    logger.info(f"[MAIN]   Years of data: {args.years}")
    logger.info(f"[MAIN]   Quick setup: {args.quick_setup}")
    logger.info(f"[MAIN]   Parallel collection: {args.parallel_collection}")
    logger.info(f"[MAIN]   Force retrain: {args.force_retrain}")
    logger.info(f"[MAIN]   Skip optimization: {args.skip_optimization}")
    logger.info(f"[MAIN]   Skip data collection: {args.skip_data_collection}")
    logger.info(f"[MAIN]   Skip model training: {args.skip_model_training}")

    # Initialize master setup
    logger.info(f"[MAIN] Initializing master setup orchestrator...")
    master_setup = MasterSystemSetup()
    logger.info(f"[MAIN] Master setup orchestrator initialized successfully")

    try:
        if args.quick_setup:
            logger.info(f"[MAIN] Executing QUICK SETUP mode...")
            success = master_setup.quick_setup(args.symbol)
        else:
            logger.info(f"[MAIN] Executing FULL SETUP mode...")
            success = master_setup.full_setup(
                symbol=args.symbol,
                years=args.years,
                skip_optimization=args.skip_optimization,
                skip_data_collection=args.skip_data_collection,
                skip_model_training=args.skip_model_training,
                parallel_collection=args.parallel_collection,
                force_retrain=args.force_retrain
            )

        # Generate final report
        logger.info(f"[MAIN] Generating final setup report...")
        master_setup.generate_setup_report(args.symbol)

        if success:
            logger.info("[MAIN] [SUCCESS] Master system setup completed successfully!")
            return 0
        else:
            logger.error("[MAIN] [FAILED] Master system setup completed with errors!")
            return 1
            
    except KeyboardInterrupt:
        logger.info("\nSetup interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during setup: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == '__main__':
    sys.exit(main())
