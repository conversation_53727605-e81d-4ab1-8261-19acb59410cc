"""
Test script for the Ensemble Model implementation.
This script tests the functionality of the ensemble model.
"""
import sys
import logging
import os
import numpy as np
import unittest
from unittest.mock import MagicMock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append('.')

# Import the ensemble model
from model.ensemble_model import EnsembleModel
from model.model_trainer import ModelManager

class TestEnsembleModel(unittest.TestCase):
    """Test cases for the Ensemble Model."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock ModelManager
        self.model_manager = MagicMock(spec=ModelManager)

        # Set up model names and weights
        self.model_names = ['model1', 'model2']
        self.weights = [0.6, 0.4]

        # Configure the mock to return our model names
        self.model_manager.list_models.return_value = self.model_names

        # Create the ensemble model
        self.ensemble = EnsembleModel(
            model_manager=self.model_manager,
            ensemble_name='test_ensemble',
            model_names=self.model_names,
            weights=self.weights
        )

    def test_initialization(self):
        """Test ensemble model initialization."""
        # Check that the model was initialized correctly
        self.assertEqual(self.ensemble.ensemble_name, 'test_ensemble')
        self.assertEqual(self.ensemble.model_names, self.model_names)
        self.assertEqual(self.ensemble.weights, self.weights)
        self.assertEqual(self.ensemble.model_manager, self.model_manager)

    def test_set_weights(self):
        """Test setting weights for the ensemble model."""
        # Set new weights
        new_weights = [0.7, 0.3]
        self.ensemble.set_weights(new_weights)

        # Check that weights were updated
        self.assertEqual(self.ensemble.weights, new_weights)

        # Test with invalid weights (wrong length)
        with self.assertRaises(ValueError):
            self.ensemble.set_weights([0.5, 0.3, 0.2])

    def test_predict_weighted(self):
        """Test weighted prediction method."""
        # Create mock predictions for component models
        pred1 = np.array([[0.1], [0.2]])
        pred2 = np.array([[0.3], [0.4]])

        # Configure the model manager to return these predictions
        self.model_manager.predict.side_effect = [pred1, pred2]

        # Create input data
        X = np.random.rand(2, 10, 5)  # Batch size 2, sequence length 10, features 5

        # Make prediction
        prediction = self.ensemble.predict(X, method='weighted')

        # Check that model_manager.predict was called for each model
        self.assertEqual(self.model_manager.predict.call_count, 2)

        # Check prediction shape
        self.assertEqual(prediction.shape, (2, 1))

        # Check prediction values (weighted average)
        expected = self.weights[0] * pred1 + self.weights[1] * pred2
        np.testing.assert_array_almost_equal(prediction, expected)

    def test_predict_majority(self):
        """Test majority vote prediction method."""
        # Create mock predictions for component models
        pred1 = np.array([[0.1], [-0.2]])  # Positive, Negative
        pred2 = np.array([[-0.3], [-0.4]])  # Negative, Negative

        # Configure the model manager to return these predictions
        self.model_manager.predict.side_effect = [pred1, pred2]

        # Create input data
        X = np.random.rand(2, 10, 5)  # Batch size 2, sequence length 10, features 5

        # Make prediction
        prediction = self.ensemble.predict(X, method='majority')

        # Check prediction shape
        self.assertEqual(prediction.shape, (2, 1))

        # Check prediction values (majority vote)
        # First sample: 1 positive, 1 negative -> 0
        # Second sample: 2 negative -> -1
        expected = np.array([[0.0], [-1.0]])
        np.testing.assert_array_almost_equal(prediction, expected)

    def test_predict_max(self):
        """Test max prediction method."""
        # Create mock predictions for component models
        pred1 = np.array([[0.1], [0.2]])
        pred2 = np.array([[0.3], [0.1]])

        # Configure the model manager to return these predictions
        self.model_manager.predict.side_effect = [pred1, pred2]

        # Create input data
        X = np.random.rand(2, 10, 5)  # Batch size 2, sequence length 10, features 5

        # Make prediction
        prediction = self.ensemble.predict(X, method='max')

        # Check prediction values (maximum)
        expected = np.array([[0.3], [0.2]])
        np.testing.assert_array_almost_equal(prediction, expected)

    def test_predict_min(self):
        """Test min prediction method."""
        # Create mock predictions for component models
        pred1 = np.array([[0.1], [0.2]])
        pred2 = np.array([[0.3], [0.1]])

        # Configure the model manager to return these predictions
        self.model_manager.predict.side_effect = [pred1, pred2]

        # Create input data
        X = np.random.rand(2, 10, 5)  # Batch size 2, sequence length 10, features 5

        # Make prediction
        prediction = self.ensemble.predict(X, method='min')

        # Check prediction values (minimum)
        expected = np.array([[0.1], [0.1]])
        np.testing.assert_array_almost_equal(prediction, expected)

    def test_evaluate(self):
        """Test evaluate method."""
        # Mock the predict method
        self.ensemble.predict = MagicMock(return_value=np.array([[0.1], [0.2]]))

        # Create input data
        X = np.random.rand(2, 10, 5)  # Batch size 2, sequence length 10, features 5
        y = np.array([0.15, 0.25])  # Target values

        # Evaluate the model
        metrics = self.ensemble.evaluate(X, y)

        # Check that predict was called
        self.ensemble.predict.assert_called_once()

        # Check that metrics were calculated
        self.assertIn('mse', metrics)
        self.assertIn('rmse', metrics)
        self.assertIn('mae', metrics)
        self.assertIn('direction_accuracy', metrics)

    def test_save_load_model(self):
        """Test save and load model methods."""
        # Create a temporary file path
        filepath = 'test_ensemble_model.pkl'

        # Save the model
        self.ensemble.save_model(filepath)

        # Check that the file was created
        self.assertTrue(os.path.exists(filepath))

        # Update the mock to include the new model names
        self.model_manager.list_models.return_value = self.model_names + ['model3', 'model4']

        # Create a new ensemble model
        new_ensemble = EnsembleModel(
            model_manager=self.model_manager,
            ensemble_name='new_ensemble',
            model_names=['model3', 'model4'],
            weights=[0.5, 0.5]
        )

        # Load the saved model
        new_ensemble.load_model(filepath)

        # Check that the model was loaded correctly
        self.assertEqual(new_ensemble.ensemble_name, 'test_ensemble')
        self.assertEqual(new_ensemble.model_names, self.model_names)
        self.assertEqual(new_ensemble.weights, self.weights)

        # Clean up
        os.remove(filepath)

    def test_error_handling(self):
        """Test error handling in the ensemble model."""
        # Test with invalid model names
        self.model_manager.list_models.return_value = ['model3', 'model4']

        with self.assertRaises(ValueError):
            EnsembleModel(
                model_manager=self.model_manager,
                ensemble_name='test_ensemble',
                model_names=['invalid_model1', 'invalid_model2'],
                weights=[0.6, 0.4]
            )

        # Test with None predictions from component models
        self.model_manager.predict.side_effect = [None, np.array([[0.3], [0.4]])]

        # Create input data
        X = np.random.rand(2, 10, 5)  # Batch size 2, sequence length 10, features 5

        # Make prediction (should handle None gracefully)
        prediction = self.ensemble.predict(X)

        # Check that prediction has the right shape
        self.assertEqual(prediction.shape, (2, 1))

def main():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    main()
