"""
MT5 Connector Module.
This module handles the connection to MT5 terminals and provides methods for data retrieval and order execution.
It uses the standardized MT5 initializer to maintain consistent initialization across the codebase.
"""
import logging
import time
from typing import Dict, List, Optional
import pandas as pd
import numpy as np
import Meta<PERSON><PERSON>r<PERSON> as mt5
from config.credentials import MT5_TERMINALS
from utils.mt5_connection_manager import MT5ConnectionManagerExtension
from utils.mt5_initializer import initialize_mt5_once, initialize_all_terminals, get_terminal_status

# Configure logger
logger = logging.getLogger('mt5_connector')

class MT5Connector:
    """
    MT5 Connector class for handling connections to MT5 terminals and providing methods for
    data retrieval and order execution.
    """
    def __init__(self):
        """Initialize MT5Connector."""
        self.connected_terminals: Dict[int, Dict] = {}
        self.active_terminal_id: Optional[int] = None
        self.connection_manager = MT5ConnectionManagerExtension()
        self._initialize_mt5()

    def _initialize_mt5(self) -> bool:
        """
        Initialize MT5 with proper parameters using the standardized initializer.
        This method ensures consistent initialization across the codebase.
        It uses the enhanced initialize_all_terminals function to initialize all 5 terminals
        with retry mechanism and proper error handling.

        Returns:
            bool: True if initialization is successful, False otherwise.
        """
        try:
            logger.info("Initializing MT5 using the enhanced standardized initializer...")

            # Use the enhanced initialize_all_terminals function to initialize all 5 terminals
            # This function includes retry mechanism and proper error handling
            terminal_status = initialize_all_terminals(max_retries=3, retry_delay=5)

            # Check if at least Terminal 1 was initialized successfully
            if not terminal_status[1]['initialized']:
                logger.error("Failed to initialize Terminal 1 (primary terminal)")
                logger.info("Please check that Terminal 1 is running and accessible")
                logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
                return False

            # Get connected terminals from the connection manager for additional information
            logger.info("Getting connected terminals from the connection manager...")
            connected_terminals = self.connection_manager.initialize_all_terminals()

            if connected_terminals:
                self.connected_terminals = connected_terminals
                # Set Terminal 1 as the active terminal (most reliable)
                self.active_terminal_id = 1
                logger.info(f"Successfully initialized {len(connected_terminals)} MT5 terminals")
                logger.info(f"Active terminal: {self.active_terminal_id} ({self.connected_terminals[self.active_terminal_id]['name']})")

                # Log the status of each terminal
                initialized_terminals = [tid for tid, status in terminal_status.items() if status['initialized']]
                logger.info(f"Initialized terminals: {initialized_terminals}")

                for terminal_id, terminal_info in self.connected_terminals.items():
                    algo_trading_enabled = terminal_info.get('algo_trading_enabled', False)
                    status = "[ENABLED]" if algo_trading_enabled else "[DISABLED]"
                    logger.info(f"Terminal {terminal_id} ({terminal_info['name']}): Algo Trading {status}")

                # Verify that we have at least one terminal with Algo Trading enabled
                algo_enabled_terminals = [tid for tid, status in terminal_status.items()
                                         if status['initialized'] and status['algo_trading_enabled']]

                if not algo_enabled_terminals:
                    logger.warning("No terminals have Algo Trading enabled!")
                    logger.info("Please enable Algo Trading in at least one terminal manually")
                    logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")

                return True
            else:
                # Even if connection manager failed, we might still have initialized terminals
                # from the initialize_all_terminals function
                initialized_count = sum(1 for status in terminal_status.values() if status['initialized'])

                if initialized_count > 0:
                    logger.warning("Connection manager failed but some terminals were initialized")
                    self.active_terminal_id = 1  # Default to Terminal 1

                    # Create minimal terminal info
                    for terminal_id in range(1, 6):
                        if terminal_status[terminal_id]['initialized']:
                            self.connected_terminals[terminal_id] = {
                                'terminal_id': terminal_id,
                                'name': f'Terminal {terminal_id}',
                                'algo_trading_enabled': terminal_status[terminal_id]['algo_trading_enabled'],
                                'connected_at': time.time()
                            }

                    logger.info(f"Recovered {initialized_count} initialized terminals")
                    return True
                else:
                    logger.error("Failed to initialize any MT5 terminals")
                    logger.info("Please check that all terminals are running and accessible")
                    logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
                    return False
        except Exception as e:
            logger.error(f"Error initializing MT5: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def connect_terminal(self, terminal_id: int) -> bool:
        """
        Connect to a specific MT5 terminal using the standardized MT5 initializer.
        Following the MT5 connection guide best practices:
        1. Never calls mt5.shutdown() which can disable Algo Trading
        2. Uses the standardized initializer to ensure MT5 is initialized only once
        3. Provides detailed information about Algo Trading status
        4. Uses terminal status tracking to avoid redundant initialization

        Args:
            terminal_id: Terminal ID to connect to

        Returns:
            bool: True if connection is successful, False otherwise
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal ID {terminal_id} not found in configuration")
            logger.info("Please check the terminal configuration in config/credentials.py")
            return False

        config = MT5_TERMINALS[terminal_id]
        logger.info(f"Connecting to Terminal {terminal_id} ({config.get('name', f'Terminal {terminal_id}')})...")

        # Check if terminal is already initialized using our status tracking
        terminal_status = get_terminal_status()
        if terminal_status[terminal_id]['initialized']:
            logger.info(f"Terminal {terminal_id} is already initialized, using existing connection")
        else:
            # Use the standardized initializer to ensure MT5 is initialized only once
            # This is critical to prevent disabling Algo Trading
            if not initialize_mt5_once(terminal_id):
                logger.error(f"Failed to initialize MT5 with Terminal {terminal_id}")
                logger.info("Please check that the terminal is running and accessible")
                logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
                return False

        try:
            # Get terminal info to verify connection
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                logger.error(f"Failed to get terminal info: {mt5.last_error()}")
                return False

            # Check if terminal is connected
            if not terminal_info.connected:
                logger.error(f"Terminal {terminal_id} is not connected to the server")
                logger.info("Please check your internet connection and make sure the terminal is connected")
                return False

            # Verify connection by getting account info
            account_info = mt5.account_info()
            if account_info is None:
                logger.error(f"Failed to get account info: {mt5.last_error()}")
                logger.info("This might indicate that the terminal is not logged in or the connection to the server is lost")
                return False

            # Check if Algo Trading is enabled in terminal info
            terminal_trade_allowed = False
            if hasattr(terminal_info, 'trade_allowed'):
                terminal_trade_allowed = terminal_info.trade_allowed
                logger.info(f"  Terminal trade_allowed: {terminal_trade_allowed}")
                if not terminal_trade_allowed:
                    logger.warning("  Algo Trading button is not enabled in the MT5 terminal")
                    logger.info("  Please enable it by clicking the 'Algo Trading' button in the toolbar")

            # Check if Algo Trading is enabled in account info
            account_trade_expert = False
            if hasattr(account_info, 'trade_expert'):
                account_trade_expert = account_info.trade_expert
                logger.info(f"  Account trade_expert: {account_trade_expert}")
                if not account_trade_expert:
                    logger.warning("  Expert Advisors are not allowed to trade for this account")
                    logger.info("  This might be due to an account change or server restrictions")

            # Determine if Algo Trading is enabled
            algo_trading_enabled = terminal_trade_allowed and account_trade_expert

            if algo_trading_enabled:
                logger.info(f"[SUCCESS] Algo Trading is ENABLED for Terminal {terminal_id}")
            else:
                logger.warning(f"[FAILED] Algo Trading is DISABLED for Terminal {terminal_id}")

                # Provide specific guidance based on what's disabled
                if not terminal_trade_allowed and not account_trade_expert:
                    logger.warning("Both terminal and account trading permissions are disabled")
                    logger.info("1. Click the 'Algo Trading' button in the toolbar to enable it")
                    logger.info("2. Check if your account allows Expert Advisors to trade")
                elif not terminal_trade_allowed:
                    logger.warning("The Algo Trading button is not enabled in the MT5 terminal")
                    logger.info("Click the 'Algo Trading' button in the toolbar to enable it")
                elif not account_trade_expert:
                    logger.warning("Expert Advisors are not allowed to trade for this account")
                    logger.info("This might be due to an account change or server restrictions")

                logger.info("Please refer to documentation/technical/mt5_connection_guide.md for detailed instructions")

            # Store connection info
            self.connected_terminals[terminal_id] = {
                'terminal_id': terminal_id,
                'name': config['name'],
                'login': config['login'],
                'server': config['server'],
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'leverage': account_info.leverage,
                'algo_trading_enabled': algo_trading_enabled,
                'terminal_trade_allowed': terminal_trade_allowed,
                'account_trade_expert': account_trade_expert,
                'connected_at': time.time()
            }

            self.active_terminal_id = terminal_id
            logger.info(f"Successfully connected to Terminal {terminal_id} ({config['name']})")
            logger.info(f"Account Balance: {account_info.balance}, Equity: {account_info.equity}")

            return True

        except Exception as e:
            logger.error(f"Error connecting to Terminal {terminal_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def disconnect_terminal(self) -> bool:
        """
        Disconnect from the current MT5 terminal.
        Note: This method does NOT call mt5.shutdown() to ensure Algo Trading remains enabled.

        Returns:
            bool: True if disconnection is successful, False otherwise
        """
        # We don't need to check if MT5 is initialized here
        # Just update our internal state

        # We don't call mt5.shutdown() to ensure Algo Trading remains enabled
        # Instead, we just update our internal state
        logger.info("Disconnected from MT5 terminal (without shutting down)")

        if self.active_terminal_id in self.connected_terminals:
            del self.connected_terminals[self.active_terminal_id]

        self.active_terminal_id = None
        return True

    def get_account_info(self, terminal_id: Optional[int] = None) -> Optional[Dict]:
        """
        Get account information for a specific terminal.

        Args:
            terminal_id: Terminal ID to get account info for. If None, use active terminal.

        Returns:
            Dict: Account information or None if failed
        """
        terminal_id = terminal_id or self.active_terminal_id

        if terminal_id is None:
            logger.error("No active terminal")
            return None

        if terminal_id not in self.connected_terminals:
            logger.error(f"Terminal {terminal_id} is not connected")
            return None

        # Connect to terminal if not active
        if terminal_id != self.active_terminal_id:
            if not self.connect_terminal(terminal_id):
                return None

        account_info = mt5.account_info()
        if account_info is None:
            logger.error(f"Failed to get account info: {mt5.last_error()}")
            return None

        # Convert account_info to dictionary
        account_dict = {
            'login': account_info.login,
            'server': account_info.server,
            'balance': account_info.balance,
            'equity': account_info.equity,
            'margin': account_info.margin,
            'free_margin': account_info.margin_free,
            'leverage': account_info.leverage,
            'profit': account_info.profit
        }

        return account_dict

    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """
        Get symbol information.

        Args:
            symbol: Symbol to get information for (format: 'XXXYYY.a')

        Returns:
            Dict: Symbol information or None if failed
        """
        # Use the standardized initializer to ensure MT5 is initialized
        # This is critical to prevent disabling Algo Trading
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            logger.info("Please check that Terminal 1 is running and accessible")
            logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
            return None

        # Validate symbol format
        if not self._validate_symbol_format(symbol):
            logger.warning(f"Symbol {symbol} does not follow the recommended format 'XXXYYY.a'")
            # Continue anyway as some symbols might have different formats

        try:
            # Get symbol information
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {symbol}: {mt5.last_error()}")
                return None

            # Convert symbol_info to dictionary
            symbol_dict = {
                'name': symbol_info.name,
                'point': symbol_info.point,
                'digits': symbol_info.digits,
                'spread': symbol_info.spread,
                'tick_value': symbol_info.trade_tick_value,
                'tick_size': symbol_info.trade_tick_size,
                'contract_size': symbol_info.trade_contract_size,
                'volume_min': symbol_info.volume_min,
                'volume_max': symbol_info.volume_max,
                'volume_step': symbol_info.volume_step
            }

            logger.info(f"Successfully retrieved symbol info for {symbol}")
            logger.info(f"Point: {symbol_dict['point']}, Digits: {symbol_dict['digits']}, Spread: {symbol_dict['spread']}")

            return symbol_dict

        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def get_current_tick(self, symbol: str) -> Optional[Dict]:
        """
        Get current tick information for a symbol.

        Args:
            symbol: Symbol to get tick information for

        Returns:
            Dict: Tick information with ask, bid, last prices or None if failed
        """
        # Use the standardized initializer to ensure MT5 is initialized
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            return None

        try:
            # Get current tick
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"Failed to get tick info for {symbol}: {mt5.last_error()}")
                return None

            # Convert tick to dictionary
            tick_dict = {
                'ask': tick.ask,
                'bid': tick.bid,
                'last': tick.last,
                'volume': tick.volume,
                'time': tick.time,
                'flags': tick.flags,
                'volume_real': tick.volume_real
            }

            return tick_dict

        except Exception as e:
            logger.error(f"Error getting tick info for {symbol}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _validate_symbol_format(self, symbol: str) -> bool:
        """
        Validate symbol format (XXXYYY.a).

        Args:
            symbol: Symbol to validate

        Returns:
            bool: True if symbol format is valid, False otherwise
        """
        import re
        # Match pattern like BTCUSD.a, EURUSD.a, etc.
        # First part can be 3-6 characters, second part should be exactly 3 characters
        pattern = r'^[A-Z]{3,6}[A-Z]{3}\.a$'
        return bool(re.match(pattern, symbol))

    def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        num_bars: Optional[int] = None
    ) -> Optional[pd.DataFrame]:
        """
        Get historical data for a symbol.

        Args:
            symbol: Symbol to get data for
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4', 'D1')
            start_date: Start date in 'YYYY-MM-DD' format (optional if num_bars is provided)
            end_date: End date in 'YYYY-MM-DD' format (optional)
            num_bars: Number of bars to retrieve (optional if start_date is provided)

        Returns:
            pd.DataFrame: Historical data or None if failed
        """
        # Use the standardized initializer to ensure MT5 is initialized
        # This is critical to prevent disabling Algo Trading
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            logger.info("Please check that Terminal 1 is running and accessible")
            logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
            return None

        # Convert timeframe string to MT5 timeframe constant
        timeframe_map = {
            'M1': mt5.TIMEFRAME_M1,
            'M5': mt5.TIMEFRAME_M5,
            'M15': mt5.TIMEFRAME_M15,
            'M30': mt5.TIMEFRAME_M30,
            'H1': mt5.TIMEFRAME_H1,
            'H4': mt5.TIMEFRAME_H4,
            'D1': mt5.TIMEFRAME_D1,
            'W1': mt5.TIMEFRAME_W1,
            'MN1': mt5.TIMEFRAME_MN1
        }

        if timeframe not in timeframe_map:
            logger.error(f"Invalid timeframe: {timeframe}")
            return None

        mt5_timeframe = timeframe_map[timeframe]

        # Get historical data
        if num_bars:
            # If num_bars is provided, we can use copy_rates_from_pos or copy_rates_from
            if start_date:
                # Convert start_date to timestamp
                from_date = pd.to_datetime(start_date)
                from_timestamp = int(from_date.timestamp())
                rates = mt5.copy_rates_from(symbol, mt5_timeframe, from_timestamp, num_bars)

                # Log the request details for debugging
                logger.info(f"Requested {num_bars} bars from {from_date} for {symbol} ({timeframe})")
            else:
                # Get the most recent bars
                rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, num_bars)
                logger.info(f"Requested {num_bars} most recent bars for {symbol} ({timeframe})")
        else:
            # If num_bars is not provided, we need start_date and optionally end_date
            if not start_date:
                logger.error("Either start_date or num_bars must be provided")
                return None

            # Convert dates to datetime with proper timezone handling
            from_date = pd.to_datetime(start_date)
            to_date = pd.to_datetime(end_date) if end_date else pd.Timestamp.now()

            # Ensure dates are timezone-naive
            if from_date.tzinfo is not None:
                from_date = from_date.replace(tzinfo=None)
            if to_date.tzinfo is not None:
                to_date = to_date.replace(tzinfo=None)

            # Calculate date range in days
            date_range_days = (to_date - from_date).days
            logger.info(f"Date range: {date_range_days} days from {from_date} to {to_date}")

            # If date range is too large, break it down into smaller chunks
            # MT5 has limitations on how much data can be retrieved at once
            max_days_per_request = 365  # Maximum days per request (adjust as needed)

            if date_range_days > max_days_per_request:
                logger.info(f"Date range too large, breaking down into chunks of {max_days_per_request} days")

                # Initialize empty list to store all rates
                all_rates = []
                current_date = from_date

                # Process each chunk
                while current_date < to_date:
                    # Calculate end date for this chunk
                    chunk_end_date = current_date + pd.Timedelta(days=max_days_per_request)
                    if chunk_end_date > to_date:
                        chunk_end_date = to_date

                    # Convert to timestamps
                    from_timestamp = int(current_date.timestamp())
                    to_timestamp = int(chunk_end_date.timestamp())

                    logger.info(f"Retrieving chunk: from={from_timestamp} ({current_date}), to={to_timestamp} ({chunk_end_date})")

                    # Get data for this chunk
                    chunk_rates = mt5.copy_rates_range(symbol, mt5_timeframe, from_timestamp, to_timestamp)

                    if chunk_rates is not None and len(chunk_rates) > 0:
                        logger.info(f"Retrieved {len(chunk_rates)} bars for chunk")
                        all_rates.extend(chunk_rates)
                    else:
                        logger.warning(f"No data retrieved for chunk from {current_date} to {chunk_end_date}")

                    # Move to next chunk
                    current_date = chunk_end_date

                # Use combined rates
                rates = np.array(all_rates) if all_rates else None

                if rates is not None:
                    logger.info(f"Combined {len(rates)} bars from all chunks")
                else:
                    logger.warning("No data retrieved from any chunk")
            else:
                # For smaller date ranges, use a single request
                # Convert datetime to MT5 format
                from_timestamp = int(from_date.timestamp())
                to_timestamp = int(to_date.timestamp())

                logger.info(f"Using timestamps: from={from_timestamp} ({from_date}), to={to_timestamp} ({to_date})")

                # Get historical data using date range
                rates = mt5.copy_rates_range(symbol, mt5_timeframe, from_timestamp, to_timestamp)

        if rates is None or len(rates) == 0:
            logger.error(f"Failed to get historical data for {symbol}: {mt5.last_error()}")
            return None

        # Convert to DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)

        logger.info(f"Retrieved {len(df)} bars for {symbol} ({timeframe})")
        return df

    def place_order(
        self,
        symbol: str,
        order_type: str,
        volume: float,
        price: float = None,
        sl: float = None,
        tp: float = None,
        comment: str = ""
    ) -> Optional[int]:
        """
        Place a trading order.
        Following the MT5 connection guide best practices:
        1. Never calls mt5.shutdown() which can disable Algo Trading
        2. Uses the global initializer to ensure MT5 is initialized only once
        3. Checks if Algo Trading is enabled before placing an order
        4. Provides detailed error information

        Args:
            symbol: Symbol to trade
            order_type: Order type ('BUY', 'SELL', 'BUY_LIMIT', 'SELL_LIMIT', 'BUY_STOP', 'SELL_STOP')
            volume: Order volume in lots
            price: Order price (required for limit and stop orders)
            sl: Stop loss price
            tp: Take profit price
            comment: Order comment

        Returns:
            int: Order ticket number or None if failed
        """
        # Use the standardized initializer to ensure MT5 is initialized
        # This is critical to prevent disabling Algo Trading
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            logger.info("Please check that Terminal 1 is running and accessible")
            logger.info("Refer to documentation/technical/mt5_connection_guide.md for detailed instructions")
            return None

        try:
            # Check if Algo Trading is enabled
            terminal_info = mt5.terminal_info()
            account_info = mt5.account_info()

            if terminal_info is None or account_info is None:
                logger.error("Failed to get terminal or account info")
                logger.error(f"MT5 error: {mt5.last_error()}")
                return None

            terminal_trade_allowed = terminal_info.trade_allowed if hasattr(terminal_info, 'trade_allowed') else False
            account_trade_expert = account_info.trade_expert if hasattr(account_info, 'trade_expert') else False
            algo_trading_enabled = terminal_trade_allowed and account_trade_expert

            if not algo_trading_enabled:
                logger.error("Cannot place order: Algo Trading is DISABLED")

                if not terminal_trade_allowed:
                    logger.error("The Algo Trading button is not enabled in the MT5 terminal")
                    logger.info("Please enable it by clicking the 'Algo Trading' button in the toolbar")

                if not account_trade_expert:
                    logger.error("Expert Advisors are not allowed to trade for this account")
                    logger.info("This might be due to an account change or server restrictions")

                logger.info("Please refer to mt5_connection_guide.md for detailed instructions")
                return None

            # Map order type to MT5 constants
            order_type_map = {
                'BUY': mt5.ORDER_TYPE_BUY,
                'SELL': mt5.ORDER_TYPE_SELL,
                'BUY_LIMIT': mt5.ORDER_TYPE_BUY_LIMIT,
                'SELL_LIMIT': mt5.ORDER_TYPE_SELL_LIMIT,
                'BUY_STOP': mt5.ORDER_TYPE_BUY_STOP,
                'SELL_STOP': mt5.ORDER_TYPE_SELL_STOP
            }

            if order_type not in order_type_map:
                logger.error(f"Invalid order type: {order_type}")
                logger.info(f"Valid order types are: {', '.join(order_type_map.keys())}")
                return None

            mt5_order_type = order_type_map[order_type]

            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {symbol}: {mt5.last_error()}")
                logger.info(f"Please check if the symbol {symbol} is available in the terminal")
                return None

            # Enable symbol for trading if needed
            if not symbol_info.visible:
                logger.info(f"Symbol {symbol} is not visible, attempting to select it...")
                if not mt5.symbol_select(symbol, True):
                    logger.error(f"Failed to select symbol {symbol}: {mt5.last_error()}")
                    return None
                logger.info(f"Symbol {symbol} selected successfully")

            # Check if symbol is available for trading
            if not symbol_info.trade_mode:
                logger.error(f"Symbol {symbol} is not available for trading")
                logger.info("This might be due to market hours or broker restrictions")
                return None

            # Get current price if not provided
            if price is None and mt5_order_type in [mt5.ORDER_TYPE_BUY, mt5.ORDER_TYPE_SELL]:
                symbol_tick = mt5.symbol_info_tick(symbol)
                if symbol_tick is None:
                    logger.error(f"Failed to get symbol tick for {symbol}: {mt5.last_error()}")
                    return None

                if mt5_order_type == mt5.ORDER_TYPE_BUY:
                    price = symbol_tick.ask
                    logger.info(f"Using current ask price for BUY order: {price}")
                else:
                    price = symbol_tick.bid
                    logger.info(f"Using current bid price for SELL order: {price}")

            # Validate volume
            if volume < symbol_info.volume_min:
                logger.error(f"Volume {volume} is less than minimum allowed {symbol_info.volume_min}")
                return None
            if volume > symbol_info.volume_max:
                logger.error(f"Volume {volume} is greater than maximum allowed {symbol_info.volume_max}")
                return None

            # Prepare order request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": mt5_order_type,
                "price": price,
                "sl": sl,
                "tp": tp,
                "deviation": 10,
                "magic": 123456,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # Log order details
            logger.info(f"Placing {order_type} order for {symbol} with volume {volume} at price {price}")
            if sl:
                logger.info(f"Stop Loss: {sl}")
            if tp:
                logger.info(f"Take Profit: {tp}")

            # Send order
            result = mt5.order_send(request)
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Failed to place order: {result.retcode}, {result.comment}")

                # Provide more detailed error information
                if result.retcode == mt5.TRADE_RETCODE_INVALID_PRICE:
                    logger.error("Invalid price. Please check the price and try again.")
                elif result.retcode == mt5.TRADE_RETCODE_INVALID_VOLUME:
                    logger.error(f"Invalid volume. Min: {symbol_info.volume_min}, Max: {symbol_info.volume_max}, Step: {symbol_info.volume_step}")
                elif result.retcode == mt5.TRADE_RETCODE_INVALID_STOPS:
                    logger.error("Invalid stop loss or take profit. Please check the values and try again.")
                elif result.retcode == mt5.TRADE_RETCODE_TRADE_DISABLED:
                    logger.error("Trading is disabled. Please check if Algo Trading is enabled.")
                elif result.retcode == mt5.TRADE_RETCODE_MARKET_CLOSED:
                    logger.error("Market is closed. Please try again during market hours.")
                elif result.retcode == mt5.TRADE_RETCODE_NO_MONEY:
                    logger.error("Insufficient funds to place the order.")
                elif result.retcode == mt5.TRADE_RETCODE_PRICE_CHANGED:
                    logger.error("Price changed. Please try again with the current price.")
                elif result.retcode == mt5.TRADE_RETCODE_PRICE_OFF:
                    logger.error("No quotes to process the request. Please try again later.")
                elif result.retcode == mt5.TRADE_RETCODE_INVALID_EXPIRATION:
                    logger.error("Invalid expiration date. Please check the expiration and try again.")
                elif result.retcode == mt5.TRADE_RETCODE_ORDER_LOCKED:
                    logger.error("Order is locked and cannot be modified. Please try again later.")
                elif result.retcode == mt5.TRADE_RETCODE_CLOSE_ORDER_EXIST:
                    logger.error("Order to close this position already exists. Please cancel it first.")

                return None

            logger.info(f"Order placed successfully: Ticket #{result.order}")
            logger.info(f"Order details: {order_type} {symbol} {volume} lots at {price}")
            return result.order

        except Exception as e:
            logger.error(f"Error placing order: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def modify_order(
        self,
        ticket: int,
        sl: float = None,
        tp: float = None
    ) -> bool:
        """
        Modify an existing order.

        Args:
            ticket: Order ticket number
            sl: New stop loss price
            tp: New take profit price

        Returns:
            bool: True if modification is successful, False otherwise
        """
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            return False

        # Get order info
        position = mt5.positions_get(ticket=ticket)
        if position is None or len(position) == 0:
            logger.error(f"Failed to get position {ticket}: {mt5.last_error()}")
            return False

        position = position[0]

        # Prepare modification request
        request = {
            "action": mt5.TRADE_ACTION_SLTP,
            "symbol": position.symbol,
            "position": ticket,
            "sl": sl if sl is not None else position.sl,
            "tp": tp if tp is not None else position.tp,
        }

        # Send modification request
        result = mt5.order_send(request)
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.error(f"Failed to modify order: {result.retcode}, {result.comment}")
            return False

        logger.info(f"Order {ticket} modified successfully")
        return True

    def close_order(self, ticket: int) -> bool:
        """
        Close an existing order.

        Args:
            ticket: Order ticket number

        Returns:
            bool: True if closing is successful, False otherwise
        """
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            return False

        # Get position info
        position = mt5.positions_get(ticket=ticket)
        if position is None or len(position) == 0:
            logger.error(f"Failed to get position {ticket}: {mt5.last_error()}")
            return False

        position = position[0]

        # Determine order type for closing
        order_type = mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
        price = mt5.symbol_info_tick(position.symbol).bid if position.type == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(position.symbol).ask

        # Prepare close request
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": order_type,
            "position": ticket,
            "price": price,
            "deviation": 10,
            "magic": 123456,
            "comment": "Close position",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # Send close request
        result = mt5.order_send(request)
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.error(f"Failed to close order: {result.retcode}, {result.comment}")
            return False

        logger.info(f"Order {ticket} closed successfully")
        return True

    def get_open_positions(self, symbol: str = None) -> Optional[List[Dict]]:
        """
        Get all open positions.

        Args:
            symbol: Symbol to filter positions (optional)

        Returns:
            List[Dict]: List of open positions or None if failed
        """
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            return None

        # Get positions
        if symbol:
            positions = mt5.positions_get(symbol=symbol)
        else:
            positions = mt5.positions_get()

        if positions is None:
            logger.error(f"Failed to get positions: {mt5.last_error()}")
            return None

        # Convert positions to list of dictionaries
        positions_list = []
        for position in positions:
            position_dict = {
                'ticket': position.ticket,
                'symbol': position.symbol,
                'type': 'BUY' if position.type == mt5.ORDER_TYPE_BUY else 'SELL',
                'volume': position.volume,
                'open_price': position.price_open,
                'current_price': position.price_current,
                'sl': position.sl,
                'tp': position.tp,
                'profit': position.profit,
                'swap': position.swap,
                'time': pd.to_datetime(position.time, unit='s')
            }
            positions_list.append(position_dict)

        return positions_list

    def get_orders(self, symbol: str = None) -> Optional[List[Dict]]:
        """
        Get all pending orders.

        Args:
            symbol: Symbol to filter orders (optional)

        Returns:
            List[Dict]: List of pending orders or None if failed
        """
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            return None

        # Get orders
        if symbol:
            orders = mt5.orders_get(symbol=symbol)
        else:
            orders = mt5.orders_get()

        if orders is None:
            logger.error(f"Failed to get orders: {mt5.last_error()}")
            return None

        # Convert orders to list of dictionaries
        orders_list = []
        for order in orders:
            order_dict = {
                'ticket': order.ticket,
                'symbol': order.symbol,
                'type': order.type,
                'volume': order.volume_current,
                'price': order.price_open,
                'sl': order.sl,
                'tp': order.tp,
                'time_setup': pd.to_datetime(order.time_setup, unit='s')
            }
            orders_list.append(order_dict)

        return orders_list

    def get_account_history(
        self,
        from_date: str,
        to_date: str = None
    ) -> Optional[pd.DataFrame]:
        """
        Get account history.

        Args:
            from_date: Start date in 'YYYY-MM-DD' format
            to_date: End date in 'YYYY-MM-DD' format (optional)

        Returns:
            pd.DataFrame: Account history or None if failed
        """
        if not initialize_mt5_once():
            logger.error("Failed to initialize MT5")
            return None

        # Convert dates to datetime
        from_dt = pd.to_datetime(from_date)
        to_dt = pd.to_datetime(to_date) if to_date else pd.Timestamp.now()

        # Convert datetime to MT5 format
        from_timestamp = int(from_dt.timestamp())
        to_timestamp = int(to_dt.timestamp())

        # Get history
        history = mt5.history_deals_get(from_timestamp, to_timestamp)
        if history is None:
            logger.error(f"Failed to get history: {mt5.last_error()}")
            return None

        # Convert to DataFrame
        if len(history) > 0:
            df = pd.DataFrame(list(history), columns=history[0]._asdict().keys())
            df['time'] = pd.to_datetime(df['time'], unit='s')
            return df
        else:
            return pd.DataFrame()

    def check_connection(self) -> bool:
        """
        Check if MT5 is connected and initialized.
        This method follows the MT5 connection guide best practices by not calling mt5.shutdown().

        Returns:
            bool: True if MT5 is connected, False otherwise
        """
        try:
            # First, check if MT5 is initialized using the global initializer
            if not initialize_mt5_once():
                logger.error("Failed to initialize MT5")
                logger.info("Please check that Terminal 1 is running and accessible")
                logger.info("Refer to mt5_connection_guide.md for detailed instructions")
                return False

            # Check if terminal info is available
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                logger.error("Failed to get terminal info")
                logger.error(f"MT5 error: {mt5.last_error()}")
                return False

            # Check if terminal is connected
            if not terminal_info.connected:
                logger.error("Terminal is not connected to the server")
                logger.info("Please check your internet connection and make sure the terminal is connected")
                return False

            # Check if account info is available
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("Failed to get account info")
                logger.error(f"MT5 error: {mt5.last_error()}")
                logger.info("This might indicate that the terminal is not logged in or the connection to the server is lost")
                return False

            # Check if Algo Trading is enabled
            terminal_trade_allowed = terminal_info.trade_allowed if hasattr(terminal_info, 'trade_allowed') else False
            account_trade_expert = account_info.trade_expert if hasattr(account_info, 'trade_expert') else False
            algo_trading_enabled = terminal_trade_allowed and account_trade_expert

            if not algo_trading_enabled:
                logger.warning("Algo Trading is DISABLED")

                if not terminal_trade_allowed:
                    logger.warning("The Algo Trading button is not enabled in the MT5 terminal")
                    logger.info("Please enable it by clicking the 'Algo Trading' button in the toolbar")

                if not account_trade_expert:
                    logger.warning("Expert Advisors are not allowed to trade for this account")
                    logger.info("This might be due to an account change or server restrictions")

                logger.info("Please refer to mt5_connection_guide.md for detailed instructions")
            else:
                logger.info("Algo Trading is ENABLED")

            return True

        except Exception as e:
            logger.error(f"Error checking MT5 connection: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def check_algo_trading_status(self, terminal_id: int) -> Optional[Dict]:
        """
        Check if Algo Trading is enabled for a specific terminal without connecting to it.
        This method is safer than connect_terminal as it doesn't change the active terminal,
        which can sometimes disable Algo Trading in other terminals.

        Args:
            terminal_id: Terminal ID to check

        Returns:
            Dict with algo_trading_enabled status or None if check fails
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal ID {terminal_id} not found in configuration")
            return None

        # Get terminal info without changing the active terminal
        terminal_info = None
        account_info = None

        # Use the connection manager to safely check the terminal
        terminal_info = self.connection_manager.check_terminal_info(terminal_id)
        if terminal_info:
            # Check if Algo Trading is enabled
            terminal_trade_allowed = terminal_info.trade_allowed if hasattr(terminal_info, 'trade_allowed') else False

            # Get account info if terminal info is available
            account_info = self.connection_manager.check_account_info(terminal_id)
            account_trade_expert = False
            if account_info and hasattr(account_info, 'trade_expert'):
                account_trade_expert = account_info.trade_expert

            # Determine if Algo Trading is enabled
            algo_trading_enabled = terminal_trade_allowed and account_trade_expert

            return {
                'terminal_id': terminal_id,
                'terminal_trade_allowed': terminal_trade_allowed,
                'account_trade_expert': account_trade_expert,
                'algo_trading_enabled': algo_trading_enabled
            }

        return None

    def __del__(self):
        """Clean up resources."""
        # We don't call mt5.shutdown() to ensure Algo Trading remains enabled
        # Just clear our internal state
        self.connected_terminals = {}
        self.active_terminal_id = None
