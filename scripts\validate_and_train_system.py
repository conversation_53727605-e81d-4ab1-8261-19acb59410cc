"""
Comprehensive System Validation and Training Script.
This script validates the entire system and trains all models systematically.

Usage:
    python scripts/validate_and_train_system.py [options]

Options:
    --symbol BTCUSD.a        Symbol to process (default: BTCUSD.a)
    --years 5                Years of historical data to collect (default: 5)
    --force-retrain          Force retraining of existing models
    --validate-only          Only validate, don't train models
    --skip-data-collection   Skip data collection step
"""
import argparse
import logging
import sys
import os
from datetime import datetime, timedelta
# Add the parent directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import required modules
from data.data_collector import DataCollector
from data.data_manager import DataManager
from data.validation.data_validator import DataValidator
from model.model_manager import ModelManager
from utils.path_utils import (
    get_model_path,
    get_metrics_path,
    VALID_TIMEFRAMES,
    VALID_TERMINAL_IDS,
    TERMINAL_MODEL_TYPES
)
from utils.mt5_initializer import initialize_all_terminals
from trading.mt5_connector import MT5Connector

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system_validation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemValidator:
    """Comprehensive system validator and trainer."""
    
    def __init__(self):
        """Initialize the system validator."""
        self.mt5_connector = None
        self.data_collector = None
        self.data_manager = None
        self.data_validator = None
        self.model_manager = None
        self.validation_results = {}
        
    def initialize_components(self) -> bool:
        """Initialize all system components."""
        try:
            logger.info("Initializing system components...")
            
            # Initialize MT5 connector
            self.mt5_connector = MT5Connector()
            logger.info("[SUCCESS] MT5 Connector initialized")

            # Initialize data components
            self.data_collector = DataCollector(self.mt5_connector)
            self.data_manager = DataManager()
            self.data_validator = DataValidator()
            logger.info("[SUCCESS] Data components initialized")

            # Initialize model manager
            self.model_manager = ModelManager()
            logger.info("[SUCCESS] Model Manager initialized")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            return False
    
    def validate_mt5_terminals(self) -> bool:
        """Validate all MT5 terminals."""
        logger.info("\n=== VALIDATING MT5 TERMINALS ===")
        
        try:
            # Initialize all terminals
            terminal_status = initialize_all_terminals(max_retries=3, retry_delay=5)
            
            success_count = 0
            for terminal_id, status in terminal_status.items():
                if status['initialized'] and status['algo_trading_enabled']:
                    logger.info(f"[SUCCESS] Terminal {terminal_id}: READY")
                    success_count += 1
                else:
                    logger.error(f"[FAILED] Terminal {terminal_id}: FAILED")
            
            self.validation_results['terminals'] = {
                'total': len(terminal_status),
                'successful': success_count,
                'status': terminal_status
            }
            
            if success_count == len(terminal_status):
                logger.info(f"[SUCCESS] All {success_count} terminals validated successfully")
                return True
            else:
                logger.warning(f"[WARNING] Only {success_count}/{len(terminal_status)} terminals validated")
                return success_count > 0
                
        except Exception as e:
            logger.error(f"Terminal validation failed: {str(e)}")
            return False
    
    def validate_data_structure(self) -> bool:
        """Validate data directory structure."""
        logger.info("\n=== VALIDATING DATA STRUCTURE ===")
        
        try:
            required_dirs = [
                "data/storage/historical",
                "data/storage/features",
                "model/saved_models/arima",
                "model/saved_models/lstm", 
                "model/saved_models/tft",
                "model/saved_models/ensemble",
                "model/visualizations"
            ]
            
            for dir_path in required_dirs:
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
                    logger.info(f"[CREATED] Directory: {dir_path}")
                else:
                    logger.info(f"[EXISTS] Directory: {dir_path}")
            
            # Validate terminal-specific directories
            for terminal_id in VALID_TERMINAL_IDS:
                terminal_dir = f"data/storage/historical/terminal_{terminal_id}"
                if not os.path.exists(terminal_dir):
                    os.makedirs(terminal_dir, exist_ok=True)
                    logger.info(f"[CREATED] Terminal directory: {terminal_dir}")

            self.validation_results['data_structure'] = {
                'required_dirs': len(required_dirs),
                'created_dirs': len(required_dirs),
                'status': 'success'
            }

            logger.info("[SUCCESS] Data structure validation completed")
            return True
            
        except Exception as e:
            logger.error(f"Data structure validation failed: {str(e)}")
            return False
    
    def collect_historical_data(self, symbol: str, years: int) -> bool:
        """Collect historical data for all terminals and timeframes."""
        logger.info(f"\n=== COLLECTING HISTORICAL DATA FOR {symbol} ===")
        
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years * 365)
            
            logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"Timeframes: {VALID_TIMEFRAMES}")
            logger.info(f"Terminals: {VALID_TERMINAL_IDS}")
            
            # Collect data for each terminal and timeframe
            collection_results = {}
            total_collections = len(VALID_TERMINAL_IDS) * len(VALID_TIMEFRAMES)
            successful_collections = 0
            
            for terminal_id in VALID_TERMINAL_IDS:
                terminal_results = {}
                
                for timeframe in VALID_TIMEFRAMES:
                    try:
                        logger.info(f"Collecting {symbol} {timeframe} data for Terminal {terminal_id}...")
                        
                        # Collect data for this terminal and timeframe
                        df = self.data_collector.download_historical_data(
                            symbol=symbol,
                            timeframes=[timeframe],
                            start_date=start_date,
                            end_date=end_date,
                            terminal_ids=[terminal_id],
                            save=True,
                            years_to_collect=years
                        )
                        
                        if df and timeframe in df and df[timeframe] is not None and len(df[timeframe]) > 0:
                            data_count = len(df[timeframe])
                            logger.info(f"[SUCCESS] Terminal {terminal_id} {timeframe}: {data_count} bars collected")
                            terminal_results[timeframe] = {'status': 'success', 'count': data_count}
                            successful_collections += 1
                        else:
                            logger.warning(f"[WARNING] Terminal {terminal_id} {timeframe}: No data collected")
                            terminal_results[timeframe] = {'status': 'failed', 'count': 0}

                    except Exception as e:
                        logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: {str(e)}")
                        terminal_results[timeframe] = {'status': 'error', 'count': 0}
                
                collection_results[terminal_id] = terminal_results
            
            self.validation_results['data_collection'] = {
                'total_collections': total_collections,
                'successful_collections': successful_collections,
                'results': collection_results
            }
            
            success_rate = successful_collections / total_collections
            logger.info(f"Data collection completed: {successful_collections}/{total_collections} ({success_rate:.1%})")
            
            return success_rate >= 0.8  # Require at least 80% success rate
            
        except Exception as e:
            logger.error(f"Data collection failed: {str(e)}")
            return False
    
    def validate_collected_data(self, symbol: str) -> bool:
        """Validate the quality of collected data."""
        logger.info(f"\n=== VALIDATING DATA QUALITY FOR {symbol} ===")
        
        try:
            validation_results = {}
            total_validations = 0
            successful_validations = 0
            
            for terminal_id in VALID_TERMINAL_IDS:
                for timeframe in VALID_TIMEFRAMES:
                    try:
                        # Look for existing data files for this terminal/timeframe
                        import glob
                        pattern = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/*.parquet"
                        files = glob.glob(pattern)

                        # Filter out metadata files and find actual data files
                        data_files = [f for f in files if not f.endswith('_metadata.json') and f.endswith('.parquet')]

                        if not data_files:
                            logger.warning(f"No data file found for Terminal {terminal_id} {timeframe}")
                            continue

                        # Use the most recent file (or latest.parquet if it exists)
                        latest_file = None
                        for f in data_files:
                            if 'latest.parquet' in f:
                                latest_file = f
                                break

                        if latest_file:
                            data_path = latest_file
                        else:
                            # Sort by modification time and use the newest
                            data_files.sort(key=os.path.getmtime, reverse=True)
                            data_path = data_files[0]

                        logger.info(f"Found data file for Terminal {terminal_id} {timeframe}: {os.path.basename(data_path)}")
                        
                        # Load and validate data
                        import pandas as pd
                        df = pd.read_parquet(data_path)
                        
                        # Run validation checks
                        validation_results = self.data_validator.validate_data(df)

                        total_validations += 1

                        # Check if all validation rules passed
                        all_passed = True
                        issues = []

                        for rule_name, result in validation_results.items():
                            if result.get('status') != 'pass':
                                all_passed = False
                                issues.append(f"{rule_name}: {result.get('message', 'Unknown error')}")

                        if all_passed:
                            successful_validations += 1
                            logger.info(f"[OK] Terminal {terminal_id} {timeframe}: Data quality OK ({len(df)} bars)")
                        else:
                            logger.warning(f"[WARNING] Terminal {terminal_id} {timeframe}: Data quality issues")
                            for issue in issues:
                                logger.warning(f"  - {issue}")
                        
                        # Store validation results for this terminal/timeframe
                        # validation_results[f"terminal_{terminal_id}_{timeframe}"] = validation_results
                        
                    except Exception as e:
                        logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: Validation error - {str(e)}")
                        total_validations += 1
            
            self.validation_results['data_quality'] = {
                'total_validations': total_validations,
                'successful_validations': successful_validations,
                'results': validation_results
            }
            
            if total_validations > 0:
                success_rate = successful_validations / total_validations
                logger.info(f"Data quality validation: {successful_validations}/{total_validations} ({success_rate:.1%})")
                return success_rate >= 0.8
            else:
                logger.warning("No data found for validation")
                return False
                
        except Exception as e:
            logger.error(f"Data quality validation failed: {str(e)}")
            return False

    def train_all_models(self, symbol: str, force_retrain: bool = False) -> bool:
        """Train all models for all terminals and timeframes."""
        logger.info(f"\n=== TRAINING MODELS FOR {symbol} ===")

        try:
            training_results = {}
            total_models = len(VALID_TERMINAL_IDS) * len(VALID_TIMEFRAMES)
            successful_models = 0

            # Train models for each terminal
            for terminal_id in VALID_TERMINAL_IDS:
                model_type = TERMINAL_MODEL_TYPES[terminal_id]
                logger.info(f"\nTraining {model_type.upper()} models for Terminal {terminal_id}...")

                terminal_results = {}

                for timeframe in VALID_TIMEFRAMES:
                    try:
                        logger.info(f"Training {symbol} {timeframe} {model_type} model...")

                        # Check if model already exists
                        model_path = get_model_path(symbol, timeframe, model_type, terminal_id)
                        if os.path.exists(model_path) and not force_retrain:
                            logger.info(f"[EXISTS] Model already exists: {model_path}")
                            terminal_results[timeframe] = {'status': 'exists', 'path': model_path}
                            successful_models += 1
                            continue

                        # Train the model using model manager
                        success = self.model_manager.initialize_terminal_models(
                            symbol=symbol,
                            timeframes=[timeframe],
                            terminals=[terminal_id],
                            force_retrain=force_retrain
                        )

                        if success and terminal_id in success and timeframe in success[terminal_id] and success[terminal_id][timeframe]:
                            logger.info(f"[SUCCESS] Terminal {terminal_id} {timeframe}: Model trained successfully")
                            terminal_results[timeframe] = {'status': 'success', 'path': model_path}
                            successful_models += 1
                        else:
                            logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: Model training failed")
                            terminal_results[timeframe] = {'status': 'failed', 'path': None}

                    except Exception as e:
                        logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: Training error - {str(e)}")
                        terminal_results[timeframe] = {'status': 'error', 'path': None}

                training_results[terminal_id] = terminal_results

            self.validation_results['model_training'] = {
                'total_models': total_models,
                'successful_models': successful_models,
                'results': training_results
            }

            success_rate = successful_models / total_models
            logger.info(f"Model training completed: {successful_models}/{total_models} ({success_rate:.1%})")

            return success_rate >= 0.8  # Require at least 80% success rate

        except Exception as e:
            logger.error(f"Model training failed: {str(e)}")
            return False

    def validate_trained_models(self, symbol: str) -> bool:
        """Validate that all trained models are working correctly."""
        logger.info(f"\n=== VALIDATING TRAINED MODELS FOR {symbol} ===")

        try:
            validation_results = {}
            total_models = 0
            valid_models = 0

            for terminal_id in VALID_TERMINAL_IDS:
                model_type = TERMINAL_MODEL_TYPES[terminal_id]
                terminal_results = {}

                for timeframe in VALID_TIMEFRAMES:
                    try:
                        # Check if model file exists
                        model_path = get_model_path(symbol, timeframe, model_type, terminal_id)
                        metrics_path = get_metrics_path(symbol, timeframe, model_type, terminal_id)

                        total_models += 1

                        if not os.path.exists(model_path):
                            logger.warning(f"⚠ Terminal {terminal_id} {timeframe}: Model file not found")
                            terminal_results[timeframe] = {'status': 'missing', 'path': model_path}
                            continue

                        # Check model file size
                        model_size = os.path.getsize(model_path)
                        if model_size < 1024:  # Less than 1KB is suspicious
                            logger.warning(f"⚠ Terminal {terminal_id} {timeframe}: Model file too small ({model_size} bytes)")
                            terminal_results[timeframe] = {'status': 'invalid_size', 'size': model_size}
                            continue

                        # Check if metrics file exists
                        has_metrics = os.path.exists(metrics_path)

                        # Try to load the model to verify it's valid
                        try:
                            model_loaded = False
                            if model_type == 'arima':
                                import pickle
                                with open(model_path, 'rb') as f:
                                    model = pickle.load(f)
                                    model_loaded = model is not None
                            elif model_type in ['lstm', 'tft']:
                                import torch
                                model = torch.load(model_path, map_location='cpu')
                                model_loaded = model is not None
                            elif model_type in ['lstm_arima', 'tft_arima']:
                                import pickle
                                with open(model_path, 'rb') as f:
                                    model = pickle.load(f)
                                    model_loaded = model is not None

                            if model_loaded:
                                logger.info(f"[SUCCESS] Terminal {terminal_id} {timeframe}: Model validated")
                                terminal_results[timeframe] = {
                                    'status': 'valid',
                                    'path': model_path,
                                    'size': model_size,
                                    'has_metrics': has_metrics
                                }
                                valid_models += 1
                            else:
                                logger.warning(f"[WARNING] Terminal {terminal_id} {timeframe}: Model loaded but is None")
                                terminal_results[timeframe] = {'status': 'invalid_model', 'path': model_path}

                        except Exception as load_error:
                            logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: Model load error - {str(load_error)}")
                            terminal_results[timeframe] = {'status': 'load_error', 'error': str(load_error)}

                    except Exception as e:
                        logger.error(f"[FAILED] Terminal {terminal_id} {timeframe}: Validation error - {str(e)}")
                        terminal_results[timeframe] = {'status': 'error', 'error': str(e)}

                validation_results[terminal_id] = terminal_results

            self.validation_results['model_validation'] = {
                'total_models': total_models,
                'valid_models': valid_models,
                'results': validation_results
            }

            if total_models > 0:
                success_rate = valid_models / total_models
                logger.info(f"Model validation: {valid_models}/{total_models} ({success_rate:.1%})")
                return success_rate >= 0.8
            else:
                logger.warning("No models found for validation")
                return False

        except Exception as e:
            logger.error(f"Model validation failed: {str(e)}")
            return False

    def generate_summary_report(self) -> None:
        """Generate a comprehensive summary report."""
        logger.info("\n" + "="*80)
        logger.info("SYSTEM VALIDATION AND TRAINING SUMMARY REPORT")
        logger.info("="*80)

        # Terminal validation summary
        if 'terminals' in self.validation_results:
            terminal_data = self.validation_results['terminals']
            logger.info(f"\n📡 MT5 TERMINALS: {terminal_data['successful']}/{terminal_data['total']} validated")

        # Data collection summary
        if 'data_collection' in self.validation_results:
            data_data = self.validation_results['data_collection']
            success_rate = data_data['successful_collections'] / data_data['total_collections']
            logger.info(f"📊 DATA COLLECTION: {data_data['successful_collections']}/{data_data['total_collections']} ({success_rate:.1%})")

        # Data quality summary
        if 'data_quality' in self.validation_results:
            quality_data = self.validation_results['data_quality']
            success_rate = quality_data['successful_validations'] / quality_data['total_validations']
            logger.info(f"✅ DATA QUALITY: {quality_data['successful_validations']}/{quality_data['total_validations']} ({success_rate:.1%})")

        # Model training summary
        if 'model_training' in self.validation_results:
            training_data = self.validation_results['model_training']
            success_rate = training_data['successful_models'] / training_data['total_models']
            logger.info(f"🤖 MODEL TRAINING: {training_data['successful_models']}/{training_data['total_models']} ({success_rate:.1%})")

        # Model validation summary
        if 'model_validation' in self.validation_results:
            validation_data = self.validation_results['model_validation']
            success_rate = validation_data['valid_models'] / validation_data['total_models']
            logger.info(f"🔍 MODEL VALIDATION: {validation_data['valid_models']}/{validation_data['total_models']} ({success_rate:.1%})")

        # Overall system status
        logger.info("\n" + "="*80)
        all_passed = all([
            self.validation_results.get('terminals', {}).get('successful', 0) >= 4,  # At least 4 terminals
            self.validation_results.get('data_collection', {}).get('successful_collections', 0) > 0,
            self.validation_results.get('model_training', {}).get('successful_models', 0) > 0
        ])

        if all_passed:
            logger.info("[SUCCESS] SYSTEM STATUS: READY FOR TRADING")
            logger.info("\nNext steps:")
            logger.info("1. Start the trading bot:")
            logger.info("   python run_trading_bot.py --multi-terminal")
            logger.info("2. Monitor performance:")
            logger.info("   python scripts/run_dashboard.py")
        else:
            logger.info("[WARNING] SYSTEM STATUS: REQUIRES ATTENTION")
            logger.info("\nPlease review the validation results above and fix any issues.")

        logger.info("="*80)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Comprehensive system validation and training')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Symbol to process')
    parser.add_argument('--years', type=int, default=5, help='Years of historical data to collect')
    parser.add_argument('--force-retrain', action='store_true', help='Force retraining of existing models')
    parser.add_argument('--validate-only', action='store_true', help='Only validate, don\'t train models')
    parser.add_argument('--skip-data-collection', action='store_true', help='Skip data collection step')

    args = parser.parse_args()

    logger.info("Starting comprehensive system validation and training...")
    logger.info(f"Symbol: {args.symbol}")
    logger.info(f"Years of data: {args.years}")
    logger.info(f"Force retrain: {args.force_retrain}")
    logger.info(f"Validate only: {args.validate_only}")
    logger.info(f"Skip data collection: {args.skip_data_collection}")

    # Initialize validator
    validator = SystemValidator()

    # Step 1: Initialize components
    if not validator.initialize_components():
        logger.error("Failed to initialize system components")
        return 1

    # Step 2: Validate MT5 terminals
    if not validator.validate_mt5_terminals():
        logger.error("MT5 terminal validation failed")
        return 1

    # Step 3: Validate data structure
    if not validator.validate_data_structure():
        logger.error("Data structure validation failed")
        return 1

    # Step 4: Collect historical data (unless skipped)
    if not args.skip_data_collection:
        if not validator.collect_historical_data(args.symbol, args.years):
            logger.error("Historical data collection failed")
            return 1

    # Step 5: Validate collected data
    if not validator.validate_collected_data(args.symbol):
        logger.warning("Data quality validation had issues, but continuing...")

    # Step 6: Train models (unless validate-only)
    if not args.validate_only:
        if not validator.train_all_models(args.symbol, args.force_retrain):
            logger.error("Model training failed")
            return 1

    # Step 7: Validate trained models
    if not validator.validate_trained_models(args.symbol):
        logger.warning("Model validation had issues, but continuing...")

    # Step 8: Generate summary report
    validator.generate_summary_report()

    logger.info("System validation and training completed successfully!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
