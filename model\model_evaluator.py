"""
Model Evaluator Module.
This module handles evaluation and performance monitoring of the LSTM model.
"""
import logging
import time
import os
from typing import Dict, List, Optional, Tuple, Union, Any
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from config.trading_config import TRADING_CONFIG

# Configure logger
logger = logging.getLogger('model.evaluator')

class ModelEvaluator:
    """
    Model Evaluator class for evaluating model performance and detecting model decay.
    """
    def __init__(self):
        """Initialize ModelEvaluator."""
        self.model_decay_config = TRADING_CONFIG['model_decay']
        self.performance_window = self.model_decay_config['performance_window']
        self.min_accuracy = self.model_decay_config['min_accuracy']
        self.min_profit_factor = self.model_decay_config['min_profit_factor']
        self.max_consecutive_losses = self.model_decay_config['max_consecutive_losses']
        
        # Initialize performance metrics
        self.reset_metrics()
    
    def reset_metrics(self):
        """Reset performance metrics."""
        self.predictions = []
        self.actual_values = []
        self.trade_results = []
        self.consecutive_losses = 0
        self.performance_history = {
            'mse': [],
            'rmse': [],
            'mae': [],
            'r2': [],
            'direction_accuracy': [],
            'profit_factor': [],
            'win_rate': [],
            'expectancy': []
        }
    
    def add_prediction(self, prediction: float, actual: float, trade_result: float = None):
        """
        Add a prediction and actual value to the performance metrics.
        
        Args:
            prediction: Predicted value
            actual: Actual value
            trade_result: Trade result (profit/loss) if available
        """
        self.predictions.append(prediction)
        self.actual_values.append(actual)
        
        if trade_result is not None:
            self.trade_results.append(trade_result)
            
            # Update consecutive losses
            if trade_result < 0:
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0
    
    def calculate_metrics(self) -> Dict[str, float]:
        """
        Calculate performance metrics.
        
        Returns:
            Dict[str, float]: Performance metrics
        """
        if len(self.predictions) == 0:
            logger.warning("No predictions available for calculating metrics")
            return {}
        
        # Convert to numpy arrays
        y_pred = np.array(self.predictions)
        y_true = np.array(self.actual_values)
        
        # Calculate regression metrics
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        
        # Calculate R-squared (coefficient of determination)
        r2 = r2_score(y_true, y_pred) if len(y_true) > 1 else 0.0
        
        # Calculate directional accuracy
        direction_correct = np.sum((y_pred > 0) == (y_true > 0))
        direction_accuracy = direction_correct / len(y_true)
        
        # Calculate trading metrics if trade results are available
        profit_factor = 0.0
        win_rate = 0.0
        expectancy = 0.0
        
        if len(self.trade_results) > 0:
            # Calculate win rate
            wins = sum(1 for result in self.trade_results if result > 0)
            win_rate = wins / len(self.trade_results)
            
            # Calculate profit factor
            gross_profit = sum(result for result in self.trade_results if result > 0)
            gross_loss = abs(sum(result for result in self.trade_results if result < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Calculate expectancy
            expectancy = sum(self.trade_results) / len(self.trade_results)
        
        # Save metrics to history
        self.performance_history['mse'].append(mse)
        self.performance_history['rmse'].append(rmse)
        self.performance_history['mae'].append(mae)
        self.performance_history['r2'].append(r2)
        self.performance_history['direction_accuracy'].append(direction_accuracy)
        self.performance_history['profit_factor'].append(profit_factor)
        self.performance_history['win_rate'].append(win_rate)
        self.performance_history['expectancy'].append(expectancy)
        
        return {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'direction_accuracy': direction_accuracy,
            'profit_factor': profit_factor,
            'win_rate': win_rate,
            'expectancy': expectancy,
            'consecutive_losses': self.consecutive_losses
        }
    
    def detect_model_decay(self) -> Tuple[bool, str]:
        """
        Detect model decay based on performance metrics.
        
        Returns:
            Tuple[bool, str]: (decay_detected, reason)
        """
        # Check if we have enough data
        if len(self.predictions) < self.performance_window:
            return False, "Not enough data for decay detection"
        
        # Calculate metrics
        metrics = self.calculate_metrics()
        
        # Check direction accuracy
        if metrics['direction_accuracy'] < self.min_accuracy:
            return True, f"Direction accuracy ({metrics['direction_accuracy']:.2f}) below threshold ({self.min_accuracy:.2f})"
        
        # Check profit factor
        if metrics['profit_factor'] < self.min_profit_factor:
            return True, f"Profit factor ({metrics['profit_factor']:.2f}) below threshold ({self.min_profit_factor:.2f})"
        
        # Check consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            return True, f"Consecutive losses ({self.consecutive_losses}) reached threshold ({self.max_consecutive_losses})"
        
        # Check for trend in direction accuracy
        if len(self.performance_history['direction_accuracy']) >= 3:
            recent_accuracy = self.performance_history['direction_accuracy'][-3:]
            if all(x > y for x, y in zip(recent_accuracy, recent_accuracy[1:])):
                return True, "Declining trend in direction accuracy"
        
        return False, "No model decay detected"
    
    def plot_metrics(self, save_path: str = None):
        """
        Plot performance metrics.
        
        Args:
            save_path: Path to save the plot
        """
        if len(self.performance_history['mse']) == 0:
            logger.warning("No metrics available for plotting")
            return
        
        # Create figure with subplots
        fig, axs = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot regression metrics
        axs[0, 0].plot(self.performance_history['rmse'], label='RMSE')
        axs[0, 0].plot(self.performance_history['mae'], label='MAE')
        axs[0, 0].set_title('Regression Metrics')
        axs[0, 0].set_xlabel('Evaluation')
        axs[0, 0].set_ylabel('Error')
        axs[0, 0].legend()
        axs[0, 0].grid(True)
        
        # Plot R-squared
        axs[0, 1].plot(self.performance_history['r2'], label='R²')
        axs[0, 1].set_title('R-squared')
        axs[0, 1].set_xlabel('Evaluation')
        axs[0, 1].set_ylabel('R²')
        axs[0, 1].legend()
        axs[0, 1].grid(True)
        
        # Plot direction accuracy
        axs[1, 0].plot(self.performance_history['direction_accuracy'], label='Direction Accuracy')
        axs[1, 0].axhline(y=self.min_accuracy, color='r', linestyle='--', label=f'Min Accuracy ({self.min_accuracy:.2f})')
        axs[1, 0].set_title('Direction Accuracy')
        axs[1, 0].set_xlabel('Evaluation')
        axs[1, 0].set_ylabel('Accuracy')
        axs[1, 0].legend()
        axs[1, 0].grid(True)
        
        # Plot trading metrics
        axs[1, 1].plot(self.performance_history['profit_factor'], label='Profit Factor')
        axs[1, 1].plot(self.performance_history['win_rate'], label='Win Rate')
        axs[1, 1].plot(self.performance_history['expectancy'], label='Expectancy')
        axs[1, 1].axhline(y=self.min_profit_factor, color='r', linestyle='--', label=f'Min Profit Factor ({self.min_profit_factor:.2f})')
        axs[1, 1].set_title('Trading Metrics')
        axs[1, 1].set_xlabel('Evaluation')
        axs[1, 1].set_ylabel('Value')
        axs[1, 1].legend()
        axs[1, 1].grid(True)
        
        # Adjust layout
        plt.tight_layout()
        
        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Metrics plot saved to {save_path}")
        else:
            plt.show()
    
    def plot_predictions_vs_actual(self, save_path: str = None):
        """
        Plot predictions vs actual values.
        
        Args:
            save_path: Path to save the plot
        """
        if len(self.predictions) == 0:
            logger.warning("No predictions available for plotting")
            return
        
        # Create figure
        plt.figure(figsize=(12, 6))
        
        # Plot predictions and actual values
        plt.plot(self.actual_values, label='Actual')
        plt.plot(self.predictions, label='Predicted')
        
        # Add labels and title
        plt.xlabel('Sample')
        plt.ylabel('Value')
        plt.title('Predictions vs Actual Values')
        plt.legend()
        plt.grid(True)
        
        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Predictions plot saved to {save_path}")
        else:
            plt.show()
    
    def plot_confusion_matrix(self, save_path: str = None):
        """
        Plot confusion matrix for direction prediction.
        
        Args:
            save_path: Path to save the plot
        """
        if len(self.predictions) == 0:
            logger.warning("No predictions available for plotting")
            return
        
        # Convert to numpy arrays
        y_pred = np.array(self.predictions)
        y_true = np.array(self.actual_values)
        
        # Convert to direction (up/down)
        y_pred_dir = (y_pred > 0).astype(int)
        y_true_dir = (y_true > 0).astype(int)
        
        # Calculate confusion matrix
        tp = np.sum((y_pred_dir == 1) & (y_true_dir == 1))
        fp = np.sum((y_pred_dir == 1) & (y_true_dir == 0))
        tn = np.sum((y_pred_dir == 0) & (y_true_dir == 0))
        fn = np.sum((y_pred_dir == 0) & (y_true_dir == 1))
        
        # Create confusion matrix
        cm = np.array([[tn, fp], [fn, tp]])
        
        # Create figure
        plt.figure(figsize=(8, 6))
        
        # Plot confusion matrix
        plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
        plt.title('Confusion Matrix')
        plt.colorbar()
        
        # Add labels
        classes = ['Down', 'Up']
        tick_marks = np.arange(len(classes))
        plt.xticks(tick_marks, classes)
        plt.yticks(tick_marks, classes)
        
        # Add text
        thresh = cm.max() / 2.0
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                plt.text(j, i, format(cm[i, j], 'd'),
                        ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black")
        
        plt.ylabel('True Direction')
        plt.xlabel('Predicted Direction')
        
        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Confusion matrix plot saved to {save_path}")
        else:
            plt.show()
    
    def save_metrics(self, filepath: str):
        """
        Save performance metrics to a CSV file.
        
        Args:
            filepath: Path to save the metrics
        """
        # Create DataFrame from performance history
        df = pd.DataFrame(self.performance_history)
        
        # Save to CSV
        df.to_csv(filepath, index=False)
        logger.info(f"Performance metrics saved to {filepath}")
    
    def load_metrics(self, filepath: str) -> bool:
        """
        Load performance metrics from a CSV file.
        
        Args:
            filepath: Path to load the metrics from
            
        Returns:
            bool: True if metrics were loaded successfully, False otherwise
        """
        # Check if file exists
        if not os.path.exists(filepath):
            logger.error(f"Metrics file {filepath} not found")
            return False
        
        try:
            # Load from CSV
            df = pd.read_csv(filepath)
            
            # Update performance history
            self.performance_history = df.to_dict(orient='list')
            
            logger.info(f"Performance metrics loaded from {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error loading metrics: {str(e)}")
            return False

class WalkForwardOptimizer:
    """
    Walk-Forward Optimizer for model validation.
    """
    def __init__(self):
        """Initialize WalkForwardOptimizer."""
        self.walk_forward_config = TRADING_CONFIG['walk_forward']
        self.window_size = self.walk_forward_config['window_size']
        self.step_size = self.walk_forward_config['step_size']
        self.min_training_size = self.walk_forward_config['min_training_size']
        self.out_of_sample_size = self.walk_forward_config['out_of_sample_size']
    
    def generate_windows(
        self, 
        data: pd.DataFrame, 
        date_column: str = None
    ) -> List[Dict[str, Any]]:
        """
        Generate walk-forward windows.
        
        Args:
            data: DataFrame with data
            date_column: Name of the date column
        
        Returns:
            List[Dict[str, Any]]: List of windows
        """
        # Check if data has a date index
        if date_column is not None:
            if date_column in data.columns:
                data = data.set_index(date_column)
            else:
                logger.warning(f"Date column {date_column} not found in data")
        
        # Get data length
        data_length = len(data)
        
        # Check if data is long enough
        if data_length < self.min_training_size + self.out_of_sample_size:
            logger.error(f"Data length ({data_length}) is less than minimum required ({self.min_training_size + self.out_of_sample_size})")
            return []
        
        # Generate windows
        windows = []
        
        for i in range(0, data_length - self.min_training_size - self.out_of_sample_size + 1, self.step_size):
            # Calculate window indices
            train_start = i
            train_end = min(i + self.window_size, data_length - self.out_of_sample_size)
            test_start = train_end
            test_end = min(test_start + self.out_of_sample_size, data_length)
            
            # Skip if training window is too small
            if train_end - train_start < self.min_training_size:
                continue
            
            # Create window
            window = {
                'train_start': train_start,
                'train_end': train_end,
                'test_start': test_start,
                'test_end': test_end,
                'train_data': data.iloc[train_start:train_end],
                'test_data': data.iloc[test_start:test_end]
            }
            
            windows.append(window)
        
        logger.info(f"Generated {len(windows)} walk-forward windows")
        return windows
    
    def plot_windows(self, data: pd.DataFrame, windows: List[Dict[str, Any]], save_path: str = None):
        """
        Plot walk-forward windows.
        
        Args:
            data: DataFrame with data
            windows: List of windows
            save_path: Path to save the plot
        """
        # Create figure
        plt.figure(figsize=(15, 8))
        
        # Plot data
        plt.plot(data.index, data['close'], label='Close Price')
        
        # Plot windows
        for i, window in enumerate(windows):
            train_data = window['train_data']
            test_data = window['test_data']
            
            # Plot training window
            plt.axvspan(train_data.index[0], train_data.index[-1], alpha=0.2, color='blue', label=f'Train {i+1}' if i == 0 else None)
            
            # Plot testing window
            plt.axvspan(test_data.index[0], test_data.index[-1], alpha=0.2, color='red', label=f'Test {i+1}' if i == 0 else None)
        
        # Add labels and title
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.title('Walk-Forward Windows')
        plt.legend()
        plt.grid(True)
        
        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Windows plot saved to {save_path}")
        else:
            plt.show()
