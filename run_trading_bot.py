"""Run Trading Bot Script.
This script is the entry point for running the trading bot.
It automatically opens all MT5 terminals before starting the trading bot.
"""
import os
import sys
import time
import argparse
import traceback
import subprocess

from trading_bot import TradingBot
from config.logging_config import setup_logging
from config.credentials import MT5_TER<PERSON><PERSON><PERSON>

def open_mt5_terminals(logger):
    """
    Open all MT5 terminals before starting the trading bot.
    Uses the /portable parameter to ensure Algo Trading remains enabled.

    Args:
        logger: Logger instance

    Returns:
        bool: True if all terminals were opened successfully, False otherwise
    """
    logger.info("Opening all MT5 terminals with /portable parameter...")

    # Sort terminals by ID to ensure Terminal 1 is opened first
    # This helps prevent disabling Algo Trading in other terminals
    sorted_terminals = sorted(MT5_TERMINALS.items(), key=lambda x: int(x[0]))

    opened_terminals = 0
    for terminal_id, config in sorted_terminals:
        path = config["path"]
        logger.info(f"Opening Terminal {terminal_id}: {path}")

        try:
            # Open the MT5 terminal with the /portable parameter
            # This helps ensure Algo Trading remains enabled
            # When using shell=True, the command should be a string, not a list
            command = f'"{path}" /portable'
            logger.info(f"Executing command: {command}")
            subprocess.Popen(command, shell=True)
            opened_terminals += 1

            # Add a small delay between opening terminals
            # This helps prevent conflicts and ensures terminals open properly
            time.sleep(2)
        except Exception as e:
            logger.error(f"Error opening Terminal {terminal_id}: {str(e)}")
            logger.error(traceback.format_exc())

    # Wait for terminals to start
    if opened_terminals > 0:
        wait_time = 30  # Increased to 30 seconds to give terminals more time to start
        logger.info(f"Opened {opened_terminals} terminals. Waiting {wait_time} seconds for terminals to start...")
        time.sleep(wait_time)
        logger.info("Wait complete. Proceeding with trading bot initialization.")
        return True
    else:
        logger.error("Failed to open any terminals. Please check terminal paths.")
        return False

# Import visualization modules for dashboard generation
try:
    from generate_dashboard import generate_dashboard, load_data
    # We'll import the main function from generate_sample_dashboard when needed
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False

def main():
    """Main function."""
    # Set up logging
    logger = setup_logging()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='BTCUSD Trading Bot')
    parser.add_argument('--terminal', type=int, default=1, help='MT5 terminal ID')
    parser.add_argument('--skip-terminal-open', action='store_true', help='Skip opening MT5 terminals')
    parser.add_argument('--train', action='store_true', help='Train models')
    parser.add_argument('--backtest', action='store_true', help='Run backtest')
    parser.add_argument('--optimize', action='store_true', help='Run walk-forward optimization')
    parser.add_argument('--start-date', type=str, default='2022-01-01', help='Start date for training/backtest')
    parser.add_argument('--end-date', type=str, default=None, help='End date for training/backtest')
    parser.add_argument('--report', action='store_true', help='Generate performance report')
    parser.add_argument('--dashboard', action='store_true', help='Start dashboard only')

    # Visualization options
    parser.add_argument('--generate-dashboard', action='store_true', help='Generate visualization dashboard')
    parser.add_argument('--sample-dashboard', action='store_true', help='Generate sample dashboard with dummy data')
    parser.add_argument('--data-dir', type=str, default='data/storage', help='Data directory for dashboard generation')
    parser.add_argument('--output-dir', type=str, default='monitoring/dashboard', help='Output directory for dashboard')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a', help='Trading symbol')
    parser.add_argument('--days', type=int, default=90, help='Number of days for sample data generation')

    args = parser.parse_args()

    # Log the startup message with the correct symbol
    logger.info(f"Starting Trading Bot for {args.symbol}...")

    try:
        # Open all MT5 terminals before starting the trading bot (unless skipped)
        if not args.skip_terminal_open:
            logger.info("Step 1: Opening all MT5 terminals...")
            if not open_mt5_terminals(logger):
                logger.error("Failed to open MT5 terminals. Continuing anyway, but some terminals may not be available.")
        else:
            logger.info("Skipping terminal opening as requested with --skip-terminal-open")

        logger.info("Step 2: Initializing trading bot...")

        # Create trading bot
        bot = TradingBot(terminal_id=args.terminal, symbol=args.symbol)

        # Run commands
        if args.train:
            logger.info(f"Training models from {args.start_date} to {args.end_date or 'now'}...")
            bot.train_models(args.start_date, args.end_date)
            logger.info("Models trained successfully")

        if args.backtest:
            logger.info(f"Running backtest from {args.start_date} to {args.end_date or 'now'}...")
            results = bot.run_backtest(args.start_date, args.end_date)
            results.to_csv('monitoring/data/backtest_results.csv')
            logger.info(f"Backtest results saved to monitoring/data/backtest_results.csv")

        if args.optimize:
            logger.info(f"Running walk-forward optimization from {args.start_date} to {args.end_date or 'now'}...")
            bot.run_walk_forward_optimization(args.start_date, args.end_date)
            logger.info("Walk-forward optimization completed")

        if args.report:
            logger.info("Generating performance report...")
            bot.generate_performance_report()
            logger.info("Performance report generated")

        if args.dashboard:
            logger.info("Starting dashboard...")
            bot.dashboard.start()

            try:
                # Keep the main thread alive
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                bot.dashboard.stop()
                logger.info("Dashboard stopped")

        # Handle visualization options
        if args.generate_dashboard and VISUALIZATION_AVAILABLE:
            logger.info("Generating visualization dashboard...")
            try:
                # Load data
                price_data, feature_data, performance_data, trades_data, validation_results = load_data(
                    data_dir=args.data_dir,
                    start_date=args.start_date,
                    end_date=args.end_date,
                    symbol=args.symbol
                )

                # Generate dashboard
                dashboard_path = generate_dashboard(
                    price_data=price_data,
                    feature_data=feature_data,
                    performance_data=performance_data,
                    trades_data=trades_data,
                    validation_results=validation_results,
                    output_dir=args.output_dir,
                    symbol=args.symbol
                )

                logger.info(f"Dashboard generated successfully at {dashboard_path}")

                # Open dashboard in browser
                if os.path.exists(dashboard_path):
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(dashboard_path)}")
                    logger.info("Dashboard opened in browser")
            except Exception as e:
                logger.error(f"Error generating dashboard: {str(e)}")
                logger.error(traceback.format_exc())

        elif args.sample_dashboard and VISUALIZATION_AVAILABLE:
            logger.info("Generating sample dashboard...")
            try:
                # Import the main function from generate_sample_dashboard
                from generate_sample_dashboard import main as generate_sample_dashboard_main

                # Set sys.argv for the sample dashboard generator
                sys.argv = [
                    sys.argv[0],
                    f"--output-dir={args.output_dir}",
                    f"--days={args.days}",
                    f"--symbol={args.symbol}"
                ]

                # Run the sample dashboard generator
                generate_sample_dashboard_main()

                logger.info("Sample dashboard generated successfully")
            except Exception as e:
                logger.error(f"Error generating sample dashboard: {str(e)}")
                logger.error(traceback.format_exc())

        # Start bot if no commands were given
        elif not (args.train or args.backtest or args.optimize or args.report or args.dashboard
                or args.generate_dashboard or args.sample_dashboard):
            logger.info("Starting trading bot...")
            bot.start()

            try:
                # Keep the main thread alive
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                bot.stop()
                logger.info("Trading bot stopped")

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        logger.error(traceback.format_exc())
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())