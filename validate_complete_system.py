#!/usr/bin/env python3
"""
Comprehensive System Validation Script.
This script validates all aspects of the MT5 trading system including:
1. MT5 terminal initialization (all 5 terminals)
2. Data collection with standardized paths
3. Model training with correct terminal-model mapping
4. Trading system functionality
5. Documentation organization
"""
import os
import sys
import logging
import time
import traceback
from pathlib import Path
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.abspath('.'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mt5_initialization():
    """Test MT5 terminal initialization."""
    logger.info("="*60)
    logger.info("TESTING MT5 TERMINAL INITIALIZATION")
    logger.info("="*60)
    
    try:
        from utils.mt5_initializer import initialize_all_terminals
        
        # Initialize all terminals
        logger.info("Initializing all 5 MT5 terminals...")
        terminal_status = initialize_all_terminals(max_retries=3, retry_delay=5)
        
        # Check results
        initialized_count = sum(1 for status in terminal_status.values() if status.get('initialized', False))
        algo_enabled_count = sum(1 for status in terminal_status.values() if status.get('algo_trading_enabled', False))
        
        logger.info(f"✓ Terminals initialized: {initialized_count}/5")
        logger.info(f"✓ Algo Trading enabled: {algo_enabled_count}/5")
        
        if initialized_count >= 1:
            logger.info("✓ MT5 INITIALIZATION TEST PASSED")
            return True
        else:
            logger.error("✗ MT5 INITIALIZATION TEST FAILED")
            return False
            
    except Exception as e:
        logger.error(f"✗ MT5 initialization test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_data_collection_paths():
    """Test data collection with standardized paths."""
    logger.info("="*60)
    logger.info("TESTING DATA COLLECTION PATHS")
    logger.info("="*60)
    
    try:
        from utils.path_utils import get_historical_data_path, VALID_TIMEFRAMES, VALID_TERMINAL_IDS
        
        symbol = "BTCUSD.a"
        start_date = "20240101"
        end_date = "20240102"
        
        # Test path generation for all terminals and timeframes
        paths_generated = 0
        for terminal_id in VALID_TERMINAL_IDS:
            for timeframe in VALID_TIMEFRAMES:
                try:
                    path = get_historical_data_path(
                        symbol=symbol,
                        timeframe=timeframe,
                        terminal_id=terminal_id,
                        start_date=start_date,
                        end_date=end_date,
                        create_dirs=True
                    )
                    
                    # Validate path format
                    expected_pattern = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/"
                    if expected_pattern in path.replace('\\', '/'):
                        paths_generated += 1
                        logger.info(f"✓ Valid path: Terminal {terminal_id} {timeframe}")
                    else:
                        logger.error(f"✗ Invalid path: {path}")
                        
                except Exception as e:
                    logger.error(f"✗ Path generation failed for Terminal {terminal_id} {timeframe}: {str(e)}")
        
        expected_paths = len(VALID_TERMINAL_IDS) * len(VALID_TIMEFRAMES)
        logger.info(f"✓ Generated {paths_generated}/{expected_paths} valid paths")
        
        if paths_generated == expected_paths:
            logger.info("✓ DATA COLLECTION PATHS TEST PASSED")
            return True
        else:
            logger.error("✗ DATA COLLECTION PATHS TEST FAILED")
            return False
            
    except Exception as e:
        logger.error(f"✗ Data collection paths test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_model_paths():
    """Test model path generation with terminal-model mapping."""
    logger.info("="*60)
    logger.info("TESTING MODEL PATHS & TERMINAL MAPPING")
    logger.info("="*60)
    
    try:
        from utils.path_utils import (
            get_model_path, get_metrics_path, get_terminal_model_type,
            VALID_TIMEFRAMES, VALID_TERMINAL_IDS, TERMINAL_MODEL_TYPES
        )
        
        symbol = "BTCUSD.a"
        paths_generated = 0
        
        # Test terminal-model mapping
        logger.info("Testing terminal-model mapping:")
        for terminal_id in VALID_TERMINAL_IDS:
            model_type = get_terminal_model_type(terminal_id)
            expected_model_type = TERMINAL_MODEL_TYPES[terminal_id]
            
            if model_type == expected_model_type:
                logger.info(f"✓ Terminal {terminal_id}: {model_type}")
                paths_generated += 1
            else:
                logger.error(f"✗ Terminal {terminal_id}: Expected {expected_model_type}, got {model_type}")
        
        # Test model path generation
        logger.info("Testing model path generation:")
        for terminal_id in VALID_TERMINAL_IDS:
            model_type = get_terminal_model_type(terminal_id)
            
            for timeframe in VALID_TIMEFRAMES:
                try:
                    model_path = get_model_path(symbol, timeframe, model_type)
                    metrics_path = get_metrics_path(symbol, timeframe, model_type)
                    
                    # Validate paths (ensemble models use 'ensemble' folder)
                    expected_folder = 'ensemble' if '_' in model_type else model_type
                    if f"saved_models/{expected_folder}" in model_path.replace('\\', '/'):
                        logger.info(f"✓ Model path: Terminal {terminal_id} {timeframe}")
                        paths_generated += 1
                    else:
                        logger.error(f"✗ Invalid model path: {model_path}")
                        
                except Exception as e:
                    logger.error(f"✗ Model path generation failed: {str(e)}")
        
        if paths_generated >= len(VALID_TERMINAL_IDS):
            logger.info("✓ MODEL PATHS TEST PASSED")
            return True
        else:
            logger.error("✗ MODEL PATHS TEST FAILED")
            return False
            
    except Exception as e:
        logger.error(f"✗ Model paths test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_documentation_structure():
    """Test documentation organization."""
    logger.info("="*60)
    logger.info("TESTING DOCUMENTATION STRUCTURE")
    logger.info("="*60)
    
    try:
        required_folders = [
            "01_system_architecture",
            "02_data_management", 
            "03_model_training",
            "04_trading_operations",
            "05_deployment_monitoring"
        ]
        
        docs_path = Path("documents")
        folders_found = 0
        total_docs = 0
        
        for folder in required_folders:
            folder_path = docs_path / folder
            if folder_path.exists() and folder_path.is_dir():
                folders_found += 1
                
                # Count markdown files
                md_files = list(folder_path.glob("*.md"))
                doc_count = len(md_files)
                total_docs += doc_count
                
                logger.info(f"✓ {folder}: {doc_count} documents")
                
                if doc_count > 8:
                    logger.warning(f"⚠ {folder} has {doc_count} documents (max 8 recommended)")
            else:
                logger.error(f"✗ Missing folder: {folder}")
        
        logger.info(f"✓ Found {folders_found}/5 required folders")
        logger.info(f"✓ Total documents: {total_docs}")
        
        if folders_found == 5:
            logger.info("✓ DOCUMENTATION STRUCTURE TEST PASSED")
            return True
        else:
            logger.error("✗ DOCUMENTATION STRUCTURE TEST FAILED")
            return False
            
    except Exception as e:
        logger.error(f"✗ Documentation structure test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_ensemble_model_functionality():
    """Test ensemble model serialization and functionality."""
    logger.info("="*60)
    logger.info("TESTING ENSEMBLE MODEL FUNCTIONALITY")
    logger.info("="*60)
    
    try:
        from model.ensemble_model import EnsembleModel
        from utils.path_utils import get_model_path
        
        # Test ensemble model creation
        symbol = "BTCUSD.a"
        timeframe = "M5"
        
        # Test LSTM+ARIMA ensemble (Terminal 4)
        lstm_path = get_model_path(symbol, timeframe, 'lstm')
        arima_path = get_model_path(symbol, timeframe, 'arima')
        
        ensemble = EnsembleModel(
            model_paths=[lstm_path, arima_path],
            model_types=['lstm', 'arima'],
            weights=[0.6, 0.4]
        )
        
        # Test serialization
        test_path = "test_ensemble_validation.pkl"
        success = ensemble.save_model(test_path)
        
        if success and os.path.exists(test_path):
            file_size = os.path.getsize(test_path)
            logger.info(f"✓ Ensemble saved successfully (size: {file_size} bytes)")
            
            # Test loading
            loaded_ensemble = EnsembleModel.load(test_path)
            if loaded_ensemble is not None:
                logger.info("✓ Ensemble loaded successfully")
                
                # Clean up
                os.remove(test_path)
                
                logger.info("✓ ENSEMBLE MODEL FUNCTIONALITY TEST PASSED")
                return True
            else:
                logger.error("✗ Failed to load ensemble model")
                return False
        else:
            logger.error("✗ Failed to save ensemble model")
            return False
            
    except Exception as e:
        logger.error(f"✗ Ensemble model functionality test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def main():
    """Run comprehensive system validation."""
    logger.info("🚀 STARTING COMPREHENSIVE SYSTEM VALIDATION")
    logger.info("="*80)
    
    tests = [
        ("MT5 Terminal Initialization", test_mt5_initialization),
        ("Data Collection Paths", test_data_collection_paths),
        ("Model Paths & Terminal Mapping", test_model_paths),
        ("Documentation Structure", test_documentation_structure),
        ("Ensemble Model Functionality", test_ensemble_model_functionality)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"✗ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "="*80)
    logger.info("📊 VALIDATION RESULTS SUMMARY")
    logger.info("="*80)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✓ PASSED" if result else "✗ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info("="*80)
    logger.info(f"OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL SYSTEM VALIDATIONS PASSED!")
        logger.info("✅ The MT5 trading system is ready for operation")
        return 0
    else:
        logger.error(f"❌ {total - passed} validation(s) failed")
        logger.error("⚠️ Please address the failed validations before proceeding")
        return 1

if __name__ == "__main__":
    sys.exit(main())
