{"1": {"model_type": "arima", "description": "ARIMA Models", "models": {"AUDNZD.a": {"M5": {"model_path": "model\\saved_models\\arima\\AUDNZD.a_M5_arima.pkl", "metrics_path": "model\\saved_models\\arima\\AUDNZD.a_M5_arima_metrics.json", "is_best": true, "note": "Best model for AUDNZD.a (M5)"}, "M15": {"model_path": "model\\saved_models\\arima\\AUDNZD.a_M15_arima.pkl", "metrics_path": "model\\saved_models\\arima\\AUDNZD.a_M15_arima_metrics.json", "is_best": false, "note": "Using arima model for AUDNZD.a (M15)"}, "M30": {"model_path": "model\\saved_models\\arima\\AUDNZD.a_M30_arima.pkl", "metrics_path": "model\\saved_models\\arima\\AUDNZD.a_M30_arima_metrics.json", "is_best": false, "note": "Using arima model for AUDNZD.a (M30)"}, "H1": {"model_path": "model\\saved_models\\arima\\AUDNZD.a_H1_arima.pkl", "metrics_path": "model\\saved_models\\arima\\AUDNZD.a_H1_arima_metrics.json", "is_best": false, "note": "Using arima model for AUDNZD.a (H1)"}, "H4": {"model_path": "model\\saved_models\\arima\\AUDNZD.a_H4_arima.pkl", "metrics_path": "model\\saved_models\\arima\\AUDNZD.a_H4_arima_metrics.json", "is_best": false, "note": "Using arima model for AUDNZD.a (H4)"}}}}, "2": {"model_type": "lstm", "description": "LSTM Models", "models": {"AUDNZD.a": {"M5": {"model_path": "model\\saved_models\\lstm\\AUDNZD.a_M5_lstm.pth", "metrics_path": "model\\saved_models\\lstm\\AUDNZD.a_M5_lstm_metrics.json", "is_best": false, "note": "Using lstm model for AUDNZD.a (M5)"}, "M15": {"model_path": "model\\saved_models\\lstm\\AUDNZD.a_M15_lstm.pth", "metrics_path": "model\\saved_models\\lstm\\AUDNZD.a_M15_lstm_metrics.json", "is_best": false, "note": "Using lstm model for AUDNZD.a (M15)"}, "M30": {"model_path": "model\\saved_models\\lstm\\AUDNZD.a_M30_lstm.pth", "metrics_path": "model\\saved_models\\lstm\\AUDNZD.a_M30_lstm_metrics.json", "is_best": false, "note": "Using lstm model for AUDNZD.a (M30)"}, "H1": {"model_path": "model\\saved_models\\lstm\\AUDNZD.a_H1_lstm.pth", "metrics_path": "model\\saved_models\\lstm\\AUDNZD.a_H1_lstm_metrics.json", "is_best": false, "note": "Using lstm model for AUDNZD.a (H1)"}, "H4": {"model_path": "model\\saved_models\\lstm\\AUDNZD.a_H4_lstm.pth", "metrics_path": "model\\saved_models\\lstm\\AUDNZD.a_H4_lstm_metrics.json", "is_best": false, "note": "Using lstm model for AUDNZD.a (H4)"}}}}, "3": {"model_type": "tft", "description": "TFT Models", "models": {"AUDNZD.a": {"M5": {"model_path": "model\\saved_models\\tft\\AUDNZD.a_M5_tft.pth", "metrics_path": "model\\saved_models\\tft\\AUDNZD.a_M5_tft_metrics.json", "is_best": false, "note": "Using tft model for AUDNZD.a (M5)"}, "M15": {"model_path": "model\\saved_models\\tft\\AUDNZD.a_M15_tft.pth", "metrics_path": "model\\saved_models\\tft\\AUDNZD.a_M15_tft_metrics.json", "is_best": true, "note": "Best model for AUDNZD.a (M15)"}, "M30": {"model_path": "model\\saved_models\\tft\\AUDNZD.a_M30_tft.pth", "metrics_path": "model\\saved_models\\tft\\AUDNZD.a_M30_tft_metrics.json", "is_best": true, "note": "Best model for AUDNZD.a (M30)"}, "H1": {"model_path": "model\\saved_models\\tft\\AUDNZD.a_H1_tft.pth", "metrics_path": "model\\saved_models\\tft\\AUDNZD.a_H1_tft_metrics.json", "is_best": true, "note": "Best model for AUDNZD.a (H1)"}, "H4": {"model_path": "model\\saved_models\\tft\\AUDNZD.a_H4_tft.pth", "metrics_path": "model\\saved_models\\tft\\AUDNZD.a_H4_tft_metrics.json", "is_best": true, "note": "Best model for AUDNZD.a (H4)"}}}}, "4": {"model_type": "lstm_arima", "description": "LSTM + ARIMA Ensemble", "models": {"AUDNZD.a": {"M5": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_M5_lstm_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_M5_lstm_arima_metrics.json", "is_best": false, "note": "Using lstm_arima model for AUDNZD.a (M5)"}, "M15": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_M15_lstm_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_M15_lstm_arima_metrics.json", "is_best": false, "note": "Using lstm_arima model for AUDNZD.a (M15)"}, "M30": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_M30_lstm_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_M30_lstm_arima_metrics.json", "is_best": false, "note": "Using lstm_arima model for AUDNZD.a (M30)"}, "H1": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_H1_lstm_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_H1_lstm_arima_metrics.json", "is_best": false, "note": "Using lstm_arima model for AUDNZD.a (H1)"}, "H4": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_H4_lstm_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_H4_lstm_arima_metrics.json", "is_best": false, "note": "Using lstm_arima model for AUDNZD.a (H4)"}}}}, "5": {"model_type": "tft_arima", "description": "TFT + ARIMA Ensemble", "models": {"AUDNZD.a": {"M5": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_M5_tft_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_M5_tft_arima_metrics.json", "is_best": false, "note": "Using tft_arima model for AUDNZD.a (M5)"}, "M15": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_M15_tft_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_M15_tft_arima_metrics.json", "is_best": false, "note": "Using tft_arima model for AUDNZD.a (M15)"}, "M30": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_M30_tft_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_M30_tft_arima_metrics.json", "is_best": false, "note": "Using tft_arima model for AUDNZD.a (M30)"}, "H1": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_H1_tft_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_H1_tft_arima_metrics.json", "is_best": false, "note": "Using tft_arima model for AUDNZD.a (H1)"}, "H4": {"model_path": "model\\saved_models\\ensemble\\AUDNZD.a_H4_tft_arima.pkl", "metrics_path": "model\\saved_models\\ensemble\\AUDNZD.a_H4_tft_arima_metrics.json", "is_best": false, "note": "Using tft_arima model for AUDNZD.a (H4)"}}}}}