"""
Path Utilities Module.
This module provides utility functions for working with file paths.
Uses pathlib for cross-platform path handling and validation.
This is the central source of truth for all path-related operations,
including data paths, model paths, and terminal-model assignments.
"""
import os
import logging
import re
from pathlib import Path
from typing import Union, List, Tuple, Optional
from datetime import datetime

# Configure logger
logger = logging.getLogger('utils.path_utils')

# Standard directories
DATA_DIR = Path("data")
MODEL_DIR = Path("model")
STORAGE_DIR = DATA_DIR / "storage"
HISTORICAL_DATA_DIR = STORAGE_DIR / "historical"
FEATURES_DIR = STORAGE_DIR / "features"
SAVED_MODELS_DIR = MODEL_DIR / "saved_models"
VISUALIZATIONS_DIR = MODEL_DIR / "visualizations"

# Valid timeframes - focusing on required timeframes only
VALID_TIMEFRAMES = ['M5', 'M15', 'M30', 'H1', 'H4']

# Valid model types
VALID_MODEL_TYPES = ['lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima']

# Valid terminal IDs
VALID_TERMINAL_IDS = [1, 2, 3, 4, 5]

# Terminal-model type mapping
TERMINAL_MODEL_TYPES = {
    1: 'arima',
    2: 'lstm',
    3: 'tft',
    4: 'lstm_arima',
    5: 'tft_arima'
}

# Model type descriptions
MODEL_TYPE_DESCRIPTIONS = {
    'arima': 'ARIMA (AutoRegressive Integrated Moving Average)',
    'lstm': 'LSTM (Long Short-Term Memory)',
    'tft': 'TFT (Temporal Fusion Transformer)',
    'lstm_arima': 'LSTM + ARIMA Ensemble',
    'tft_arima': 'TFT + ARIMA Ensemble'
}

# Model type file extensions
MODEL_FILE_EXTENSIONS = {
    'arima': 'pkl',
    'lstm': 'pth',
    'tft': 'pth',
    'lstm_arima': 'pkl',
    'tft_arima': 'pkl'
}

# Ensemble model components
ENSEMBLE_COMPONENTS = {
    'lstm_arima': ['lstm', 'arima'],
    'tft_arima': ['tft', 'arima']
}

def normalize_path(path: Union[str, Path]) -> str:
    """
    Normalize a path to use the correct path separators for the current OS.

    Args:
        path: The path to normalize (string or Path object)

    Returns:
        str: The normalized path as a string
    """
    # Convert to Path object and then back to string
    return str(Path(path))

def validate_path(path: Union[str, Path], must_exist: bool = False) -> Path:
    """
    Validate a path and return a Path object.

    Args:
        path: The path to validate (string or Path object)
        must_exist: If True, the path must exist on the filesystem

    Returns:
        Path: The validated Path object

    Raises:
        ValueError: If the path is invalid or doesn't exist (when must_exist=True)
    """
    try:
        path_obj = Path(path)
        if must_exist and not path_obj.exists():
            logger.warning(f"Path does not exist: {path}")
            raise ValueError(f"Path does not exist: {path}")
        return path_obj
    except Exception as e:
        logger.error(f"Invalid path: {path} - {str(e)}")
        raise ValueError(f"Invalid path: {path} - {str(e)}")

def ensure_directory(path: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.

    Args:
        path: The directory path to ensure exists

    Returns:
        Path: The Path object for the directory
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj

def get_executable_name(path: Union[str, Path]) -> str:
    """
    Get the executable name from a path.

    Args:
        path: The path to extract the executable name from

    Returns:
        str: The executable name
    """
    return Path(path).name

def is_windows_path(path: Union[str, Path]) -> bool:
    """
    Check if a path is a Windows path.

    Args:
        path: The path to check

    Returns:
        bool: True if the path is a Windows path, False otherwise
    """
    path_str = str(path)
    return '\\' in path_str or (':' in path_str and os.name == 'nt')

def convert_to_windows_path(path: Union[str, Path]) -> str:
    """
    Convert a path to Windows format.

    Args:
        path: The path to convert

    Returns:
        str: The path in Windows format
    """
    # Convert to string first to handle Path objects
    path_str = str(path)

    # Log a warning if we're on a non-Windows system
    if os.name != 'nt':
        logger.warning("Converting to Windows path on a non-Windows system")

    return path_str.replace('/', '\\')

def convert_to_posix_path(path: Union[str, Path]) -> str:
    """
    Convert a path to POSIX format.

    Args:
        path: The path to convert

    Returns:
        str: The path in POSIX format
    """
    path_obj = Path(path)
    return str(path_obj).replace('\\', '/')

def get_mt5_path(config: dict) -> str:
    """
    Get the MT5 terminal path in the correct format for the current OS.

    Args:
        config: The terminal configuration

    Returns:
        str: The MT5 terminal path
    """
    path = config.get('path', '')
    return normalize_path(path)

def validate_path_format(path: Union[str, Path], expected_format: str) -> Tuple[bool, str]:
    """
    Validate that a path follows the expected format.

    Args:
        path: Path to validate
        expected_format: Expected format pattern (regex)

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    path_str = str(path)

    # Check if path matches the expected format
    if re.match(expected_format, path_str):
        return True, ""
    else:
        error_msg = f"Path '{path_str}' does not match expected format '{expected_format}'"
        logger.warning(error_msg)
        return False, error_msg

def validate_historical_data_path(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate that a path follows the historical data path format.

    Args:
        path: Path to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    # Expected format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
    # or: data/storage/historical/merged/{symbol}_{timeframe}/{start_date}_{end_date}_merged_{timestamp}.parquet

    terminal_pattern = r'data/storage/historical/terminal_\d+/[A-Za-z0-9\.]+_[A-Za-z0-9]+/\d+_\d+_terminal_\d+_\d+\.parquet'
    merged_pattern = r'data/storage/historical/merged/[A-Za-z0-9\.]+_[A-Za-z0-9]+/\d+_\d+_merged_\d+\.parquet'

    # Check if path matches either pattern
    path_str = str(path).replace('\\', '/')
    if re.match(terminal_pattern, path_str) or re.match(merged_pattern, path_str):
        return True, ""
    else:
        error_msg = f"Historical data path '{path_str}' does not match expected format"
        logger.warning(error_msg)
        return False, error_msg

def validate_features_path(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate that a path follows the features path format.

    Args:
        path: Path to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    # Expected format: data/storage/features/terminal_{terminal_id}/{symbol}_{timeframe}/features_terminal_{terminal_id}_{timestamp}.parquet
    # or: data/storage/features/merged/{symbol}_{timeframe}/features_merged_{timestamp}.parquet

    terminal_pattern = r'data/storage/features/terminal_\d+/[A-Za-z0-9\.]+_[A-Za-z0-9]+/features_terminal_\d+_\d+\.parquet'
    merged_pattern = r'data/storage/features/merged/[A-Za-z0-9\.]+_[A-Za-z0-9]+/features_merged_\d+\.parquet'

    # Check if path matches either pattern
    path_str = str(path).replace('\\', '/')
    if re.match(terminal_pattern, path_str) or re.match(merged_pattern, path_str):
        return True, ""
    else:
        error_msg = f"Features path '{path_str}' does not match expected format"
        logger.warning(error_msg)
        return False, error_msg

def validate_model_path(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate that a path follows the model path format.

    Args:
        path: Path to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    # Expected formats:
    # model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
    # model/saved_models/tft/{symbol}_{timeframe}_tft.pth
    # model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
    # model/saved_models/ensemble/{symbol}_{timeframe}_{model_type}.pkl

    lstm_pattern = r'model/saved_models/lstm/[A-Za-z0-9\.]+_[A-Za-z0-9]+_lstm\.pth'
    tft_pattern = r'model/saved_models/tft/[A-Za-z0-9\.]+_[A-Za-z0-9]+_tft\.pth'
    arima_pattern = r'model/saved_models/arima/[A-Za-z0-9\.]+_[A-Za-z0-9]+_arima\.pkl'
    ensemble_pattern = r'model/saved_models/ensemble/[A-Za-z0-9\.]+_[A-Za-z0-9]+_(lstm_arima|tft_arima)\.pkl'

    # Check if path matches any of the patterns
    path_str = str(path).replace('\\', '/')
    if (re.match(lstm_pattern, path_str) or
        re.match(tft_pattern, path_str) or
        re.match(arima_pattern, path_str) or
        re.match(ensemble_pattern, path_str)):
        return True, ""
    else:
        error_msg = f"Model path '{path_str}' does not match expected format"
        logger.warning(error_msg)
        return False, error_msg

def get_historical_data_path(
    symbol: str,
    timeframe: str,
    terminal_id: Optional[int] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    create_dirs: bool = True,
    use_latest: bool = False
) -> str:
    """
    Get the standardized path for historical data.
    Following the required format:
    data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
    or for latest data:
    data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/latest.parquet

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (optional)
        start_date: Start date in YYYYMMDD format (optional)
        end_date: End date in YYYYMMDD format (optional)
        create_dirs: Whether to create directories if they don't exist
        use_latest: Whether to use the latest file instead of creating a new one

    Returns:
        str: Path to the historical data file
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Validate timeframe format
    if timeframe not in VALID_TIMEFRAMES:
        logger.warning(f"Timeframe {timeframe} is not in standard format {VALID_TIMEFRAMES}")

    # Generate timestamp
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

    # Determine base directory based on terminal_id
    if terminal_id is not None:
        # Validate terminal_id
        if terminal_id not in VALID_TERMINAL_IDS:
            logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")
            # Use default terminal_id=1 if invalid
            terminal_id = 1
            logger.info(f"Using default Terminal ID: {terminal_id}")

        # Format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
        base_dir = HISTORICAL_DATA_DIR / f"terminal_{terminal_id}"
    else:
        # Path for merged data
        # Format: data/storage/historical/merged/{symbol}_{timeframe}/{start_date}_{end_date}_merged_{timestamp}.parquet
        base_dir = HISTORICAL_DATA_DIR / "merged"

    # Create symbol_timeframe directory
    symbol_timeframe_dir = base_dir / f"{symbol}_{timeframe}"

    # Create directories if requested
    if create_dirs:
        symbol_timeframe_dir.mkdir(parents=True, exist_ok=True)

    # Determine filename based on provided dates
    if start_date and end_date:
        # Validate date formats
        try:
            if len(start_date) == 10 and '-' in start_date:  # YYYY-MM-DD format
                datetime.strptime(start_date, "%Y-%m-%d")
                start_date = start_date.replace('-', '')
            elif len(start_date) == 8:  # YYYYMMDD format
                datetime.strptime(start_date, "%Y%m%d")
            else:
                raise ValueError(f"Invalid start_date format: {start_date}")

            if len(end_date) == 10 and '-' in end_date:  # YYYY-MM-DD format
                datetime.strptime(end_date, "%Y-%m-%d")
                end_date = end_date.replace('-', '')
            elif len(end_date) == 8:  # YYYYMMDD format
                datetime.strptime(end_date, "%Y%m%d")
            else:
                raise ValueError(f"Invalid end_date format: {end_date}")
        except ValueError as e:
            logger.warning(f"Date format invalid: {str(e)}")
            # Use current date as fallback
            current_date = datetime.now().strftime("%Y%m%d")
            start_date = current_date
            end_date = current_date
            logger.info(f"Using current date as fallback: {current_date}")

        # Create versioned filename following the required format
        if terminal_id is not None:
            filename = f"{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet"
        else:
            filename = f"{start_date}_{end_date}_merged_{timestamp}.parquet"
    else:
        # Default filename with current date
        current_date = datetime.now().strftime("%Y%m%d")
        if terminal_id is not None:
            filename = f"{current_date}_{current_date}_terminal_{terminal_id}_{timestamp}.parquet"
        else:
            filename = f"{current_date}_{current_date}_merged_{timestamp}.parquet"

    # Check if we should use the latest file
    if use_latest:
        # Return the path to the latest file
        filepath = symbol_timeframe_dir / "latest.parquet"

        # Check if the latest file exists
        if not os.path.exists(filepath):
            logger.warning(f"Latest file not found at {filepath}, will create a new file")
            # Fall back to creating a new file
            filepath = symbol_timeframe_dir / filename
    else:
        # Full path following the required format
        filepath = symbol_timeframe_dir / filename

    # Validate the path format
    path_str = str(filepath).replace('\\', '/')
    expected_pattern = r'data/storage/historical/terminal_\d+/[A-Za-z0-9\.]+_[A-Za-z0-9]+/\d+_\d+_terminal_\d+_\d+\.parquet'
    merged_pattern = r'data/storage/historical/merged/[A-Za-z0-9\.]+_[A-Za-z0-9]+/\d+_\d+_merged_\d+\.parquet'
    latest_pattern = r'data/storage/historical/(terminal_\d+|merged)/[A-Za-z0-9\.]+_[A-Za-z0-9]+/latest\.parquet'

    if use_latest:
        if not re.match(latest_pattern, path_str):
            logger.warning(f"Generated path does not match expected format for latest file: {path_str}")
            logger.info(f"Expected format: {latest_pattern}")
    elif terminal_id is not None:
        if not re.match(expected_pattern, path_str):
            logger.warning(f"Generated path does not match expected format: {path_str}")
            logger.info(f"Expected format: {expected_pattern}")
    else:
        if not re.match(merged_pattern, path_str):
            logger.warning(f"Generated path does not match expected format: {path_str}")
            logger.info(f"Expected format: {merged_pattern}")

    # Return full path as string
    return str(filepath)

def get_features_path(
    symbol: str,
    timeframe: str,
    terminal_id: Optional[int] = None,
    create_dirs: bool = True
) -> str:
    """
    Get the standardized path for feature data.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (optional)
        create_dirs: Whether to create directories if they don't exist

    Returns:
        str: Path to the feature data file
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Validate timeframe format
    if timeframe not in VALID_TIMEFRAMES:
        logger.warning(f"Timeframe {timeframe} is not in standard format {VALID_TIMEFRAMES}")

    # Generate timestamp
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

    # Determine base directory based on terminal_id
    if terminal_id is not None:
        # Validate terminal_id
        if terminal_id not in VALID_TERMINAL_IDS:
            logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")

        # Format: data/storage/features/terminal_{terminal_id}/{symbol}_{timeframe}/features_terminal_{terminal_id}_{timestamp}.parquet
        base_dir = FEATURES_DIR / f"terminal_{terminal_id}"
    else:
        # Path for merged data
        # Format: data/storage/features/merged/{symbol}_{timeframe}/features_merged_{timestamp}.parquet
        base_dir = FEATURES_DIR / "merged"

    # Create symbol_timeframe directory
    symbol_timeframe_dir = base_dir / f"{symbol}_{timeframe}"

    # Create directories if requested
    if create_dirs:
        symbol_timeframe_dir.mkdir(parents=True, exist_ok=True)

    # Create filename
    if terminal_id is not None:
        filename = f"features_terminal_{terminal_id}_{timestamp}.parquet"
    else:
        filename = f"features_merged_{timestamp}.parquet"

    # Return full path as string
    return str(symbol_timeframe_dir / filename)

def get_model_path(
    symbol: str,
    timeframe: str,
    model_type: str = None,
    terminal_id: Optional[int] = None
) -> str:
    """
    Get the standardized path for a model file.
    Following the required format:
    Terminal 1: model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
    Terminal 2: model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
    Terminal 3: model/saved_models/tft/{symbol}_{timeframe}_tft.pth
    Terminal 4: model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima.pkl
    Terminal 5: model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima.pkl

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        terminal_id: Terminal ID (optional)

    Returns:
        str: Path to the model file
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Validate timeframe format
    if timeframe not in VALID_TIMEFRAMES:
        logger.warning(f"Timeframe {timeframe} is not in standard format {VALID_TIMEFRAMES}")

    # Create base directory if it doesn't exist
    base_dir = SAVED_MODELS_DIR
    base_dir.mkdir(parents=True, exist_ok=True)

    # If terminal_id is provided, use it to determine the model type
    if terminal_id is not None:
        # Validate terminal_id
        if terminal_id not in VALID_TERMINAL_IDS:
            logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")
            # Use default terminal_id=2 (LSTM) if invalid
            terminal_id = 2
            logger.info(f"Using default Terminal ID: {terminal_id}")

        # Get model type from terminal ID using our mapping
        model_type = TERMINAL_MODEL_TYPES.get(terminal_id, 'lstm')
        logger.info(f"Using model type '{model_type}' for Terminal {terminal_id}")
    elif model_type is None:
        logger.error("Either model_type or terminal_id must be provided")
        # Default to LSTM if neither is provided
        model_type = 'lstm'
        logger.info(f"Using default model type: {model_type}")

    # Validate model_type
    if model_type not in VALID_MODEL_TYPES:
        logger.warning(f"Model type {model_type} is not in standard format {VALID_MODEL_TYPES}")
        # Default to LSTM if invalid
        model_type = 'lstm'
        logger.info(f"Using default model type: {model_type}")

    # Get the file extension for the model type
    extension = get_model_file_extension(model_type)

    # Determine the appropriate directory and filename based on model type
    # following the required standardized paths
    if model_type == 'lstm':
        # Terminal 2: LSTM
        model_dir = base_dir / 'lstm'
        model_dir.mkdir(parents=True, exist_ok=True)
        # Standard path format: model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
        filepath = model_dir / f"{symbol}_{timeframe}_lstm.{extension}"
    elif model_type == 'tft':
        # Terminal 3: TFT
        model_dir = base_dir / 'tft'
        model_dir.mkdir(parents=True, exist_ok=True)
        # Standard path format: model/saved_models/tft/{symbol}_{timeframe}_tft.pth
        filepath = model_dir / f"{symbol}_{timeframe}_tft.{extension}"
    elif model_type == 'arima':
        # Terminal 1: ARIMA
        model_dir = base_dir / 'arima'
        model_dir.mkdir(parents=True, exist_ok=True)
        # Standard path format: model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
        filepath = model_dir / f"{symbol}_{timeframe}_arima.{extension}"
    elif model_type in ['lstm_arima', 'tft_arima']:
        # Terminal 4 & 5: Ensemble models
        model_dir = base_dir / 'ensemble'
        model_dir.mkdir(parents=True, exist_ok=True)
        # Standard path format: model/saved_models/ensemble/{symbol}_{timeframe}_{model_type}.pkl
        filepath = model_dir / f"{symbol}_{timeframe}_{model_type}.{extension}"
    else:
        # Default case for unknown model types (should not happen with validation above)
        model_dir = base_dir / model_type
        model_dir.mkdir(parents=True, exist_ok=True)
        filepath = model_dir / f"{symbol}_{timeframe}_{model_type}.{extension}"

    # Validate the path format
    path_str = str(filepath).replace('\\', '/')

    # Define expected patterns for each model type
    patterns = {
        'lstm': r'model/saved_models/lstm/[A-Za-z0-9\.]+_[A-Za-z0-9]+_lstm\.pth',
        'tft': r'model/saved_models/tft/[A-Za-z0-9\.]+_[A-Za-z0-9]+_tft\.pth',
        'arima': r'model/saved_models/arima/[A-Za-z0-9\.]+_[A-Za-z0-9]+_arima\.pkl',
        'lstm_arima': r'model/saved_models/ensemble/[A-Za-z0-9\.]+_[A-Za-z0-9]+_lstm_arima\.pkl',
        'tft_arima': r'model/saved_models/ensemble/[A-Za-z0-9\.]+_[A-Za-z0-9]+_tft_arima\.pkl'
    }

    # Check if path matches the expected pattern for the model type
    if model_type in patterns and not re.match(patterns[model_type], path_str):
        logger.warning(f"Generated model path does not match expected format: {path_str}")
        logger.info(f"Expected format: {patterns[model_type]}")

    # Return full path as string
    return str(filepath)

def get_metrics_path(
    symbol: str,
    timeframe: str,
    model_type: str = None,
    terminal_id: Optional[int] = None
) -> str:
    """
    Get the standardized path for a model metrics file.
    Following the required format:
    Terminal 1: model/saved_models/arima/{symbol}_{timeframe}_arima_metrics.json
    Terminal 2: model/saved_models/lstm/{symbol}_{timeframe}_lstm_metrics.json
    Terminal 3: model/saved_models/tft/{symbol}_{timeframe}_tft_metrics.json
    Terminal 4: model/saved_models/ensemble/{symbol}_{timeframe}_lstm_arima_metrics.json
    Terminal 5: model/saved_models/ensemble/{symbol}_{timeframe}_tft_arima_metrics.json

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        terminal_id: Terminal ID (optional)

    Returns:
        str: Path to the metrics file
    """
    # Get the model path
    model_path = get_model_path(symbol, timeframe, model_type, terminal_id)

    # Convert to Path object
    path_obj = Path(model_path)

    # Create metrics path by replacing extension with _metrics.json
    metrics_path = path_obj.with_name(f"{path_obj.stem}_metrics.json")

    # Validate the path format
    path_str = str(metrics_path).replace('\\', '/')

    # Define expected patterns for each model type
    if terminal_id is not None:
        model_type = TERMINAL_MODEL_TYPES.get(terminal_id, 'lstm')
    elif model_type is None:
        model_type = 'lstm'

    patterns = {
        'lstm': r'model/saved_models/lstm/[A-Za-z0-9\.]+_[A-Za-z0-9]+_lstm_metrics\.json',
        'tft': r'model/saved_models/tft/[A-Za-z0-9\.]+_[A-Za-z0-9]+_tft_metrics\.json',
        'arima': r'model/saved_models/arima/[A-Za-z0-9\.]+_[A-Za-z0-9]+_arima_metrics\.json',
        'lstm_arima': r'model/saved_models/ensemble/[A-Za-z0-9\.]+_[A-Za-z0-9]+_lstm_arima_metrics\.json',
        'tft_arima': r'model/saved_models/ensemble/[A-Za-z0-9\.]+_[A-Za-z0-9]+_tft_arima_metrics\.json'
    }

    # Check if path matches the expected pattern for the model type
    if model_type in patterns and not re.match(patterns[model_type], path_str):
        logger.warning(f"Generated metrics path does not match expected format: {path_str}")
        logger.info(f"Expected format: {patterns[model_type]}")

    # Return as string
    return str(metrics_path)

def get_visualization_path(
    symbol: str,
    timeframe: str,
    model_type: str = None,
    visualization_type: str = "predictions",
    terminal_id: Optional[int] = None
) -> str:
    """
    Get the standardized path for a model visualization file.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        visualization_type: Type of visualization (e.g., 'predictions', 'feature_importance')
        terminal_id: Terminal ID (optional)

    Returns:
        str: Path to the visualization file
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Validate timeframe format
    if timeframe not in VALID_TIMEFRAMES:
        logger.warning(f"Timeframe {timeframe} is not in standard format {VALID_TIMEFRAMES}")

    # If terminal_id is provided, use it to determine the model type
    if terminal_id is not None:
        # Validate terminal_id
        if terminal_id not in VALID_TERMINAL_IDS:
            logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")

        # Get model type from terminal ID using our mapping
        model_type = TERMINAL_MODEL_TYPES.get(terminal_id, 'lstm')
    elif model_type is None:
        logger.error("Either model_type or terminal_id must be provided")
        # Default to LSTM if neither is provided
        model_type = 'lstm'

    # Validate model_type
    if model_type not in VALID_MODEL_TYPES:
        logger.warning(f"Model type {model_type} is not in standard format {VALID_MODEL_TYPES}")

    # Validate visualization_type
    valid_viz_types = ['predictions', 'feature_importance', 'residuals', 'component_contributions', 'metrics']
    if visualization_type not in valid_viz_types:
        logger.warning(f"Visualization type {visualization_type} is not in standard format {valid_viz_types}")

    # Create visualization directory if it doesn't exist
    base_dir = VISUALIZATIONS_DIR
    base_dir.mkdir(parents=True, exist_ok=True)

    # Determine the appropriate directory based on model type
    if model_type == 'lstm':
        # Terminal 2: LSTM
        model_dir = base_dir / 'lstm'
    elif model_type == 'tft':
        # Terminal 3: TFT
        model_dir = base_dir / 'tft'
    elif model_type == 'arima':
        # Terminal 1: ARIMA
        model_dir = base_dir / 'arima'
    elif model_type in ['lstm_arima', 'tft_arima']:
        # Terminal 4 & 5: Ensemble models
        model_dir = base_dir / 'ensemble'
    else:
        # Default case for unknown model types
        model_dir = base_dir / model_type

    # Create directory structure
    symbol_tf_dir = model_dir / f"{symbol}_{timeframe}"
    symbol_tf_dir.mkdir(parents=True, exist_ok=True)

    # Return standardized path
    return str(symbol_tf_dir / f"{visualization_type}.png")

def get_metadata_path(data_path: Union[str, Path]) -> str:
    """
    Get the metadata path for a data file.

    Args:
        data_path: Path to the data file (string or Path object)

    Returns:
        str: Path to the metadata file
    """
    # Validate input
    if not data_path:
        raise ValueError("Data path cannot be empty")

    # Convert to string if it's a Path object
    path_str = str(data_path)

    # Validate that it's a parquet file
    if not path_str.endswith('.parquet'):
        logger.warning(f"Expected a parquet file path, got: {path_str}")

    # Replace extension with _metadata.json
    return path_str.replace('.parquet', '_metadata.json')

def get_terminal_model_type(terminal_id: int) -> str:
    """
    Get the model type for a specific terminal.

    Args:
        terminal_id: Terminal ID (1-5)

    Returns:
        str: Model type for the terminal
    """
    if terminal_id not in VALID_TERMINAL_IDS:
        logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")
        return 'lstm'  # Default to LSTM if invalid terminal ID

    return TERMINAL_MODEL_TYPES.get(terminal_id, 'lstm')

def get_terminal_for_model_type(model_type: str) -> int:
    """
    Get the terminal ID for a specific model type.

    Args:
        model_type: Model type

    Returns:
        int: Terminal ID for the model type
    """
    for terminal_id, terminal_model_type in TERMINAL_MODEL_TYPES.items():
        if terminal_model_type == model_type:
            return terminal_id

    return 2  # Default to Terminal 2 (LSTM) if model_type not found

def get_model_type_description(model_type: str) -> str:
    """
    Get a description for a model type.

    Args:
        model_type: Model type

    Returns:
        str: Description of the model type
    """
    return MODEL_TYPE_DESCRIPTIONS.get(model_type, f'Unknown model type: {model_type}')

def get_model_file_extension(model_type: str) -> str:
    """
    Get the file extension for a model type.

    Args:
        model_type: Model type

    Returns:
        str: File extension for the model type
    """
    return MODEL_FILE_EXTENSIONS.get(model_type, 'pth')  # Default to .pth if model_type not found

def get_model_components(model_type: str) -> List[str]:
    """
    Get the component model types for an ensemble model.

    Args:
        model_type: Model type

    Returns:
        List[str]: List of component model types
    """
    return ENSEMBLE_COMPONENTS.get(model_type, [model_type])  # Return the model type itself if not an ensemble

def is_ensemble_model(model_type: str) -> bool:
    """
    Check if a model type is an ensemble model.

    Args:
        model_type: Model type

    Returns:
        bool: True if the model type is an ensemble model, False otherwise
    """
    return model_type in ENSEMBLE_COMPONENTS

def get_terminal_data_path(
    symbol: str,
    timeframe: str,
    terminal_id: int
) -> str:
    """
    Get the path for a specific terminal's data directory.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID

    Returns:
        str: Path to the terminal data directory
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Validate terminal ID
    if terminal_id not in VALID_TERMINAL_IDS:
        logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")

    # Format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}
    base_dir = HISTORICAL_DATA_DIR / f"terminal_{terminal_id}"
    symbol_timeframe_dir = base_dir / f"{symbol}_{timeframe}"

    # Create directory if it doesn't exist
    symbol_timeframe_dir.mkdir(parents=True, exist_ok=True)

    return str(symbol_timeframe_dir)

def get_terminal_data_paths(
    symbol: str,
    timeframe: str,
    terminal_ids: Optional[List[int]] = None
) -> List[str]:
    """
    Get paths for all terminal data files.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_ids: List of terminal IDs (default: [1, 2, 3, 4, 5])

    Returns:
        List[str]: List of paths to terminal data files
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Use default terminal IDs if none provided
    if terminal_ids is None:
        terminal_ids = VALID_TERMINAL_IDS

    # Validate terminal IDs
    for terminal_id in terminal_ids:
        if terminal_id not in VALID_TERMINAL_IDS:
            logger.warning(f"Terminal ID {terminal_id} is not in the valid range {VALID_TERMINAL_IDS}")

    # Generate paths for each terminal
    return [
        get_historical_data_path(symbol, timeframe, terminal_id, create_dirs=False)
        for terminal_id in terminal_ids
    ]
