import os
import logging
import traceback
from datetime import datetime
from typing import Optional, Dict, List
import pandas as pd
import json
from utils.path_utils import (
    get_historical_data_path,
    get_features_path,
    get_metadata_path
)

# Setup logger
logger = logging.getLogger(__name__)

class DataManager:
    """
    Data Manager class for handling historical data storage and retrieval.

    This class provides methods for:
    - Saving historical data to Parquet files
    - Loading historical data from Parquet files
    - Managing data versions
    - Automatic data saving
    """

    def __init__(self, storage_dir: str = "data/storage"):
        """
        Initialize the DataManager.

        Args:
            storage_dir: Directory for storing data files
        """
        self.storage_dir = storage_dir
        self.ensure_storage_dir_exists()

        # Cache for loaded data
        self.data_cache = {}

    def ensure_storage_dir_exists(self):
        """Ensure the storage directory exists."""
        os.makedirs(self.storage_dir, exist_ok=True)

        # Create subdirectories for different data types
        os.makedirs(os.path.join(self.storage_dir, "historical"), exist_ok=True)
        os.makedirs(os.path.join(self.storage_dir, "features"), exist_ok=True)
        os.makedirs(os.path.join(self.storage_dir, "models"), exist_ok=True)

    def get_data_path(self, symbol: str, timeframe: str, data_type: str = "historical", terminal_id: Optional[int] = None) -> str:
        """
        Get the path for a data file.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
            data_type: Type of data ('historical', 'features', 'models')
            terminal_id: Terminal ID (optional)

        Returns:
            str: Path to the data file
        """
        # Use standardized path utilities based on data_type
        if data_type == "historical":
            # Get the directory path without the filename
            path = get_historical_data_path(symbol, timeframe, terminal_id, create_dirs=False)
            return os.path.dirname(path)
        elif data_type == "features":
            # Get the directory path without the filename
            path = get_features_path(symbol, timeframe, terminal_id, create_dirs=False)
            return os.path.dirname(path)
        elif data_type == "models":
            # Use model path utilities from path_utils.py
            from utils.path_utils import get_model_path, get_terminal_model_type
            model_type = None
            if terminal_id is not None:
                # Get model type based on terminal_id
                model_type = get_terminal_model_type(terminal_id)

            # Get the directory path without the filename
            path = get_model_path(symbol, timeframe, model_type, terminal_id)
            return os.path.dirname(path)
        else:
            # For other data types, use the old approach
            if terminal_id is not None:
                # Standardized path with terminal_id
                return os.path.join(self.storage_dir, data_type, f"terminal_{terminal_id}", f"{symbol}_{timeframe}")
            else:
                # Path for merged data
                return os.path.join(self.storage_dir, data_type, f"{symbol}_{timeframe}")

    def save_historical_data(self,
                           df: pd.DataFrame,
                           symbol: str,
                           timeframe: str,
                           start_date: str,
                           end_date: Optional[str] = None,
                           terminal_id: Optional[int] = None,
                           metadata: Optional[Dict] = None) -> str:
        """
        Save historical data to a Parquet file.
        Following the required format:
        data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet

        Args:
            df: DataFrame with historical data
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format (optional)
            terminal_id: Terminal ID (optional)
            metadata: Additional metadata to store with the data (optional)

        Returns:
            str: Path to the saved file
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")

        # Format dates for filename (YYYYMMDD)
        start_date_fmt = start_date.replace('-', '')
        end_date_fmt = end_date.replace('-', '')

        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

        # Get standardized path for historical data
        # Following the required format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
        filepath = get_historical_data_path(
            symbol=symbol,
            timeframe=timeframe,
            terminal_id=terminal_id,
            start_date=start_date_fmt,
            end_date=end_date_fmt
        )

        # Ensure directory exists
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Check for duplicate data before saving
        if self._check_for_duplicates(df, symbol, timeframe, terminal_id):
            logger.warning(f"Duplicate data detected for {symbol} ({timeframe}). Merging with existing data.")
            df = self._merge_with_existing_data(df, symbol, timeframe, terminal_id)

        # Validate data before saving
        if not self._validate_data(df, symbol, timeframe):
            logger.error(f"Data validation failed for {symbol} ({timeframe}). Data will not be saved.")
            return ""

        # Prepare metadata
        file_metadata = {
            'symbol': symbol,
            'timeframe': timeframe,
            'start_date': start_date,
            'end_date': end_date,
            'terminal_id': terminal_id,
            'created_at': timestamp,
            'rows': len(df),
            'columns': list(df.columns),
        }

        # Add custom metadata if provided
        if metadata:
            file_metadata.update(metadata)

        # Save to Parquet with metadata
        df.to_parquet(filepath, engine='pyarrow', compression='snappy')

        # Save metadata separately for easier access
        metadata_filepath = filepath.replace('.parquet', '_metadata.json')
        with open(metadata_filepath, 'w') as f:
            json.dump(file_metadata, f, indent=2)

        # Update latest file with standardized naming
        base_dir = os.path.dirname(filepath)

        # Create a standardized latest file name
        # Format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/latest.parquet
        latest_path = os.path.join(base_dir, "latest.parquet")

        if os.path.exists(latest_path):
            try:
                os.remove(latest_path)
            except Exception as e:
                logger.warning(f"Could not remove old latest file: {e}")

        # On Windows, we can't create symlinks easily, so we'll just copy the file
        try:
            df.to_parquet(latest_path, engine='pyarrow', compression='snappy')

            # Save latest metadata with standardized naming
            latest_metadata_filepath = os.path.join(base_dir, "latest_metadata.json")
            with open(latest_metadata_filepath, 'w') as f:
                json.dump(file_metadata, f, indent=2)

            logger.info(f"Updated latest file at {latest_path}")

        except Exception as e:
            logger.error(f"Failed to create latest file: {e}")

        logger.info(f"Saved historical data to {filepath} with metadata")
        return filepath

    def _check_for_duplicates(self, df: pd.DataFrame, symbol: str, timeframe: str, terminal_id: Optional[int] = None) -> bool:
        """
        Check if the data contains duplicates with existing data.

        Args:
            df: DataFrame with historical data
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            terminal_id: Terminal ID (optional)

        Returns:
            bool: True if duplicates found, False otherwise
        """
        try:
            # Get the latest data for comparison
            latest_data = self.load_historical_data(symbol, timeframe, terminal_id=terminal_id)

            if latest_data is None or len(latest_data) == 0:
                # No existing data, so no duplicates
                return False

            # Check for overlapping date ranges
            if not isinstance(df.index, pd.DatetimeIndex) or not isinstance(latest_data.index, pd.DatetimeIndex):
                logger.debug("Cannot check for duplicates: index is not a DatetimeIndex") # Changed to debug level
                return False

            # Get date range of new data
            new_start = df.index.min()
            new_end = df.index.max()

            # Get date range of existing data
            existing_start = latest_data.index.min()
            existing_end = latest_data.index.max()

            # Check for overlap
            if new_end >= existing_start and new_start <= existing_end:
                # There is an overlap
                overlap_start = max(new_start, existing_start)
                overlap_end = min(new_end, existing_end)

                # Calculate overlap percentage
                overlap_days = (overlap_end - overlap_start).days + 1
                new_days = (new_end - new_start).days + 1
                overlap_percentage = (overlap_days / new_days) * 100

                logger.debug(f"Data overlap detected: {overlap_percentage:.2f}% of new data overlaps with existing data")

                # Only consider it a duplicate if there's significant overlap (>95%)
                # This reduces unnecessary processing for minor overlaps
                return overlap_percentage > 95

            return False

        except Exception as e:
            logger.error(f"Error checking for duplicates: {str(e)}")
            logger.debug(traceback.format_exc())
            return False

    def _merge_with_existing_data(self, df: pd.DataFrame, symbol: str, timeframe: str, terminal_id: Optional[int] = None) -> pd.DataFrame:
        """
        Merge new data with existing data, removing duplicates.

        Args:
            df: DataFrame with new historical data
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            terminal_id: Terminal ID (optional)

        Returns:
            pd.DataFrame: Merged data
        """
        try:
            # Get the latest data for merging
            latest_data = self.load_historical_data(symbol, timeframe, terminal_id=terminal_id)

            if latest_data is None or len(latest_data) == 0:
                # No existing data, return new data
                return df

            # Ensure both DataFrames have DatetimeIndex
            if not isinstance(df.index, pd.DatetimeIndex) or not isinstance(latest_data.index, pd.DatetimeIndex):
                logger.debug("Cannot merge data: index is not a DatetimeIndex") # Changed to debug level
                return df

            # Combine the data
            combined = pd.concat([latest_data, df])

            # Remove duplicates, keeping the newer data
            combined = combined[~combined.index.duplicated(keep='last')]

            # Sort by date
            combined = combined.sort_index()

            logger.info(f"Merged data: {len(latest_data)} existing rows + {len(df)} new rows = {len(combined)} total rows after deduplication")

            return combined

        except Exception as e:
            logger.error(f"Error merging data: {str(e)}")
            logger.debug(traceback.format_exc())
            return df

    def _validate_data(self, df: pd.DataFrame, symbol: str, timeframe: str) -> bool:
        """
        Validate data before saving.

        Args:
            df: DataFrame with historical data
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')

        Returns:
            bool: True if data is valid, False otherwise
        """
        try:
            # Check if DataFrame is empty
            if df is None or len(df) == 0:
                logger.error("Data validation failed: DataFrame is empty")
                return False

            # Check if index is DatetimeIndex, if not, try to convert it
            if not isinstance(df.index, pd.DatetimeIndex):
                logger.debug("Index is not a DatetimeIndex, attempting to convert...") # Changed to debug level since this is expected
                try:
                    # Check if 'time' column exists and convert
                    if 'time' in df.columns and pd.api.types.is_datetime64_any_dtype(df['time']):
                        logger.info("Converting 'time' column to DatetimeIndex")
                        df = df.set_index('time')
                    else:
                        # Try to convert the existing index to datetime
                        logger.info("Attempting to convert existing index to DatetimeIndex")
                        df.index = pd.to_datetime(df.index)

                    # Verify conversion was successful
                    if not isinstance(df.index, pd.DatetimeIndex):
                        logger.error("Data validation failed: Could not convert index to DatetimeIndex")
                        return False

                    logger.info("Successfully converted index to DatetimeIndex")
                except Exception as e:
                    logger.error(f"Data validation failed: Could not convert index to DatetimeIndex - {str(e)}")
                    return False

            # Check for required columns
            required_columns = ['open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"Data validation failed: Missing required columns: {missing_columns}")
                return False

            # Check for NaN values in required columns
            for col in required_columns:
                if df[col].isnull().any():
                    nan_count = df[col].isnull().sum()
                    nan_percentage = (nan_count / len(df)) * 100

                    if nan_percentage > 1:  # Allow up to 1% NaN values
                        logger.error(f"Data validation failed: {nan_count} NaN values ({nan_percentage:.2f}%) in column '{col}'")
                        return False
                    else:
                        logger.warning(f"Data contains {nan_count} NaN values ({nan_percentage:.2f}%) in column '{col}', but within acceptable limit")

            # Check for correct timeframe (with improved weekend/gap handling)
            if len(df) > 1:
                # Calculate time differences for multiple consecutive rows to handle weekend gaps
                time_diffs = []
                for i in range(1, min(10, len(df))):  # Check up to 10 consecutive rows
                    time_diff = (df.index[i] - df.index[i-1]).total_seconds() / 60
                    time_diffs.append(time_diff)

                expected_diff = {
                    'M1': 1,
                    'M5': 5,
                    'M15': 15,
                    'M30': 30,
                    'H1': 60,
                    'H4': 240,
                    'D1': 1440
                }

                if timeframe in expected_diff:
                    expected = expected_diff[timeframe]

                    # Count how many time differences match the expected timeframe
                    valid_diffs = 0
                    for time_diff in time_diffs:
                        tolerance = expected * 0.5  # Increased tolerance to 50%
                        if abs(time_diff - expected) <= tolerance:
                            valid_diffs += 1
                        # Also accept weekend gaps (multiples of expected timeframe)
                        elif time_diff > expected and time_diff % expected == 0:
                            valid_diffs += 1

                    # Require at least 70% of time differences to be valid
                    valid_ratio = valid_diffs / len(time_diffs)
                    if valid_ratio < 0.7:
                        logger.warning(f"Data validation warning: Only {valid_ratio:.1%} of time differences match expected timeframe {timeframe} ({expected} minutes). This may indicate data quality issues but training will continue.")
                        # Don't return False - just log a warning and continue

            # All checks passed
            logger.info(f"Data validation passed for {symbol} ({timeframe}): {len(df)} rows")
            return True

        except Exception as e:
            logger.error(f"Error validating data: {str(e)}")
            logger.debug(traceback.format_exc())
            return False

    def load_historical_data(self,
                           symbol: str,
                           timeframe: str,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           version: Optional[str] = None,
                           terminal_id: Optional[int] = None,
                           merged: bool = False) -> Optional[pd.DataFrame]:
        """
        Load historical data from a Parquet file.
        Following the standardized path structure:
        data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{start_date}_{end_date}_terminal_{terminal_id}_{timestamp}.parquet
        or for latest data:
        data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/latest.parquet

        Args:
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            start_date: Start date in 'YYYY-MM-DD' format (optional)
            end_date: End date in 'YYYY-MM-DD' format (optional)
            version: Specific version to load (optional)
            terminal_id: Terminal ID (optional)
            merged: Whether to load merged data from all terminals (optional)

        Returns:
            pd.DataFrame: Historical data or None if not found
        """
        # If merged is True, ignore terminal_id
        if merged:
            terminal_id = None
            logger.info(f"Loading merged data for {symbol} ({timeframe})")
        else:
            logger.info(f"Loading data for {symbol} ({timeframe}) from terminal {terminal_id}")

        # Get the standardized path for historical data
        if version:
            # If version is specified, construct the path with the version
            if terminal_id is not None:
                # Format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/{version}.parquet
                base_dir = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}"
                filepath = f"{base_dir}/{version}.parquet"
            else:
                # Format: data/storage/historical/merged/{symbol}_{timeframe}/{version}.parquet
                base_dir = f"data/storage/historical/merged/{symbol}_{timeframe}"
                filepath = f"{base_dir}/{version}.parquet"
        else:
            # Otherwise, get the path for the latest file
            if terminal_id is not None:
                # Format: data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}/latest.parquet
                base_dir = f"data/storage/historical/terminal_{terminal_id}/{symbol}_{timeframe}"
                filepath = f"{base_dir}/latest.parquet"
            else:
                # Format: data/storage/historical/merged/{symbol}_{timeframe}/latest.parquet
                base_dir = f"data/storage/historical/merged/{symbol}_{timeframe}"
                filepath = f"{base_dir}/latest.parquet"

        # Check if the file exists
        if not os.path.exists(filepath):
            logger.debug(f"No data file found at {filepath}") # Changed to debug level since this is expected
            return None

        try:
            # Load the data
            df = pd.read_parquet(filepath)

            # Load metadata if available
            metadata_filepath = get_metadata_path(filepath)
            if os.path.exists(metadata_filepath):
                with open(metadata_filepath, 'r') as f:
                    metadata = json.load(f)
                    logger.info(f"Loaded metadata: {metadata}")

            logger.info(f"Loaded historical data from {filepath}")

            # Filter by date if specified
            if start_date or end_date:
                # Ensure the index is a proper datetime index
                if not isinstance(df.index, pd.DatetimeIndex):
                    # Try to convert the index to datetime if it's not already
                    try:
                        if 'time' in df.columns:
                            df = df.set_index('time')
                        else:
                            # If index is not datetime, try to convert it
                            df.index = pd.to_datetime(df.index)
                    except Exception as e:
                        logger.warning(f"Could not convert index to datetime: {e}")
                        # If conversion fails, skip date filtering
                        return df

                # Now apply date filtering
                if start_date:
                    start_ts = pd.Timestamp(start_date)
                    df = df[df.index >= start_ts]
                if end_date:
                    end_ts = pd.Timestamp(end_date)
                    df = df[df.index <= end_ts]

            return df

        except Exception as e:
            logger.error(f"Failed to load historical data from {filepath}: {e}")
            logger.debug(traceback.format_exc())
            return None

    def list_available_data(self, symbol: str, timeframe: str) -> List[str]:
        """
        List available data versions for a symbol and timeframe.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')

        Returns:
            List[str]: List of available versions
        """
        base_path = self.get_data_path(symbol, timeframe)
        if not os.path.exists(base_path):
            return []

        files = [f for f in os.listdir(base_path) if f.endswith('.parquet') and f != 'latest.parquet']
        return sorted(files)

    def save_features(self,
                    df: pd.DataFrame,
                    symbol: str,
                    timeframe: str,
                    terminal_id: Optional[int] = None,
                    metadata: Optional[Dict] = None) -> str:
        """
        Save feature data to a Parquet file.

        Args:
            df: DataFrame with features
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            terminal_id: Terminal ID (optional)
            metadata: Additional metadata to store with the data (optional)

        Returns:
            str: Path to the saved file
        """
        # Create directory if it doesn't exist
        base_path = self.get_data_path(symbol, timeframe, data_type="features", terminal_id=terminal_id)
        os.makedirs(base_path, exist_ok=True)

        # Create filename with version timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        terminal_suffix = f"_terminal_{terminal_id}" if terminal_id is not None else ""
        filename = f"features{terminal_suffix}_{timestamp}.parquet"
        filepath = os.path.join(base_path, filename)

        # Check for duplicate data before saving
        if self._check_for_duplicates(df, symbol, timeframe, terminal_id):
            logger.warning(f"Duplicate feature data detected for {symbol} ({timeframe}). Merging with existing data.")
            df = self._merge_with_existing_data(df, symbol, timeframe, terminal_id)

        # Validate data before saving
        if not self._validate_data(df, symbol, timeframe):
            logger.error(f"Data validation failed for {symbol} ({timeframe}). Data will not be saved.")
            return ""

        # Prepare metadata
        file_metadata = {
            'symbol': symbol,
            'timeframe': timeframe,
            'terminal_id': terminal_id,
            'created_at': timestamp,
            'rows': len(df),
            'columns': list(df.columns),
            'feature_count': len([col for col in df.columns if col not in ['open', 'high', 'low', 'close', 'volume']])
        }

        # Add custom metadata if provided
        if metadata:
            file_metadata.update(metadata)

        # Save to Parquet
        df.to_parquet(filepath, engine='pyarrow', compression='snappy')

        # Save metadata separately for easier access
        metadata_filepath = filepath.replace('.parquet', '_metadata.json')
        import json
        with open(metadata_filepath, 'w') as f:
            json.dump(file_metadata, f, indent=2)

        # Update latest symlink
        latest_path = os.path.join(base_path, "latest.parquet")
        if os.path.exists(latest_path):
            try:
                os.remove(latest_path)
            except Exception as e:
                logger.warning(f"Could not remove old latest symlink: {e}")

        # On Windows, we can't create symlinks easily, so we'll just copy the file
        try:
            df.to_parquet(latest_path, engine='pyarrow', compression='snappy')

            # Save latest metadata
            latest_metadata_filepath = os.path.join(base_path, "latest_metadata.json")
            with open(latest_metadata_filepath, 'w') as f:
                json.dump(file_metadata, f, indent=2)

        except Exception as e:
            logger.error(f"Failed to create latest file: {e}")

        logger.info(f"Saved feature data to {filepath} with metadata")
        return filepath

    def load_features(self,
                    symbol: str,
                    timeframe: str,
                    version: Optional[str] = None,
                    terminal_id: Optional[int] = None) -> Optional[pd.DataFrame]:
        """
        Load feature data from a Parquet file.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30')
            version: Specific version to load (optional)
            terminal_id: Terminal ID (optional)

        Returns:
            pd.DataFrame: Feature data or None if not found
        """
        base_path = self.get_data_path(symbol, timeframe, data_type="features", terminal_id=terminal_id)

        if not os.path.exists(base_path):
            logger.warning(f"No feature data directory found for {symbol} ({timeframe})")
            return None

        # If version is specified, load that specific file
        if version:
            filepath = os.path.join(base_path, f"{version}.parquet")
            if os.path.exists(filepath):
                try:
                    df = pd.read_parquet(filepath)

                    # Load metadata if available
                    metadata_filepath = filepath.replace('.parquet', '_metadata.json')
                    if os.path.exists(metadata_filepath):
                        import json
                        with open(metadata_filepath, 'r') as f:
                            metadata = json.load(f)
                            logger.info(f"Loaded feature metadata: {metadata}")

                    logger.info(f"Loaded feature data from {filepath}")
                    return df
                except Exception as e:
                    logger.error(f"Failed to load feature data from {filepath}: {e}")
                    return None
            else:
                logger.error(f"Version {version} not found for {symbol} ({timeframe})")
                return None

        # Otherwise, load the latest file
        filepath = os.path.join(base_path, "latest.parquet")
        if os.path.exists(filepath):
            try:
                df = pd.read_parquet(filepath)

                # Load metadata if available
                metadata_filepath = os.path.join(base_path, "latest_metadata.json")
                if os.path.exists(metadata_filepath):
                    import json
                    with open(metadata_filepath, 'r') as f:
                        metadata = json.load(f)
                        logger.info(f"Loaded feature metadata: {metadata}")

                logger.info(f"Loaded feature data from {filepath}")
                return df
            except Exception as e:
                logger.error(f"Failed to load feature data from {filepath}: {e}")
                return None
        else:
            logger.error(f"No feature data found for {symbol} ({timeframe})")
            return None

    def clear_cache(self):
        """Clear the data cache."""
        self.data_cache = {}
        logger.info("Data cache cleared")

    def load_data(self, path: str) -> Optional[pd.DataFrame]:
        """
        Load data from a specific path.

        Args:
            path: Path to the data file

        Returns:
            pd.DataFrame: Data or None if not found
        """
        if not os.path.exists(path):
            logger.error(f"No data file found at {path}")
            return None

        try:
            # Load the data
            df = pd.read_parquet(path)
            logger.info(f"Loaded data from {path}")
            return df
        except Exception as e:
            logger.error(f"Failed to load data from {path}: {e}")
            logger.debug(traceback.format_exc())
            return None

    def save_data(self, df: pd.DataFrame, path: str, metadata: Optional[Dict] = None) -> bool:
        """
        Save data to a specific path.

        Args:
            df: DataFrame to save
            path: Path to save the data to
            metadata: Additional metadata to store with the data (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(path), exist_ok=True)

            # Save to Parquet
            df.to_parquet(path, engine='pyarrow', compression='snappy')

            # Save metadata if provided
            if metadata:
                metadata_path = path.replace('.parquet', '_metadata.json')
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f, indent=2)

            logger.info(f"Saved data to {path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save data to {path}: {e}")
            logger.debug(traceback.format_exc())
            return False

    def get_storage_info(self) -> Dict:
        """
        Get information about stored data.

        Returns:
            Dict: Information about stored data
        """
        info = {
            "historical": {},
            "features": {},
            "models": {}
        }

        # Get information about historical data
        historical_dir = os.path.join(self.storage_dir, "historical")
        if os.path.exists(historical_dir):
            for item in os.listdir(historical_dir):
                item_path = os.path.join(historical_dir, item)
                if os.path.isdir(item_path):
                    files = [f for f in os.listdir(item_path) if f.endswith('.parquet') and f != 'latest.parquet']
                    info["historical"][item] = {
                        "versions": len(files),
                        "latest_update": max([os.path.getmtime(os.path.join(item_path, f)) for f in files]) if files else None
                    }

        # Get information about feature data
        features_dir = os.path.join(self.storage_dir, "features")
        if os.path.exists(features_dir):
            for item in os.listdir(features_dir):
                item_path = os.path.join(features_dir, item)
                if os.path.isdir(item_path):
                    files = [f for f in os.listdir(item_path) if f.endswith('.parquet') and f != 'latest.parquet']
                    info["features"][item] = {
                        "versions": len(files),
                        "latest_update": max([os.path.getmtime(os.path.join(item_path, f)) for f in files]) if files else None
                    }

        return info
