"""
Test script for end-to-end workflow.
"""
import sys
import logging
import os
from datetime import datetime, timedelta
import torch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append('.')

# Import the trading bot components
from trading.terminal_manager import TerminalManager
from model.lstm_model import TradingLSTM
from trading.mt5_connector import MT5Connector
from data.data_collector import DataCollector
from trading.trading_strategy import TradingStrategy
from analysis.feature_engineering import FeatureEngineer
from model.model_manager import ModelManager
from trading.order_manager import OrderManager
from model.model_evaluator import ModelEvaluator

def test_end_to_end_workflow():
    """Test the end-to-end workflow."""
    logger.info("Testing end-to-end workflow...")

    # Step 1: Initialize terminals
    logger.info("Step 1: Initializing terminals...")
    manager = TerminalManager()

    if not manager.initialize_terminals():
        logger.error("Failed to initialize terminals")
        return False

    logger.info("Terminals initialized successfully")

    # Step 2: Connect to Terminal 3 (IC Markets)
    logger.info("Step 2: Connecting to Terminal 3 (IC Markets)...")
    connector = MT5Connector()

    if not connector.connect_terminal(3):
        logger.error("Failed to connect to Terminal 3")
        manager.shutdown_terminals()
        return False

    logger.info("Connected to Terminal 3 successfully")

    # Step 3: Collect historical data
    logger.info("Step 3: Collecting historical data...")
    collector = DataCollector(connector)

    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = datetime.now().strftime('%Y-%m-%d')

    df = collector.get_historical_data(
        timeframe='M5',
        start_date=start_date,
        end_date=end_date,
        use_cache=False
    )

    if df is None:
        logger.error("Failed to collect historical data")
        connector.disconnect_terminal()
        manager.shutdown_terminals()
        return False

    logger.info(f"Collected {len(df)} M5 bars")

    # Step 4: Process data
    logger.info("Step 4: Processing data...")
    feature_engineer = FeatureEngineer()

    processed_data = feature_engineer.create_features(df)

    if processed_data is None:
        logger.error("Failed to process data")
        connector.disconnect_terminal()
        manager.shutdown_terminals()
        return False

    logger.info(f"Processed {len(processed_data)} samples")

    # Step 5: Load model
    logger.info("Step 5: Loading model...")

    try:
        # Add TradingLSTM to safe globals for PyTorch 2.6+ security model
        torch.serialization.add_safe_globals([TradingLSTM])

        # Check if the model file exists
        model_path = 'model/saved_models/BTCUSD.a_M5.pth'
        if not os.path.exists(model_path):
            logger.error(f"Model file not found: {model_path}")
            connector.disconnect_terminal()
            manager.shutdown_terminals()
            return False

        # Load the model
        model_data = torch.load(model_path)

        # Create a new model
        input_size = model_data.get('input_size', 32)
        hidden_size = model_data.get('hidden_size', 64)
        num_layers = model_data.get('num_layers', 2)
        output_size = model_data.get('output_size', 1)

        model = TradingLSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size
        )

        # Load the model state dict
        model.load_state_dict(model_data['model_state_dict'])

        logger.info("Model loaded successfully")
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        connector.disconnect_terminal()
        manager.shutdown_terminals()
        return False

    # Step 6: Initialize trading strategy components
    logger.info("Step 6: Initializing trading strategy components...")
    order_manager = OrderManager(connector)
    model_manager = ModelManager()
    model_evaluator = ModelEvaluator()

    # Initialize trading strategy
    logger.info("Initializing trading strategy...")
    strategy = TradingStrategy(
        mt5_connector=connector,
        order_manager=order_manager,
        model_manager=model_manager,
        data_collector=collector,
        feature_engineer=feature_engineer,
        model_evaluator=model_evaluator
    )

    # Step 7: Get current market data
    logger.info("Step 7: Getting current market data...")
    current_data = collector.get_latest_data('M5', 100)

    if current_data is None:
        logger.error("Failed to get current market data")
        connector.disconnect_terminal()
        manager.shutdown_terminals()
        return False

    logger.info(f"Got {len(current_data)} latest M5 bars")

    # Step 8: Make prediction
    logger.info("Step 8: Making prediction...")
    signals = strategy.generate_signals()

    logger.info(f"Trading signals: {signals}")

    # Step 9: Clean up
    logger.info("Step 9: Cleaning up...")
    connector.disconnect_terminal()
    manager.shutdown_terminals()

    logger.info("End-to-end workflow completed successfully")
    return True

def main():
    """Main function."""
    logger.info("Running end-to-end workflow test...")

    # Test the end-to-end workflow
    if test_end_to_end_workflow():
        logger.info("End-to-end workflow test passed")
        return 0
    else:
        logger.error("End-to-end workflow test failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
