"""
Model Path Utilities Module.
DEPRECATED: This module is deprecated. Please use utils.path_utils instead.
This module now imports from utils.path_utils to maintain backward compatibility.
"""
import sys
import os
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

# Add the project root to the Python path to allow importing from config
sys.path.append(str(Path(__file__).parent.parent.absolute()))

# Import from utils.path_utils instead of defining here
from utils.path_utils import (
    TERMINAL_MODEL_TYPES,
    get_terminal_model_type,
    get_model_path,
    get_metrics_path,
    get_visualization_path,
    is_ensemble_model,
    get_model_components
)

# Configure logger
logger = logging.getLogger('model.model_path_utils')

# Show deprecation warning
logger.warning("DEPRECATED: model.model_path_utils is deprecated. Please use utils.path_utils instead.")

def validate_path_format(path: Union[str, Path], expected_format: str) -> <PERSON>ple[bool, str]:
    """
    Validate that a path follows the expected format.

    Args:
        path: Path to validate
        expected_format: Expected format pattern (regex)

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    path_str = str(path)

    # Check if path matches the expected format
    if re.match(expected_format, path_str):
        return True, ""
    else:
        error_msg = f"Path '{path_str}' does not match expected format '{expected_format}'"
        logger.warning(error_msg)
        return False, error_msg

def validate_model_path(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate that a path follows the model path format.

    Args:
        path: Path to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    # Expected formats:
    # model/saved_models/lstm/{symbol}_{timeframe}_lstm.pth
    # model/saved_models/tft/{symbol}_{timeframe}_tft.pth
    # model/saved_models/arima/{symbol}_{timeframe}_arima.pkl
    # model/saved_models/ensemble/{symbol}_{timeframe}_{model_type}.pkl

    lstm_pattern = r'model/saved_models/lstm/[A-Za-z0-9]+_[A-Za-z0-9]+_lstm\.pth'
    tft_pattern = r'model/saved_models/tft/[A-Za-z0-9]+_[A-Za-z0-9]+_tft\.pth'
    arima_pattern = r'model/saved_models/arima/[A-Za-z0-9]+_[A-Za-z0-9]+_arima\.pkl'
    ensemble_pattern = r'model/saved_models/ensemble/[A-Za-z0-9]+_[A-Za-z0-9]+_(lstm_arima|tft_arima)\.pkl'

    # Check if path matches any of the patterns
    path_str = str(path).replace('\\', '/')
    if (re.match(lstm_pattern, path_str) or
        re.match(tft_pattern, path_str) or
        re.match(arima_pattern, path_str) or
        re.match(ensemble_pattern, path_str)):
        return True, ""
    else:
        error_msg = f"Model path '{path_str}' does not match expected format"
        logger.warning(error_msg)
        return False, error_msg

def validate_metrics_path(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate that a path follows the metrics path format.

    Args:
        path: Path to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    # Expected formats:
    # model/saved_models/lstm/{symbol}_{timeframe}_lstm_metrics.json
    # model/saved_models/tft/{symbol}_{timeframe}_tft_metrics.json
    # model/saved_models/arima/{symbol}_{timeframe}_arima_metrics.json
    # model/saved_models/ensemble/{symbol}_{timeframe}_{model_type}_metrics.json

    lstm_pattern = r'model/saved_models/lstm/[A-Za-z0-9]+_[A-Za-z0-9]+_lstm_metrics\.json'
    tft_pattern = r'model/saved_models/tft/[A-Za-z0-9]+_[A-Za-z0-9]+_tft_metrics\.json'
    arima_pattern = r'model/saved_models/arima/[A-Za-z0-9]+_[A-Za-z0-9]+_arima_metrics\.json'
    ensemble_pattern = r'model/saved_models/ensemble/[A-Za-z0-9]+_[A-Za-z0-9]+_(lstm_arima|tft_arima)_metrics\.json'

    # Check if path matches any of the patterns
    path_str = str(path).replace('\\', '/')
    if (re.match(lstm_pattern, path_str) or
        re.match(tft_pattern, path_str) or
        re.match(arima_pattern, path_str) or
        re.match(ensemble_pattern, path_str)):
        return True, ""
    else:
        error_msg = f"Metrics path '{path_str}' does not match expected format"
        logger.warning(error_msg)
        return False, error_msg

def validate_visualization_path(path: Union[str, Path]) -> Tuple[bool, str]:
    """
    Validate that a path follows the visualization path format.

    Args:
        path: Path to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    # Expected format: model/visualizations/{model_type}/{symbol}_{timeframe}/{visualization_type}.png

    pattern = r'model/visualizations/(lstm|tft|arima|ensemble)/[A-Za-z0-9]+_[A-Za-z0-9]+/[A-Za-z0-9_]+\.png'

    # Check if path matches the pattern
    path_str = str(path).replace('\\', '/')
    if re.match(pattern, path_str):
        return True, ""
    else:
        error_msg = f"Visualization path '{path_str}' does not match expected format"
        logger.warning(error_msg)
        return False, error_msg

# These functions are now imported from utils.path_utils
# The implementations below are kept for backward compatibility but will be removed in a future version

def get_all_terminal_model_types() -> Dict[int, str]:
    """
    Get all terminal model type assignments.
    DEPRECATED: Use TERMINAL_MODEL_TYPES from utils.path_utils instead.

    Returns:
        Dict[int, str]: Dictionary mapping terminal IDs to model types
    """
    logger.warning("DEPRECATED: get_all_terminal_model_types() is deprecated. Use TERMINAL_MODEL_TYPES from utils.path_utils instead.")
    return TERMINAL_MODEL_TYPES

def get_all_terminal_ids() -> List[int]:
    """
    Get all valid terminal IDs.
    DEPRECATED: Use list(TERMINAL_MODEL_TYPES.keys()) from utils.path_utils instead.

    Returns:
        List[int]: List of valid terminal IDs
    """
    logger.warning("DEPRECATED: get_all_terminal_ids() is deprecated. Use list(TERMINAL_MODEL_TYPES.keys()) from utils.path_utils instead.")
    return list(TERMINAL_MODEL_TYPES.keys())

def get_all_model_types() -> List[str]:
    """
    Get all valid model types.
    DEPRECATED: Use list(set(TERMINAL_MODEL_TYPES.values())) from utils.path_utils instead.

    Returns:
        List[str]: List of valid model types
    """
    logger.warning("DEPRECATED: get_all_model_types() is deprecated. Use list(set(TERMINAL_MODEL_TYPES.values())) from utils.path_utils instead.")
    return list(set(TERMINAL_MODEL_TYPES.values()))

def get_terminals_for_model_type(model_type: str) -> List[int]:
    """
    Get all terminals that use a specific model type.
    DEPRECATED: Use list comprehension with TERMINAL_MODEL_TYPES from utils.path_utils instead.

    Args:
        model_type: Model type ('arima', 'lstm', 'tft', 'lstm_arima', 'tft_arima')

    Returns:
        List[int]: List of terminal IDs
    """
    logger.warning("DEPRECATED: get_terminals_for_model_type() is deprecated. Use list comprehension with TERMINAL_MODEL_TYPES from utils.path_utils instead.")
    return [terminal_id for terminal_id, terminal_model_type in TERMINAL_MODEL_TYPES.items()
            if terminal_model_type == model_type]

def get_model_path(symbol: str, timeframe: str, model_type: str = None, terminal_id: Optional[int] = None) -> str:
    """
    Get the standardized path for a model file.
    DEPRECATED: Use get_model_path from utils.path_utils instead.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        terminal_id: Terminal ID (optional)

    Returns:
        str: Path to the model file
    """
    logger.warning("DEPRECATED: get_model_path() is deprecated. Use get_model_path from utils.path_utils instead.")
    # Use the imported function from utils.path_utils
    from utils.path_utils import get_model_path as utils_get_model_path
    return utils_get_model_path(symbol, timeframe, model_type, terminal_id)

def get_metrics_path(symbol: str, timeframe: str, model_type: str = None, terminal_id: Optional[int] = None) -> str:
    """
    Get the standardized path for a model metrics file.
    DEPRECATED: Use get_metrics_path from utils.path_utils instead.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        terminal_id: Terminal ID (optional)

    Returns:
        str: Path to the metrics file
    """
    logger.warning("DEPRECATED: get_metrics_path() is deprecated. Use get_metrics_path from utils.path_utils instead.")
    # Use the imported function from utils.path_utils
    from utils.path_utils import get_metrics_path as utils_get_metrics_path
    return utils_get_metrics_path(symbol, timeframe, model_type, terminal_id)

def get_visualization_path(symbol: str, timeframe: str, model_type: str = None, visualization_type: str = "predictions", terminal_id: Optional[int] = None) -> str:
    """
    Get the standardized path for a model visualization file.
    DEPRECATED: Use get_visualization_path from utils.path_utils instead.

    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        model_type: Type of model ('lstm', 'tft', 'arima', 'lstm_arima', 'tft_arima')
        visualization_type: Type of visualization (e.g., 'predictions', 'feature_importance')
        terminal_id: Terminal ID (optional)

    Returns:
        str: Path to the visualization file
    """
    logger.warning("DEPRECATED: get_visualization_path() is deprecated. Use get_visualization_path from utils.path_utils instead.")
    # Use the imported function from utils.path_utils
    from utils.path_utils import get_visualization_path as utils_get_visualization_path
    return utils_get_visualization_path(symbol, timeframe, model_type, visualization_type, terminal_id)

def get_terminal_data_path(symbol: str, timeframe: str, terminal_id: int) -> str:
    """
    Get the path to the historical data directory for a specific symbol, timeframe, and terminal.
    DEPRECATED: Use utils.path_utils.get_historical_data_path instead.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        str: Path to the historical data directory
    """
    logger.warning("DEPRECATED: get_terminal_data_path() is deprecated. Use utils.path_utils.get_historical_data_path instead.")
    # Use the imported function from utils.path_utils
    from utils.path_utils import get_historical_data_path
    # Get the directory path without the filename
    path = get_historical_data_path(symbol, timeframe, terminal_id, create_dirs=False)
    return os.path.dirname(path)

def get_terminal_features_path(symbol: str, timeframe: str, terminal_id: int) -> str:
    """
    Get the path to the features directory for a specific symbol, timeframe, and terminal.
    DEPRECATED: Use utils.path_utils.get_features_path instead.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        str: Path to the features directory
    """
    logger.warning("DEPRECATED: get_terminal_features_path() is deprecated. Use utils.path_utils.get_features_path instead.")
    # Use the imported function from utils.path_utils
    from utils.path_utils import get_features_path
    # Get the directory path without the filename
    path = get_features_path(symbol, timeframe, terminal_id, create_dirs=False)
    return os.path.dirname(path)

def get_terminal_model_path(symbol: str, timeframe: str, terminal_id: int) -> str:
    """
    Get the path to the model file for a specific symbol, timeframe, and terminal.
    This is a convenience function that calls get_model_path with the terminal_id.
    DEPRECATED: Use utils.path_utils.get_model_path with terminal_id instead.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        str: Path to the model file
    """
    logger.warning("DEPRECATED: get_terminal_model_path() is deprecated. Use utils.path_utils.get_model_path with terminal_id instead.")
    return get_model_path(symbol, timeframe, terminal_id=terminal_id)

def get_standardized_model_paths(
    symbol: str,
    timeframe: str,
    terminal_ids: Optional[List[int]] = None
) -> Dict[int, Dict[str, str]]:
    """
    Get standardized model paths for all specified terminals.
    This function returns a dictionary mapping terminal IDs to dictionaries containing
    model paths, metrics paths, and visualization paths.

    The function follows the terminal-model mapping defined in CentralConfig:
    - Terminal 1: ARIMA
    - Terminal 2: LSTM
    - Terminal 3: TFT
    - Terminal 4: LSTM+ARIMA ensemble
    - Terminal 5: TFT+ARIMA ensemble

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_ids: List of terminal IDs to get paths for (default: all terminals)

    Returns:
        Dict[int, Dict[str, str]]: Dictionary mapping terminal IDs to dictionaries containing:
            - 'model_path': Path to the model file
            - 'metrics_path': Path to the metrics file
            - 'visualization_path': Path to the visualization file
            - 'model_type': Type of model for this terminal
    """
    # Validate inputs
    if not symbol:
        raise ValueError("Symbol cannot be empty")

    if not timeframe:
        raise ValueError("Timeframe cannot be empty")

    # Validate timeframe format
    valid_timeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']
    if timeframe not in valid_timeframes:
        logger.warning(f"Timeframe {timeframe} is not in standard format {valid_timeframes}")

    # Use all terminals if none specified
    if terminal_ids is None:
        terminal_ids = get_all_terminal_ids()

    # Create result dictionary
    result = {}

    # Get paths for each terminal
    for terminal_id in terminal_ids:
        # Validate terminal_id
        if not isinstance(terminal_id, int) or terminal_id < 1 or terminal_id > 5:
            logger.warning(f"Terminal ID {terminal_id} is not in the valid range (1-5)")
            continue

        # Get model type for this terminal
        model_type = get_terminal_model_type(terminal_id)

        # Get model path
        model_path = get_model_path(symbol, timeframe, model_type)

        # Get metrics path
        metrics_path = get_metrics_path(symbol, timeframe, model_type)

        # Get visualization path
        visualization_path = get_visualization_path(symbol, timeframe, model_type, "predictions")

        # Add to result
        result[terminal_id] = {
            'model_path': model_path,
            'metrics_path': metrics_path,
            'visualization_path': visualization_path,
            'model_type': model_type
        }

        # For ensemble models, also include component model paths
        if is_ensemble_model(model_type):
            component_models = get_model_components(model_type)
            component_paths = {}

            for component_model in component_models:
                component_path = get_model_path(symbol, timeframe, component_model)
                component_metrics_path = get_metrics_path(symbol, timeframe, component_model)
                component_paths[component_model] = {
                    'model_path': component_path,
                    'metrics_path': component_metrics_path
                }

            result[terminal_id]['component_models'] = component_paths

    return result

def ensure_model_directories(symbol: str, timeframe: str, terminal_ids: Optional[List[int]] = None) -> bool:
    """
    Ensure that all model directories for the specified terminals exist.
    This function creates the necessary directory structure for model storage.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_ids: List of terminal IDs to create directories for (default: all terminals)

    Returns:
        bool: True if all directories were created successfully, False otherwise
    """
    try:
        # Get standardized model paths
        model_paths = get_standardized_model_paths(symbol, timeframe, terminal_ids)

        # Create directories for each terminal
        for terminal_id, paths in model_paths.items():
            # Create directory for model path
            model_dir = os.path.dirname(paths['model_path'])
            os.makedirs(model_dir, exist_ok=True)

            # Create directory for visualization path
            viz_dir = os.path.dirname(paths['visualization_path'])
            os.makedirs(viz_dir, exist_ok=True)

            # For ensemble models, also create directories for component models
            if 'component_models' in paths:
                for component_model, component_paths in paths['component_models'].items():
                    component_dir = os.path.dirname(component_paths['model_path'])
                    os.makedirs(component_dir, exist_ok=True)

        logger.info(f"Created model directories for {symbol} ({timeframe})")
        return True

    except Exception as e:
        logger.error(f"Error creating model directories: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_model_exists(symbol: str, timeframe: str, terminal_id: int) -> bool:
    """
    Check if a model exists for the specified terminal, symbol, and timeframe.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        bool: True if the model exists, False otherwise
    """
    try:
        # Get model type for this terminal
        model_type = get_terminal_model_type(terminal_id)

        # Get model path
        model_path = get_model_path(symbol, timeframe, model_type)

        # Check if model file exists
        exists = os.path.exists(model_path)

        if exists:
            logger.info(f"Model exists for Terminal {terminal_id} ({model_type}): {symbol} ({timeframe})")
        else:
            logger.info(f"Model does not exist for Terminal {terminal_id} ({model_type}): {symbol} ({timeframe})")

        return exists

    except Exception as e:
        logger.error(f"Error checking if model exists: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def get_model_name(symbol: str, timeframe: str, terminal_id: int) -> str:
    """
    Get the standardized model name for a specific terminal, symbol, and timeframe.
    This function follows the naming convention used in the model manager.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        str: Standardized model name
    """
    # Get model type for this terminal
    model_type = get_terminal_model_type(terminal_id)

    # Create standardized model name
    model_name = f"{symbol}_{timeframe}_{model_type}"

    return model_name

def get_component_model_names(symbol: str, timeframe: str, terminal_id: int) -> List[str]:
    """
    Get the standardized component model names for an ensemble model.
    This function is useful for ensemble models (terminals 4 and 5).

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        List[str]: List of component model names, or empty list if not an ensemble model
    """
    # Get model type for this terminal
    model_type = get_terminal_model_type(terminal_id)

    # Check if this is an ensemble model
    if not is_ensemble_model(model_type):
        return []

    # Get component model types
    component_types = get_model_components(model_type)

    # Create standardized component model names
    component_names = [f"{symbol}_{timeframe}_{component_type}" for component_type in component_types]

    return component_names

def get_all_model_paths_for_symbol(symbol: str, timeframe: str) -> Dict[str, Dict[str, str]]:
    """
    Get all model paths for a specific symbol and timeframe across all model types.
    This function is useful for getting a complete overview of all models for a symbol.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')

    Returns:
        Dict[str, Dict[str, str]]: Dictionary mapping model types to dictionaries containing:
            - 'model_path': Path to the model file
            - 'metrics_path': Path to the metrics file
            - 'visualization_path': Path to the visualization file
    """
    # Get all model types
    model_types = get_all_model_types()

    # Create result dictionary
    result = {}

    # Get paths for each model type
    for model_type in model_types:
        # Get model path
        model_path = get_model_path(symbol, timeframe, model_type)

        # Get metrics path
        metrics_path = get_metrics_path(symbol, timeframe, model_type)

        # Get visualization path
        visualization_path = get_visualization_path(symbol, timeframe, model_type, "predictions")

        # Add to result
        result[model_type] = {
            'model_path': model_path,
            'metrics_path': metrics_path,
            'visualization_path': visualization_path
        }

        # For ensemble models, also include component model paths
        if is_ensemble_model(model_type):
            component_models = get_model_components(model_type)
            component_paths = {}

            for component_model in component_models:
                component_path = get_model_path(symbol, timeframe, component_model)
                component_metrics_path = get_metrics_path(symbol, timeframe, component_model)
                component_paths[component_model] = {
                    'model_path': component_path,
                    'metrics_path': component_metrics_path
                }

            result[model_type]['component_models'] = component_paths

    return result

def get_model_status_for_all_terminals(symbol: str, timeframe: str) -> Dict[int, Dict[str, any]]:
    """
    Get the model status for all terminals for a specific symbol and timeframe.
    This function checks if models exist and returns their paths and status.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')

    Returns:
        Dict[int, Dict[str, any]]: Dictionary mapping terminal IDs to dictionaries containing:
            - 'model_type': Type of model for this terminal
            - 'model_name': Standardized model name
            - 'model_path': Path to the model file
            - 'exists': Whether the model exists
            - 'component_models': For ensemble models, a dictionary mapping component model types to their status
    """
    # Get all terminal IDs
    terminal_ids = get_all_terminal_ids()

    # Create result dictionary
    result = {}

    # Get status for each terminal
    for terminal_id in terminal_ids:
        # Get model type for this terminal
        model_type = get_terminal_model_type(terminal_id)

        # Get model name
        model_name = get_model_name(symbol, timeframe, terminal_id)

        # Get model path
        model_path = get_model_path(symbol, timeframe, model_type)

        # Check if model exists
        exists = os.path.exists(model_path)

        # Create status dictionary
        status = {
            'model_type': model_type,
            'model_name': model_name,
            'model_path': model_path,
            'exists': exists
        }

        # For ensemble models, also check component models
        if is_ensemble_model(model_type):
            component_models = get_model_components(model_type)
            component_status = {}

            for component_model in component_models:
                component_path = get_model_path(symbol, timeframe, component_model)
                component_exists = os.path.exists(component_path)
                component_status[component_model] = {
                    'model_path': component_path,
                    'exists': component_exists
                }

            status['component_models'] = component_status

        # Add to result
        result[terminal_id] = status

    return result

def get_model_metrics(symbol: str, timeframe: str, terminal_id: int) -> Dict[str, any]:
    """
    Get the metrics for a model for a specific terminal, symbol, and timeframe.
    This function loads the metrics from the metrics file if it exists.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        Dict[str, any]: Dictionary containing the metrics, or empty dict if metrics file doesn't exist
    """
    try:
        # Get model type for this terminal
        model_type = get_terminal_model_type(terminal_id)

        # Get metrics path
        metrics_path = get_metrics_path(symbol, timeframe, model_type)

        # Check if metrics file exists
        if not os.path.exists(metrics_path):
            logger.info(f"Metrics file does not exist for Terminal {terminal_id} ({model_type}): {symbol} ({timeframe})")
            return {}

        # Load metrics from file
        import json
        with open(metrics_path, 'r') as f:
            metrics = json.load(f)

        logger.info(f"Loaded metrics for Terminal {terminal_id} ({model_type}): {symbol} ({timeframe})")
        return metrics

    except Exception as e:
        logger.error(f"Error loading metrics: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def get_all_model_metrics(symbol: str, timeframe: str) -> Dict[int, Dict[str, any]]:
    """
    Get the metrics for all models for a specific symbol and timeframe.
    This function loads the metrics from the metrics files if they exist.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')

    Returns:
        Dict[int, Dict[str, any]]: Dictionary mapping terminal IDs to dictionaries containing the metrics
    """
    # Get all terminal IDs
    terminal_ids = get_all_terminal_ids()

    # Create result dictionary
    result = {}

    # Get metrics for each terminal
    for terminal_id in terminal_ids:
        metrics = get_model_metrics(symbol, timeframe, terminal_id)
        result[terminal_id] = metrics

    return result

def get_best_model(symbol: str, timeframe: str, metric: str = 'direction_accuracy') -> Tuple[int, Dict[str, any]]:
    """
    Get the best model for a specific symbol and timeframe based on a metric.
    This function compares all models and returns the one with the best metric value.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        metric: Metric to use for comparison (default: 'direction_accuracy')

    Returns:
        Tuple[int, Dict[str, any]]: Tuple containing the terminal ID and metrics of the best model
    """
    try:
        # Get metrics for all models
        all_metrics = get_all_model_metrics(symbol, timeframe)

        # Filter out models with no metrics
        valid_metrics = {terminal_id: metrics for terminal_id, metrics in all_metrics.items() if metrics}

        if not valid_metrics:
            logger.warning(f"No models with metrics found for {symbol} ({timeframe})")
            return None, {}

        # Find the best model based on the metric
        if metric in ['mse', 'rmse', 'mae']:
            # Lower is better for these metrics
            best_terminal_id = min(valid_metrics.keys(), key=lambda x: valid_metrics[x].get(metric, float('inf')))
        else:
            # Higher is better for other metrics (e.g., direction_accuracy)
            best_terminal_id = max(valid_metrics.keys(), key=lambda x: valid_metrics[x].get(metric, 0))

        best_metrics = valid_metrics[best_terminal_id]

        logger.info(f"Best model for {symbol} ({timeframe}) based on {metric}: Terminal {best_terminal_id} ({get_terminal_model_type(best_terminal_id)})")
        return best_terminal_id, best_metrics

    except Exception as e:
        logger.error(f"Error finding best model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None, {}

def get_model_comparison(symbol: str, timeframe: str) -> Dict[str, Dict[int, float]]:
    """
    Get a comparison of all models for a specific symbol and timeframe.
    This function compares all models based on common metrics.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')

    Returns:
        Dict[str, Dict[int, float]]: Dictionary mapping metrics to dictionaries mapping terminal IDs to metric values
    """
    try:
        # Get metrics for all models
        all_metrics = get_all_model_metrics(symbol, timeframe)

        # Filter out models with no metrics
        valid_metrics = {terminal_id: metrics for terminal_id, metrics in all_metrics.items() if metrics}

        if not valid_metrics:
            logger.warning(f"No models with metrics found for {symbol} ({timeframe})")
            return {}

        # Common metrics to compare
        common_metrics = ['mse', 'rmse', 'mae', 'direction_accuracy']

        # Create comparison dictionary
        comparison = {}

        for metric in common_metrics:
            comparison[metric] = {}
            for terminal_id, metrics in valid_metrics.items():
                if metric in metrics:
                    comparison[metric][terminal_id] = metrics[metric]

        return comparison

    except Exception as e:
        logger.error(f"Error comparing models: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def get_model_visualization_paths(symbol: str, timeframe: str, terminal_id: int) -> Dict[str, str]:
    """
    Get the visualization paths for a model for a specific terminal, symbol, and timeframe.
    This function returns paths to different types of visualizations.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        Dict[str, str]: Dictionary mapping visualization types to paths
    """
    try:
        # Get model type for this terminal
        model_type = get_terminal_model_type(terminal_id)

        # Visualization types
        viz_types = ['predictions', 'feature_importance', 'residuals', 'component_contributions', 'metrics']

        # Create result dictionary
        result = {}

        # Get paths for each visualization type
        for viz_type in viz_types:
            viz_path = get_visualization_path(symbol, timeframe, model_type, viz_type)
            result[viz_type] = viz_path

        return result

    except Exception as e:
        logger.error(f"Error getting visualization paths: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def check_visualization_exists(symbol: str, timeframe: str, terminal_id: int, viz_type: str = 'predictions') -> bool:
    """
    Check if a visualization exists for a specific terminal, symbol, timeframe, and visualization type.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)
        viz_type: Visualization type (default: 'predictions')

    Returns:
        bool: True if the visualization exists, False otherwise
    """
    try:
        # Get model type for this terminal
        model_type = get_terminal_model_type(terminal_id)

        # Get visualization path
        viz_path = get_visualization_path(symbol, timeframe, model_type, viz_type)

        # Check if visualization file exists
        exists = os.path.exists(viz_path)

        if exists:
            logger.info(f"Visualization exists for Terminal {terminal_id} ({model_type}): {symbol} ({timeframe}) - {viz_type}")
        else:
            logger.info(f"Visualization does not exist for Terminal {terminal_id} ({model_type}): {symbol} ({timeframe}) - {viz_type}")

        return exists

    except Exception as e:
        logger.error(f"Error checking if visualization exists: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def get_model_summary(symbol: str, timeframe: str) -> Dict[int, Dict[str, any]]:
    """
    Get a comprehensive summary of all models for a specific symbol and timeframe.
    This function combines status, metrics, and visualization information.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')

    Returns:
        Dict[int, Dict[str, any]]: Dictionary mapping terminal IDs to dictionaries containing:
            - 'model_type': Type of model for this terminal
            - 'model_name': Standardized model name
            - 'model_path': Path to the model file
            - 'exists': Whether the model exists
            - 'metrics': Dictionary of metrics (if available)
            - 'visualizations': Dictionary of visualization paths
            - 'component_models': For ensemble models, a dictionary with component model information
    """
    try:
        # Get model status for all terminals
        status = get_model_status_for_all_terminals(symbol, timeframe)

        # Get metrics for all terminals
        metrics = get_all_model_metrics(symbol, timeframe)

        # Create result dictionary
        result = {}

        # Combine information for each terminal
        for terminal_id, terminal_status in status.items():
            # Get visualization paths
            viz_paths = get_model_visualization_paths(symbol, timeframe, terminal_id)

            # Check if visualizations exist
            viz_exists = {}
            for viz_type, viz_path in viz_paths.items():
                viz_exists[viz_type] = os.path.exists(viz_path)

            # Create summary dictionary
            summary = {
                'model_type': terminal_status['model_type'],
                'model_name': terminal_status['model_name'],
                'model_path': terminal_status['model_path'],
                'exists': terminal_status['exists'],
                'metrics': metrics.get(terminal_id, {}),
                'visualizations': {
                    'paths': viz_paths,
                    'exists': viz_exists
                }
            }

            # For ensemble models, also include component model information
            if 'component_models' in terminal_status:
                component_summary = {}
                for component_model, component_status in terminal_status['component_models'].items():
                    component_summary[component_model] = {
                        'model_path': component_status['model_path'],
                        'exists': component_status['exists']
                    }

                summary['component_models'] = component_summary

            # Add to result
            result[terminal_id] = summary

        return result

    except Exception as e:
        logger.error(f"Error getting model summary: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def get_terminal_model_summary(symbol: str, timeframe: str, terminal_id: int) -> Dict[str, any]:
    """
    Get a comprehensive summary of a model for a specific terminal, symbol, and timeframe.
    This function combines status, metrics, and visualization information for a single terminal.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
        terminal_id: Terminal ID (1-5)

    Returns:
        Dict[str, any]: Dictionary containing:
            - 'model_type': Type of model for this terminal
            - 'model_name': Standardized model name
            - 'model_path': Path to the model file
            - 'exists': Whether the model exists
            - 'metrics': Dictionary of metrics (if available)
            - 'visualizations': Dictionary of visualization paths
            - 'component_models': For ensemble models, a dictionary with component model information
    """
    try:
        # Get all model summaries
        all_summaries = get_model_summary(symbol, timeframe)

        # Return summary for the specified terminal
        if terminal_id in all_summaries:
            return all_summaries[terminal_id]
        else:
            logger.warning(f"No model summary found for Terminal {terminal_id}: {symbol} ({timeframe})")
            return {}

    except Exception as e:
        logger.error(f"Error getting terminal model summary: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def get_symbol_model_status(symbol: str, timeframes: List[str] = None) -> Dict[str, Dict[int, Dict[str, bool]]]:
    """
    Get the model status for a specific symbol across all timeframes.
    This function checks if models exist for each terminal and timeframe.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSD.a')
        timeframes: List of timeframes to check (default: all standard timeframes)

    Returns:
        Dict[str, Dict[int, Dict[str, bool]]]: Dictionary mapping timeframes to dictionaries mapping terminal IDs to dictionaries containing:
            - 'exists': Whether the model exists
            - 'has_metrics': Whether the model has metrics
            - 'has_visualizations': Whether the model has visualizations
    """
    try:
        # Use all standard timeframes if none specified
        if timeframes is None:
            timeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']

        # Get all terminal IDs
        terminal_ids = get_all_terminal_ids()

        # Create result dictionary
        result = {}

        # Check status for each timeframe
        for timeframe in timeframes:
            timeframe_status = {}

            # Check status for each terminal
            for terminal_id in terminal_ids:
                # Get model type for this terminal
                model_type = get_terminal_model_type(terminal_id)

                # Get model path
                model_path = get_model_path(symbol, timeframe, model_type)

                # Get metrics path
                metrics_path = get_metrics_path(symbol, timeframe, model_type)

                # Get visualization path
                viz_path = get_visualization_path(symbol, timeframe, model_type, "predictions")

                # Check if files exist
                model_exists = os.path.exists(model_path)
                metrics_exist = os.path.exists(metrics_path)
                viz_exists = os.path.exists(viz_path)

                # Add to result
                timeframe_status[terminal_id] = {
                    'exists': model_exists,
                    'has_metrics': metrics_exist,
                    'has_visualizations': viz_exists
                }

            # Add to result
            result[timeframe] = timeframe_status

        return result

    except Exception as e:
        logger.error(f"Error getting symbol model status: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}
