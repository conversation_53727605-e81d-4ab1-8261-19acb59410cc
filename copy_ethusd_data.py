#!/usr/bin/env python3
"""
Copy ETHUSD.a data from historical directory to storage structure.
"""

import os
import shutil
import pandas as pd
import json
from pathlib import Path

def copy_ethusd_data():
    """Copy ETHUSD.a data to storage structure."""
    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
    
    for timeframe in timeframes:
        print(f"\nProcessing {timeframe} data...")
        
        # Check what terminal files exist
        historical_dir = f'data/historical/{timeframe}/ETHUSD.a'
        if not os.path.exists(historical_dir):
            print(f"No historical directory found: {historical_dir}")
            continue
            
        files = os.listdir(historical_dir)
        terminal_files = [f for f in files if f.startswith('terminal_') and f.endswith('.parquet')]
        
        print(f"Found terminal files: {terminal_files}")
        
        for terminal_file in terminal_files:
            # Extract terminal ID
            terminal_id = terminal_file.split('_')[1].split('.')[0]
            
            # Source and destination paths
            src_path = os.path.join(historical_dir, terminal_file)
            dest_dir = f'data/storage/historical/terminal_{terminal_id}/ETHUSD.a_{timeframe}'
            dest_path = os.path.join(dest_dir, 'latest.parquet')
            
            # Create destination directory
            os.makedirs(dest_dir, exist_ok=True)
            
            # Copy file
            shutil.copy2(src_path, dest_path)
            print(f"Copied {src_path} -> {dest_path}")
            
            # Create metadata
            try:
                df = pd.read_parquet(src_path)
                metadata = {
                    'symbol': 'ETHUSD.a',
                    'timeframe': timeframe,
                    'terminal_id': int(terminal_id),
                    'rows': len(df),
                    'columns': list(df.columns),
                    'date_range': f'{df.index.min()} to {df.index.max()}',
                    'source': 'historical_directory',
                    'created_at': pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
                }
                
                metadata_path = os.path.join(dest_dir, 'latest_metadata.json')
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f, indent=2, default=str)
                print(f"Created metadata: {metadata_path}")
                
            except Exception as e:
                print(f"Error creating metadata for {src_path}: {e}")

if __name__ == "__main__":
    copy_ethusd_data()
    print("\nETHUSD.a data copy completed!")
