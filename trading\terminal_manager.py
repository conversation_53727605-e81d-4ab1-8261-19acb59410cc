"""
Terminal Manager Module.
This module manages the multi-timeframe terminal strategy.
"""
import logging
import time
from typing import Dict, List
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from config.credentials import MT5_TERMINALS
from config.trading_config import TRADING_CONFIG
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_manager import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator

# Configure logger
logger = logging.getLogger('trading_bot.terminal_manager')

class TerminalManager:
    """
    Terminal Manager class for managing the multi-timeframe terminal strategy.
    """
    def __init__(
        self,
        mt5_connector: MT5Connector,
        model_manager: ModelManager,
        data_collector: DataCollector,
        feature_engineer: FeatureEngineer,
        model_evaluator: ModelEvaluator
    ):
        """
        Initialize TerminalManager.

        Args:
            mt5_connector: MT5Connector instance
            model_manager: ModelManager instance
            data_collector: DataCollector instance
            feature_engineer: FeatureEngineer instance
            model_evaluator: ModelEvaluator instance
        """
        self.mt5 = mt5_connector
        self.model_manager = model_manager
        self.data_collector = data_collector
        self.feature_engineer = feature_engineer
        self.model_evaluator = model_evaluator

        # Initialize terminal-specific order managers
        self.order_managers = {}
        self.active_terminals = {}
        self.symbol = TRADING_CONFIG['symbol']

        # Initialize terminal configurations
        self._initialize_terminals()

    def _initialize_terminals(self):
        """Initialize terminal configurations."""
        logger.info("Initializing terminal configurations...")

        for terminal_id, terminal_config in MT5_TERMINALS.items():
            timeframe = terminal_config.get('timeframe', 'M5')
            risk_profile = terminal_config.get('risk_profile', 'Moderate')

            logger.info(f"Terminal {terminal_id} ({terminal_config['name']}): {timeframe} timeframe, {risk_profile} risk profile")

            # Create order manager for this terminal
            self.order_managers[terminal_id] = OrderManager(self.mt5)

            # Store terminal info
            self.active_terminals[terminal_id] = {
                'terminal_id': terminal_id,
                'name': terminal_config['name'],
                'timeframe': timeframe,
                'risk_profile': risk_profile,
                'last_signal': 0.0,
                'last_trade_time': None,
                'active': False
            }

        # Auto-connect to all terminals after initialization
        self._auto_connect_terminals()

    def _auto_connect_terminals(self):
        """Automatically connect to all configured terminals."""
        logger.info("Auto-connecting to all configured terminals...")

        connected_count = 0
        for terminal_id in MT5_TERMINALS.keys():
            try:
                if self.connect_terminal(terminal_id):
                    connected_count += 1
                    logger.info(f"Successfully auto-connected to Terminal {terminal_id}")
                else:
                    logger.warning(f"Failed to auto-connect to Terminal {terminal_id}")
            except Exception as e:
                logger.error(f"Error auto-connecting to Terminal {terminal_id}: {str(e)}")

        logger.info(f"Auto-connection complete: {connected_count}/{len(MT5_TERMINALS)} terminals connected")

    def connect_terminal(self, terminal_id: int) -> bool:
        """
        Connect to a specific terminal.

        Args:
            terminal_id: Terminal ID to connect to

        Returns:
            bool: True if connection is successful, False otherwise
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal ID {terminal_id} not found in configuration")
            return False

        # Connect to terminal
        if not self.mt5.connect_terminal(terminal_id):
            logger.error(f"Failed to connect to terminal {terminal_id}")
            return False

        # Update terminal status
        self.active_terminals[terminal_id]['active'] = True
        logger.info(f"Connected to terminal {terminal_id} ({MT5_TERMINALS[terminal_id]['name']})")

        return True

    def get_terminal_timeframe(self, terminal_id: int) -> str:
        """
        Get the timeframe for a specific terminal.

        Args:
            terminal_id: Terminal ID

        Returns:
            str: Timeframe for the terminal
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return "M5"  # Default to M5

        return self.active_terminals[terminal_id]['timeframe']

    def get_terminal_risk_profile(self, terminal_id: int) -> Dict:
        """
        Get the risk profile for a specific terminal.

        Args:
            terminal_id: Terminal ID

        Returns:
            Dict: Risk profile parameters
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal ID {terminal_id} not found in configuration")
            return {}

        terminal_config = MT5_TERMINALS[terminal_id]

        # Get risk profile parameters
        risk_profile = {
            'position_size_factor': terminal_config.get('position_size_factor', 1.0),
            'sl_multiplier': terminal_config.get('sl_multiplier', 1.5),
            'tp_multiplier': terminal_config.get('tp_multiplier', 3.0)
        }

        return risk_profile

    def get_terminal_signal_thresholds(self, terminal_id: int) -> Dict:
        """
        Get the signal thresholds for a specific terminal.

        Args:
            terminal_id: Terminal ID

        Returns:
            Dict: Signal threshold parameters
        """
        if terminal_id not in TRADING_CONFIG.get('terminal_config', {}):
            logger.error(f"Terminal ID {terminal_id} not found in terminal_config")
            return {
                'signal_threshold': 0.3,
                'signal_reversal': 0.7
            }

        terminal_config = TRADING_CONFIG['terminal_config'][terminal_id]

        # Get signal threshold parameters
        thresholds = {
            'signal_threshold': terminal_config.get('signal_threshold', 0.3),
            'signal_reversal': terminal_config.get('signal_reversal', 0.7)
        }

        return thresholds

    def get_active_terminals(self) -> List[int]:
        """
        Get the list of active terminals.

        Returns:
            List[int]: List of active terminal IDs
        """
        return [terminal_id for terminal_id, terminal in self.active_terminals.items() if terminal['active']]

    def get_terminal_status(self, terminal_id: int) -> Dict:
        """
        Get the status of a specific terminal.

        Args:
            terminal_id: Terminal ID

        Returns:
            Dict: Terminal status
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return {}

        return self.active_terminals[terminal_id]

    def generate_timeframe_signal_for_timeframe(self, timeframe: str, terminal_id: int = 2) -> float:
        """
        Generate a trading signal for a specific timeframe.
        By default, uses the LSTM model (Terminal 2) for predictions.

        Args:
            timeframe: Timeframe (e.g., "M5", "M15", "M30", "H1")
            terminal_id: Terminal ID to determine which model type to use (default: 2 for LSTM)

        Returns:
            float: Trading signal (0-1 where <0.5 is sell, >0.5 is buy)
        """
        try:
            # Get model type based on terminal ID
            model_types = {
                1: 'arima',
                2: 'lstm',
                3: 'tft',
                4: 'lstm_arima',
                5: 'tft_arima'
            }

            model_type = model_types.get(terminal_id, 'lstm')  # Default to LSTM if terminal ID not found

            # Get model name with appropriate suffix
            model_name = f"{self.symbol}_{timeframe}_{model_type}"

            # Check if model exists
            if model_name not in self.model_manager.list_models():
                logger.warning(f"Model {model_name} not found, cannot generate signal")
                return 0.5

            # Get latest data for the timeframe
            df = self.data_collector.get_latest_data(
                timeframe=timeframe,
                num_bars=TRADING_CONFIG['model']['sequence_length'] + 100  # Get enough bars for feature creation
            )

            if df is None or len(df) == 0:
                logger.error(f"Failed to get data for {self.symbol} ({timeframe})")
                return 0.5

            # Create features
            df_features = self.feature_engineer.create_features(df)

            # Normalize features
            df_norm = self.feature_engineer.normalize_features(df_features)

            # Get the latest sequence
            feature_columns = [col for col in df_norm.columns if col != 'returns']
            sequence_length = TRADING_CONFIG['model']['sequence_length']
            latest_sequence = df_norm[feature_columns].iloc[-sequence_length:].values
            latest_sequence = np.expand_dims(latest_sequence, axis=0)  # Add batch dimension

            # Make prediction
            prediction = self.model_manager.predict(
                model_name=model_name,
                X=latest_sequence
            )

            if prediction is None:
                logger.error(f"Failed to make prediction for {timeframe}")
                return 0.5

            # Convert prediction to signal (0-1)
            # Normalize to 0-1 range where 0.5 is neutral
            signal = self.prediction_to_signal(prediction[0][0])

            logger.info(f"Signal for {self.symbol} ({timeframe}): {signal:.6f}")
            return signal

        except Exception as e:
            logger.error(f"Error generating signal for {timeframe}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return 0.5

    def prediction_to_signal(self, prediction: float) -> float:
        """
        Convert a raw prediction to a signal value between 0 and 1.

        Args:
            prediction: Raw prediction from the model

        Returns:
            float: Signal value between 0 and 1 (0.5 is neutral)
        """
        # Sigmoid function to convert any value to 0-1 range
        import math
        signal = 1 / (1 + math.exp(-prediction * 5))  # Scale by 5 to make the sigmoid steeper
        return signal

    def generate_timeframe_signal(self, terminal_id: int) -> float:
        """
        Generate a trading signal for a specific terminal's timeframe.
        Uses the terminal-model mapping:
        - Terminal 1: ARIMA
        - Terminal 2: LSTM
        - Terminal 3: TFT
        - Terminal 4: LSTM + ARIMA ensemble
        - Terminal 5: TFT + ARIMA ensemble

        Args:
            terminal_id: Terminal ID

        Returns:
            float: Trading signal (-1.0 to 1.0)
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return 0.0

        # Get terminal timeframe
        timeframe = self.get_terminal_timeframe(terminal_id)

        # Skip multi-timeframe terminal (handled separately)
        if timeframe == "MULTI":
            logger.info(f"Terminal {terminal_id} is a multi-timeframe terminal, skipping direct signal generation")
            return 0.0

        # Get model type based on terminal ID
        model_types = {
            1: 'arima',
            2: 'lstm',
            3: 'tft',
            4: 'lstm_arima',
            5: 'tft_arima'
        }

        model_type = model_types.get(terminal_id, 'lstm')  # Default to LSTM if terminal ID not found
        logger.info(f"Using model type '{model_type}' for Terminal {terminal_id}")

        # Get model name with appropriate suffix
        model_name = f"{self.symbol}_{timeframe}_{model_type}"

        # Check if model exists
        if model_name not in self.model_manager.list_models():
            logger.warning(f"Model {model_name} not found, cannot generate signal")
            return 0.0

        try:
            # Get latest data
            df = self.data_collector.get_latest_data(
                timeframe=timeframe,
                num_bars=TRADING_CONFIG['model']['sequence_length'] + 100  # Get extra bars for feature engineering
            )

            if df is None or len(df) == 0:
                logger.error(f"Failed to get data for {self.symbol} ({timeframe})")
                return 0.0

            # Create features
            df_features = self.feature_engineer.create_features(df)

            # Normalize features
            df_norm = self.feature_engineer.normalize_features(df_features)

            # Get the latest sequence
            feature_columns = [col for col in df_norm.columns if col != 'returns']
            sequence_length = TRADING_CONFIG['model']['sequence_length']
            latest_sequence = df_norm[feature_columns].iloc[-sequence_length:].values
            latest_sequence = np.expand_dims(latest_sequence, axis=0)  # Add batch dimension

            # Generate prediction
            prediction = self.model_manager.predict(
                model_name=model_name,
                X=latest_sequence
            )

            # Get signal value
            signal_value = float(prediction[0][0])

            # Update terminal signal
            self.active_terminals[terminal_id]['last_signal'] = signal_value

            logger.info(f"Signal for {self.symbol} ({timeframe}) on Terminal {terminal_id}: {signal_value:.6f}")

            return signal_value

        except Exception as e:
            logger.error(f"Error generating signal for Terminal {terminal_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return 0.0

    def generate_multi_timeframe_signal(self, terminal_id: int = 5) -> float:
        """
        Generate a trading signal for the multi-timeframe terminal by combining signals from all timeframes.

        Args:
            terminal_id: Terminal ID (default is 5 for the multi-timeframe terminal)

        Returns:
            float: Combined trading signal (0-1 where <0.5 is sell, >0.5 is buy)
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return 0.5

        # Check if this is the multi-timeframe terminal
        if self.active_terminals[terminal_id]['timeframe'] != "MULTI":
            logger.warning(f"Terminal {terminal_id} is not a multi-timeframe terminal")
            return 0.5

        # Get timeframe weights
        if terminal_id not in TRADING_CONFIG.get('terminal_config', {}) or 'timeframe_weights' not in TRADING_CONFIG['terminal_config'][terminal_id]:
            logger.warning(f"No timeframe weights found for Terminal {terminal_id}, using default weights")
            weights = {
                'M5': 0.3,
                'M15': 0.25,
                'M30': 0.25,
                'H1': 0.2
            }
        else:
            weights = TRADING_CONFIG['terminal_config'][terminal_id]['timeframe_weights']

        try:
            # Generate signals for all timeframes using the TFT+ARIMA ensemble model (Terminal 5)
            # This is appropriate for the multi-timeframe terminal
            m5_signal = self.generate_timeframe_signal_for_timeframe("M5", terminal_id=5)  # Use TFT+ARIMA ensemble
            m15_signal = self.generate_timeframe_signal_for_timeframe("M15", terminal_id=5)
            m30_signal = self.generate_timeframe_signal_for_timeframe("M30", terminal_id=5)
            h1_signal = self.generate_timeframe_signal_for_timeframe("H1", terminal_id=5)

            # Store signals in dictionary
            signals = {
                'M5': m5_signal,
                'M15': m15_signal,
                'M30': m30_signal,
                'H1': h1_signal
            }

            # Calculate weighted average
            weighted_sum = 0.0
            total_weight = 0.0

            for tf, signal in signals.items():
                if tf in weights:
                    weighted_sum += signal * weights[tf]
                    total_weight += weights[tf]

            # Normalize by total weight used
            if total_weight > 0:
                combined_signal = weighted_sum / total_weight
            else:
                # Fallback to simple average if no weights were applied
                combined_signal = sum(signals.values()) / len(signals)

            # Update terminal signal
            self.active_terminals[terminal_id]['last_signal'] = combined_signal

            logger.info(f"Combined signal for {self.symbol} on Terminal {terminal_id}: {combined_signal:.6f}")
            logger.info(f"Individual signals: M5={m5_signal:.6f}, M15={m15_signal:.6f}, M30={m30_signal:.6f}, H1={h1_signal:.6f}")

            return combined_signal

        except Exception as e:
            logger.error(f"Error generating multi-timeframe signal for Terminal {terminal_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return 0.5

    def execute_terminal_trade(self, terminal_id: int, signal: float = None) -> bool:
        """
        Execute a trade on a specific terminal based on its signal.

        Args:
            terminal_id: Terminal ID
            signal: Trading signal (if None, use the terminal's last signal)

        Returns:
            bool: True if trade was executed, False otherwise
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return False

        # Connect to terminal
        if not self.mt5.connect_terminal(terminal_id):
            logger.error(f"Failed to connect to terminal {terminal_id}")
            return False

        # Get terminal info
        terminal_info = self.active_terminals[terminal_id]
        timeframe = terminal_info['timeframe']

        # Get signal if not provided
        if signal is None:
            if timeframe == "MULTI":
                signal = self.generate_multi_timeframe_signal(terminal_id)
            else:
                signal = self.generate_timeframe_signal(terminal_id)

        # Get signal thresholds
        thresholds = self.get_terminal_signal_thresholds(terminal_id)
        signal_threshold = thresholds['signal_threshold']
        signal_reversal = thresholds['signal_reversal']

        # Get risk profile
        risk_profile = self.get_terminal_risk_profile(terminal_id)

        # Get order manager for this terminal
        order_manager = self.order_managers[terminal_id]

        # Get current positions
        position_status = order_manager.get_position_status()

        # Get ATR value for dynamic SL/TP
        atr_value = None
        try:
            # For MULTI timeframe, use H1 as the base timeframe for ATR calculation
            atr_timeframe = "H1" if timeframe == "MULTI" else timeframe
            df = self.data_collector.get_latest_data(timeframe=atr_timeframe, num_bars=50)
            df_features = self.feature_engineer.create_features(df)
            if 'atr14' in df_features.columns and len(df_features) > 0:
                atr_value = df_features['atr14'].iloc[-1]
            else:
                logger.warning("ATR not available in features, using default value")
                atr_value = 100.0  # Default value
        except Exception as e:
            logger.error(f"Failed to get ATR value: {str(e)}")
            atr_value = 100.0  # Default value

        # Apply risk profile to ATR
        if atr_value is not None:
            atr_value = atr_value * risk_profile.get('position_size_factor', 1.0)

        # Determine action based on signal and current positions
        trade_executed = False

        if signal > signal_threshold and not position_status['has_position']:
            # Open long position
            logger.info(f"Opening LONG position for {self.symbol} on Terminal {terminal_id}")
            logger.info(f"Signal: {signal:.6f}, Threshold: {signal_threshold}")

            order_manager.place_market_order(
                order_type='BUY',
                atr_value=atr_value,
                comment=f"LSTM Signal: {signal:.4f} ({timeframe})"
            )
            trade_executed = True

        elif signal < -signal_threshold and not position_status['has_position']:
            # Open short position
            logger.info(f"Opening SHORT position for {self.symbol} on Terminal {terminal_id}")
            logger.info(f"Signal: {signal:.6f}, Threshold: {-signal_threshold}")

            order_manager.place_market_order(
                order_type='SELL',
                atr_value=atr_value,
                comment=f"LSTM Signal: {signal:.4f} ({timeframe})"
            )
            trade_executed = True

        elif abs(signal) < 0.2 and position_status['has_position']:
            # Close positions if signal is weak
            logger.info(f"Closing positions for {self.symbol} on Terminal {terminal_id} due to weak signal")
            order_manager.close_all_positions()
            trade_executed = True

        elif position_status['has_position']:
            position_type = position_status.get('position_type', '')

            # Check for strong reversal signals
            if position_type == 'BUY' and signal < -signal_reversal:
                logger.info(f"Closing LONG position due to strong reversal signal: {signal:.6f}")
                order_manager.close_all_positions()
                trade_executed = True

            elif position_type == 'SELL' and signal > signal_reversal:
                logger.info(f"Closing SHORT position due to strong reversal signal: {signal:.6f}")
                order_manager.close_all_positions()
                trade_executed = True

        # Update last trade time if trade was executed
        if trade_executed:
            self.active_terminals[terminal_id]['last_trade_time'] = pd.Timestamp.now()

        return trade_executed

    def run_all_terminals(self) -> Dict[int, bool]:
        """
        Run trading cycles for all active terminals.

        Returns:
            Dict[int, bool]: Dictionary of terminal IDs and their success status
        """
        results = {}

        # Get active terminals
        active_terminals = self.get_active_terminals()

        if not active_terminals:
            logger.warning("No active terminals found")
            return results

        logger.info(f"Running trading cycles for {len(active_terminals)} active terminals")

        # Run trading cycles for each terminal
        for terminal_id in active_terminals:
            # Skip multi-timeframe terminal for now (will be run last)
            if self.active_terminals[terminal_id]['timeframe'] == "MULTI":
                continue

            # Run trading cycle
            success = self.run_terminal_trading_cycle(terminal_id)
            results[terminal_id] = success

        # Run multi-timeframe terminal last (after all other signals have been generated)
        for terminal_id in active_terminals:
            if self.active_terminals[terminal_id]['timeframe'] == "MULTI":
                success = self.run_terminal_trading_cycle(terminal_id)
                results[terminal_id] = success

        return results

    def train_all_models(self, start_date: str, end_date: str = None) -> Dict[str, bool]:
        """
        Train models for all timeframes.

        Args:
            start_date: Start date for training data
            end_date: End date for training data

        Returns:
            Dict[str, bool]: Dictionary of timeframes and their success status
        """
        results = {}

        # Get all timeframes
        timeframes = set()
        for terminal_id, terminal_info in self.active_terminals.items():
            timeframe = terminal_info['timeframe']
            if timeframe != "MULTI":  # Skip multi-timeframe terminal
                timeframes.add(timeframe)

        logger.info(f"Training models for {len(timeframes)} timeframes...")

        # Train model for each timeframe
        for timeframe in timeframes:
            # Find terminal ID for this timeframe
            terminal_id = None
            for t_id, terminal_info in self.active_terminals.items():
                if terminal_info['timeframe'] == timeframe:
                    terminal_id = t_id
                    break

            if terminal_id is None:
                logger.warning(f"No terminal found for timeframe {timeframe}, skipping")
                results[timeframe] = False
                continue

            # Train model
            results[timeframe] = self.train_terminal_model(terminal_id, start_date, end_date)

        return results

    def train_terminal_model(self, terminal_id: int, start_date: str, end_date: str = None) -> bool:
        """
        Train a model for a specific terminal.

        Args:
            terminal_id: Terminal ID
            start_date: Start date for training data
            end_date: End date for training data

        Returns:
            bool: True if training was successful, False otherwise
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return False

        # Connect to terminal
        if not self.mt5.connect_terminal(terminal_id):
            logger.error(f"Failed to connect to terminal {terminal_id}")
            return False

        # Get terminal info
        terminal_info = self.active_terminals[terminal_id]
        timeframe = terminal_info['timeframe']

        # Skip multi-timeframe terminal
        if timeframe == "MULTI":
            logger.warning(f"Terminal {terminal_id} is a multi-timeframe terminal, skipping model training")
            return False

        logger.info(f"Training model for Terminal {terminal_id} ({terminal_info['name']}) - {timeframe} timeframe...")

        try:
            # Get historical data
            df = self.data_collector.get_historical_data(
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )

            if df is None or len(df) == 0:
                logger.error(f"Failed to get historical data for {self.symbol} ({timeframe})")
                return False

            logger.info(f"Got {len(df)} bars of historical data for {self.symbol} ({timeframe})")

            # Create features
            df_features = self.feature_engineer.create_features(df)

            # Normalize features
            df_norm = self.feature_engineer.normalize_features(df_features)

            # Prepare data for training
            sequence_length = TRADING_CONFIG['model']['sequence_length']
            train_test_split = TRADING_CONFIG['model']['train_test_split']
            validation_split = TRADING_CONFIG['model']['validation_split']

            X_train, y_train, X_val, y_val, X_test, y_test = self.feature_engineer.prepare_data_for_training(
                df=df_norm,
                sequence_length=sequence_length,
                target_column='returns',
                train_test_split=train_test_split,
                validation_split=validation_split
            )

            # Check if we have enough data
            if len(X_train) == 0:
                logger.error(f"Not enough data for {self.symbol} ({timeframe})")
                return False

            logger.info(f"Training data size: {len(X_train)}")
            logger.info(f"Validation data size: {len(X_val)}")
            logger.info(f"Test data size: {len(X_test)}")

            # Create model
            model_name = f"{self.symbol}_{timeframe}"
            input_size = X_train.shape[2]  # Number of features

            # Create model
            self.model_manager.create_model(
                model_name=model_name,
                input_size=input_size,
                hidden_size=TRADING_CONFIG['model']['hidden_size'],
                num_layers=TRADING_CONFIG['model']['num_layers'],
                output_size=1
            )

            # Train model
            self.model_manager.train_model(
                model_name=model_name,
                X_train=X_train,
                y_train=y_train,
                X_val=X_val,
                y_val=y_val
            )

            # Evaluate model
            metrics = self.model_manager.evaluate_model(
                model_name=model_name,
                X=X_test,
                y=y_test
            )

            logger.info(f"Model {model_name} trained and evaluated:")
            for metric, value in metrics.items():
                logger.info(f"  {metric}: {value:.6f}")

            return True

        except Exception as e:
            logger.error(f"Error training model for Terminal {terminal_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def update_all_models(self) -> dict:
        """
        Update all models with the latest data.

        Returns:
            dict: Dictionary of timeframes and their success status
        """
        results = {}

        # Get all timeframes
        timeframes = set()
        for terminal_id, terminal_info in self.active_terminals.items():
            timeframe = terminal_info['timeframe']
            if timeframe != "MULTI":  # Skip multi-timeframe terminal
                timeframes.add(timeframe)

        logger.info(f"Updating models for {len(timeframes)} timeframes...")

        # Get the last 2 years of data for updating (730 days)
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=730)).strftime("%Y-%m-%d")

        # Update model for each timeframe
        for timeframe in timeframes:
            # Find terminal ID for this timeframe
            terminal_id = None
            for t_id, terminal_info in self.active_terminals.items():
                if terminal_info['timeframe'] == timeframe:
                    terminal_id = t_id
                    break

            if terminal_id is None:
                logger.warning(f"No terminal found for timeframe {timeframe}, skipping")
                results[timeframe] = False
                continue

            # Update model
            results[timeframe] = self.train_terminal_model(terminal_id, start_date, end_date)

        return results

    def run_terminal_trading_cycle(self, terminal_id: int) -> bool:
        """
        Run a complete trading cycle for a specific terminal.

        Args:
            terminal_id: Terminal ID

        Returns:
            bool: True if cycle completed successfully, False otherwise
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return False

        try:
            # Connect to terminal
            if not self.mt5.connect_terminal(terminal_id):
                logger.error(f"Failed to connect to terminal {terminal_id}")
                return False

            # Get terminal info
            terminal_info = self.active_terminals[terminal_id]
            timeframe = terminal_info['timeframe']

            logger.info(f"Running trading cycle for Terminal {terminal_id} ({terminal_info['name']}) - {timeframe} timeframe")

            # Generate signal
            if timeframe == "MULTI":
                signal = self.generate_multi_timeframe_signal(terminal_id)
            else:
                signal = self.generate_timeframe_signal(terminal_id)

            # Execute trade
            self.execute_terminal_trade(terminal_id, signal)

            # Apply trailing stop
            order_manager = self.order_managers[terminal_id]
            order_manager.apply_trailing_stop()

            # Get daily performance
            daily_performance = order_manager.get_daily_performance()
            logger.info(f"Daily performance for Terminal {terminal_id}: {daily_performance}")

            return True

        except Exception as e:
            logger.error(f"Error in trading cycle for Terminal {terminal_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def update_terminal_model(self, terminal_id: int, lookback_days: int = 30) -> bool:
        """
        Update the model for a specific terminal's timeframe with recent data.

        Args:
            terminal_id: Terminal ID
            lookback_days: Number of days to look back for updating

        Returns:
            bool: True if update was successful, False otherwise
        """
        if terminal_id not in self.active_terminals:
            logger.error(f"Terminal ID {terminal_id} not found in active terminals")
            return False

        # Get terminal timeframe
        timeframe = self.get_terminal_timeframe(terminal_id)

        # Skip multi-timeframe terminal (no dedicated model)
        if timeframe == "MULTI":
            logger.info(f"Terminal {terminal_id} is a multi-timeframe terminal, skipping model update")
            return False

        # Get model name
        model_name = f"{self.symbol}_{timeframe}"

        if model_name not in self.model_manager.list_models():
            logger.warning(f"Model {model_name} not found, skipping update")
            return False

        try:
            logger.info(f"Updating model {model_name}...")

            # Get recent data
            df = self.data_collector.get_latest_data(
                timeframe=timeframe,
                num_bars=lookback_days * 24 * 60 // int(timeframe[1:]) if timeframe.startswith('M') else lookback_days * 24  # Approximate number of bars
            )

            if df is None or len(df) == 0:
                logger.error(f"Failed to get data for {self.symbol} ({timeframe})")
                return False

            # Create features
            df_features = self.feature_engineer.create_features(df)

            # Normalize features
            df_norm = self.feature_engineer.normalize_features(df_features)

            # Create sequences
            X, y = self.feature_engineer.create_sequences(
                df=df_norm,
                sequence_length=TRADING_CONFIG['model']['sequence_length'],
                target_column='returns'
            )

            # Update model
            self.model_manager.update_model(
                model_name=model_name,
                X=X,
                y=y
            )

            logger.info(f"Model {model_name} updated with {len(X)} samples")
            return True

        except Exception as e:
            logger.error(f"Error updating model for Terminal {terminal_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def update_all_models(self, lookback_days: int = 30) -> Dict[int, bool]:
        """
        Update models for all terminals with recent data.

        Args:
            lookback_days: Number of days to look back for updating

        Returns:
            Dict[int, bool]: Dictionary of terminal IDs and their update success status
        """
        results = {}

        logger.info(f"Updating models for all terminals with {lookback_days} days of data")

        # Update models for each terminal
        for terminal_id, terminal_info in self.active_terminals.items():
            # Skip multi-timeframe terminal (no dedicated model)
            if terminal_info['timeframe'] == "MULTI":
                continue

            # Update model
            success = self.update_terminal_model(terminal_id, lookback_days)
            results[terminal_id] = success

        return results